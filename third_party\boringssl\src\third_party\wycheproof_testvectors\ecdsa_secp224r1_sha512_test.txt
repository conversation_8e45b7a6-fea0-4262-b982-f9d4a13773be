# Imported from Wych<PERSON><PERSON><PERSON>'s ecdsa_secp224r1_sha512_test.json.
# This file is generated by convert_wycheproof.go. Do not edit by hand.
#
# Algorithm: ECDSA
# Generator version: 0.8r12

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5]
[key.wx = 00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7]
[key.wy = 00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5]
[sha = SHA-512]

# tcId = 1
# signature malleability
msg = 313233343030
result = valid
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c394766fb67a65fe0af6c154f7cbd285ea180b4c6150cdafafb0f6f0f

# tcId = 2
# Legacy:ASN encoding of s misses leading 0
msg = 313233343030
result = acceptable
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021cc6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = MissingZero

# tcId = 3
# valid
msg = 313233343030
result = valid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 4
# long form encoding of length of sequence
msg = 313233343030
result = invalid
sig = 30813d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 5
# length of sequence contains leading 0
msg = 313233343030
result = invalid
sig = 3082003d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 6
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 303e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 7
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 8
# uint32 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 3085010000003d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 9
# uint64 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 308901000000000000003d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 10
# length of sequence = 2**31 - 1
msg = 313233343030
result = invalid
sig = 30847fffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 11
# length of sequence = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3084ffffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 12
# length of sequence = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3085ffffffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 13
# length of sequence = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3088ffffffffffffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 14
# incorrect length of sequence
msg = 313233343030
result = invalid
sig = 30ff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 15
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 16
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 303d0280691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 17
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab028000c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 18
# removing sequence
msg = 313233343030
result = invalid
sig = 

# tcId = 19
# lonely sequence tag
msg = 313233343030
result = invalid
sig = 30

# tcId = 20
# appending 0's to sequence
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 21
# prepending 0's to sequence
msg = 313233343030
result = invalid
sig = 303f0000021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 22
# appending unused 0's to sequence
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 23
# appending null value to sequence
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0500

# tcId = 24
# including garbage
msg = 313233343030
result = invalid
sig = 3042498177303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 25
# including garbage
msg = 313233343030
result = invalid
sig = 30412500303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 26
# including garbage
msg = 313233343030
result = invalid
sig = 303f303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0004deadbeef

# tcId = 27
# including garbage
msg = 313233343030
result = invalid
sig = 30422221498177021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 28
# including garbage
msg = 313233343030
result = invalid
sig = 304122202500021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 29
# including garbage
msg = 313233343030
result = invalid
sig = 3045221e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0004deadbeef021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 30
# including garbage
msg = 313233343030
result = invalid
sig = 3042021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2222498177021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 31
# including garbage
msg = 313233343030
result = invalid
sig = 3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab22212500021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 32
# including garbage
msg = 313233343030
result = invalid
sig = 3045021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab221f021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0004deadbeef

# tcId = 33
# including undefined tags
msg = 313233343030
result = invalid
sig = 3045aa00bb00cd00303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 34
# including undefined tags
msg = 313233343030
result = invalid
sig = 3043aa02aabb303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 35
# including undefined tags
msg = 313233343030
result = invalid
sig = 30452224aa00bb00cd00021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 36
# including undefined tags
msg = 313233343030
result = invalid
sig = 30432222aa02aabb021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 37
# including undefined tags
msg = 313233343030
result = invalid
sig = 3045021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2225aa00bb00cd00021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 38
# including undefined tags
msg = 313233343030
result = invalid
sig = 3043021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2223aa02aabb021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 39
# truncated length of sequence
msg = 313233343030
result = invalid
sig = 3081

# tcId = 40
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3080303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 41
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 30412280021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 42
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2280021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 43
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3080313d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 44
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 30412280031c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 45
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2280031d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 46
# Replacing sequence with NULL
msg = 313233343030
result = invalid
sig = 0500

# tcId = 47
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2e3d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 48
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2f3d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 49
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 313d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 50
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 323d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 51
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = ff3d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 52
# dropping value of sequence
msg = 313233343030
result = invalid
sig = 3000

# tcId = 53
# using composition for sequence
msg = 313233343030
result = invalid
sig = 3041300102303c1c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 54
# truncated sequence
msg = 313233343030
result = invalid
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb

# tcId = 55
# truncated sequence
msg = 313233343030
result = invalid
sig = 303c1c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 56
# indefinite length
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000
flags = BER

# tcId = 57
# indefinite length with truncated delimiter
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e00

# tcId = 58
# indefinite length with additional element
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e05000000

# tcId = 59
# indefinite length with truncated element
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e060811220000

# tcId = 60
# indefinite length with garbage
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000fe02beef

# tcId = 61
# indefinite length with nonempty EOC
msg = 313233343030
result = invalid
sig = 3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0002beef

# tcId = 62
# prepend empty sequence
msg = 313233343030
result = invalid
sig = 303f3000021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 63
# append empty sequence
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e3000

# tcId = 64
# append garbage with high tag number
msg = 313233343030
result = invalid
sig = 3040021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2ebf7f00

# tcId = 65
# sequence of sequence
msg = 313233343030
result = invalid
sig = 303f303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 66
# truncated sequence: removed last 1 elements
msg = 313233343030
result = invalid
sig = 301e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab

# tcId = 67
# repeating element in sequence
msg = 313233343030
result = invalid
sig = 305c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 68
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 303e02811c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 69
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 303e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02811d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 70
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 303f0282001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 71
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0282001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 72
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303d021d691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 73
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303d021b691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 74
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021e00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 75
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 76
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 30420285010000001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 77
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3042021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0285010000001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 78
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3046028901000000000000001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 79
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3046021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab028901000000000000001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 80
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 304102847fffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 81
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02847fffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 82
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 30410284ffffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 83
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0284ffffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 84
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 30420285ffffffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 85
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3042021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0285ffffffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 86
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 30450288ffffffffffffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 87
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3045021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0288ffffffffffffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 88
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 303d02ff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 89
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02ff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 90
# removing integer
msg = 313233343030
result = invalid
sig = 301f021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 91
# lonely integer tag
msg = 313233343030
result = invalid
sig = 302002021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 92
# lonely integer tag
msg = 313233343030
result = invalid
sig = 301f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02

# tcId = 93
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 303f021e691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 94
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021f00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000

# tcId = 95
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 303f021e0000691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 96
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021f000000c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e
flags = BER

# tcId = 97
# appending unused 0's to integer
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 98
# appending null value to integer
msg = 313233343030
result = invalid
sig = 303f021e691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0500021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 99
# appending null value to integer
msg = 313233343030
result = invalid
sig = 303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021f00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0500

# tcId = 100
# truncated length of integer
msg = 313233343030
result = invalid
sig = 30210281021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 101
# truncated length of integer
msg = 313233343030
result = invalid
sig = 3020021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0281

# tcId = 102
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 30210500021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 103
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 3020021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0500

# tcId = 104
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 105
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d011c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 106
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d031c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 107
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d041c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 108
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303dff1c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 109
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 110
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab011d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 111
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab031d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 112
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab041d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 113
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92abff1d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 114
# dropping value of integer
msg = 313233343030
result = invalid
sig = 30210200021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 115
# dropping value of integer
msg = 313233343030
result = invalid
sig = 3020021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0200

# tcId = 116
# using composition for integer
msg = 313233343030
result = invalid
sig = 30412220020169021b1c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 117
# using composition for integer
msg = 313233343030
result = invalid
sig = 3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2221020100021cc6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 118
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 303d021c6b1c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 119
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d02c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 120
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf922b021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 121
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbbae

# tcId = 122
# truncated integer
msg = 313233343030
result = invalid
sig = 303c021b691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 123
# truncated integer
msg = 313233343030
result = invalid
sig = 303c021b1c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 124
# truncated integer
msg = 313233343030
result = invalid
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb

# tcId = 125
# leading ff in integer
msg = 313233343030
result = invalid
sig = 303e021dff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 126
# leading ff in integer
msg = 313233343030
result = invalid
sig = 303e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021eff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 127
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 3022090180021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 128
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 3021021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab090180

# tcId = 129
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 3022020100021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 130
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 3021021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab020100

# tcId = 131
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303e021d01691c723dd6a7f5d11b8c8e8bd08173428bc48a2c3f031caaec3bbce8021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 132
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303e021dff691c723dd6a7f5d11b8c8e8bd08345fcca52a9b01748ca203383686e021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 133
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c96e38dc229580a2ee47371742f7da36054f46611d4da0c9a70206d55021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 134
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303e021d0096e38dc229580a2ee47371742f7cba0335ad564fe8b735dfcc7c9792021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 135
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303e021dfe96e38dc229580a2ee47371742f7e8cbd743b75d3c0fce35513c44318021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 136
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303e021d01691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 137
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303e021d0096e38dc229580a2ee47371742f7da36054f46611d4da0c9a70206d55021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 138
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d01c6b899049859a01f5093eab0834104e71ff12bb612ad778fbda8e56b

# tcId = 139
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021cc6b899049859a01f5093eab08342d7a15e7f4b39eaf3250504f090f1

# tcId = 140
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021dff394766fb67a65fe0af6c154f7cbe11bbc0c7c488012fb1b59eb344d2

# tcId = 141
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021dfe394766fb67a65fe0af6c154f7cbefb18e00ed449ed52887042571a95

# tcId = 142
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d01c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e

# tcId = 143
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c394766fb67a65fe0af6c154f7cbe11bbc0c7c488012fb1b59eb344d2

# tcId = 144
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100020100
flags = EdgeCase

# tcId = 145
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100020101
flags = EdgeCase

# tcId = 146
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201000201ff
flags = EdgeCase

# tcId = 147
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 148
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 149
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 150
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 151
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 152
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3008020100090380fe01
flags = EdgeCase

# tcId = 153
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100090142
flags = EdgeCase

# tcId = 154
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101020100
flags = EdgeCase

# tcId = 155
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101020101
flags = EdgeCase

# tcId = 156
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201010201ff
flags = EdgeCase

# tcId = 157
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 158
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 159
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 160
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 161
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 162
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3008020101090380fe01
flags = EdgeCase

# tcId = 163
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101090142
flags = EdgeCase

# tcId = 164
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff020100
flags = EdgeCase

# tcId = 165
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff020101
flags = EdgeCase

# tcId = 166
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff0201ff
flags = EdgeCase

# tcId = 167
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 168
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 169
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 170
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 171
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 172
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30080201ff090380fe01
flags = EdgeCase

# tcId = 173
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff090142
flags = EdgeCase

# tcId = 174
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100
flags = EdgeCase

# tcId = 175
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101
flags = EdgeCase

# tcId = 176
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff
flags = EdgeCase

# tcId = 177
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 178
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 179
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 180
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 181
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 182
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01
flags = EdgeCase

# tcId = 183
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142
flags = EdgeCase

# tcId = 184
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100
flags = EdgeCase

# tcId = 185
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101
flags = EdgeCase

# tcId = 186
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff
flags = EdgeCase

# tcId = 187
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 188
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 189
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 190
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 191
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 192
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01
flags = EdgeCase

# tcId = 193
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142
flags = EdgeCase

# tcId = 194
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100
flags = EdgeCase

# tcId = 195
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101
flags = EdgeCase

# tcId = 196
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff
flags = EdgeCase

# tcId = 197
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 198
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 199
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 200
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 201
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 202
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01
flags = EdgeCase

# tcId = 203
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142
flags = EdgeCase

# tcId = 204
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100
flags = EdgeCase

# tcId = 205
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101
flags = EdgeCase

# tcId = 206
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff
flags = EdgeCase

# tcId = 207
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 208
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 209
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 210
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 211
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 212
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01
flags = EdgeCase

# tcId = 213
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142
flags = EdgeCase

# tcId = 214
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100
flags = EdgeCase

# tcId = 215
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101
flags = EdgeCase

# tcId = 216
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff
flags = EdgeCase

# tcId = 217
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 218
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 219
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 220
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 221
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 222
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01
flags = EdgeCase

# tcId = 223
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142
flags = EdgeCase

# tcId = 224
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30060201010c0130

# tcId = 225
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30050201010c00

# tcId = 226
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30090c0225730c03732573

# tcId = 227
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30080201013003020100

# tcId = 228
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 3003020101

# tcId = 229
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 3006020101010100

# tcId = 230
# Edge case for Shamir multiplication
msg = 3639313930
result = valid
sig = 303c021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021c221a25eb9cc8dd66fdf156b2f6ab601ab6d9c509247f8de5d2671a96

# tcId = 231
# special case hash
msg = 33393439313934313732
result = valid
sig = 303c021c3b3008ed596b7fa276498def40d96b1eb2ffb731a44050ffb732e4e6021c6dbb08c56db737e9392ff4f3a54d8b806d70af226ecf413b3465de55

# tcId = 232
# special case hash
msg = 35333637363431383737
result = valid
sig = 303d021d00d1fe269c3061e4b94604e8d612d70887068cc7d5232cd5a9b72923a1021c3c1cbc027d33fb2451d52dce3a828a8c7ecc490a28a94e5e5bb2c4d7

# tcId = 233
# special case hash
msg = 35363731343831303935
result = valid
sig = 303d021c04586134cc679295dd93499311c4a8af37cb94dadbae18d8ee279b9b021d00bf9170a1b65b665664cf567d40a995ce252a23d6a9f962b05e364486

# tcId = 234
# special case hash
msg = 3131323037313732393039
result = valid
sig = 303d021d00c1f51009b935b4773374364ec3eed72a24b70926e0349c77862f3475021c46df3d98f104ba6602f8041a5bf5495fb240e103d1bd17f2fa878923

# tcId = 235
# special case hash
msg = 3131323938303334323336
result = valid
sig = 303e021d00e822242872f1ecf338a4f773df87b67e9b21bb283acac7d66b26551e021d0094d4e0fc3c6359994a6eaedddd1533f490f72ef85139f8d3b39cf07b

# tcId = 236
# special case hash
msg = 39383736303239363833
result = valid
sig = 303c021c7fd45528eb7bfc3710e273c4468f0b50ebf93f94cd0e7a602a4929a6021c46613dd1ffd85df8d71f3498001721fda4982c27a1c291359b05b1b8

# tcId = 237
# special case hash
msg = 3230323034323936353139
result = valid
sig = 303d021c36d137b69171a486933b50138d1db1842724766afd25c85b0032daf5021d008e700de21f2fc350a34c7cc19054cf371ecab6f7331ccecf68fca0f4

# tcId = 238
# special case hash
msg = 31343531363639313830
result = valid
sig = 303e021d00da3b436908f5a82f26bc17a8577ad2a782946e3a7587b01d253b1dd0021d00a6544e38f24e8117370c049b5d1f6712ea14337a94511224df4496a3

# tcId = 239
# special case hash
msg = 31303933363835393531
result = valid
sig = 303c021c4314a2bd139d47be3d9fd9ebdd72a06a220219c7596b944178ee6f5f021c0e6f1d2f57c699654e9c705d7b8fa3c1ccb0f939f6368bed246b2e10

# tcId = 240
# special case hash
msg = 36323139353630323031
result = valid
sig = 303d021c6a25643464682679d84970c603927f4a8ca83e7ef9715dd1ed84c28f021d00932b78d165c225a5253e6201c0b1ded0898ba24de44b23233eb78054

# tcId = 241
# special case hash
msg = 35363832343734333033
result = valid
sig = 303c021c476aaa58677d9e60477cffd026c43248e2cf3cc21e8fdccb75ceefad021c7799fc7af8f9b929203faf899bb5ca1aecf2492555157282dfde790d

# tcId = 242
# special case hash
msg = 33373336353331373836
result = valid
sig = 303d021c63a98614a1421e2ebb278de53b61618bafc757122647affd358c667a021d008edba806e0a7e438ca35f98405a8ad2d5c3e8cc2d5c4384233aef0a5

# tcId = 243
# special case hash
msg = 34373935393033373932
result = valid
sig = 303e021d00880b5238a014f8b44655b83c175880eb1e8307899a824ea3e07dbd6d021d00a4724c8649fd74e5bc8d7fe6a9067a1376fb8e08dbdaed68980b0f50

# tcId = 244
# special case hash
msg = 39333939363131303037
result = valid
sig = 303e021d00f8743588234634dd9891f4f2f40f4e46b77f97b82dc5dbe234aa6b5d021d0080656e5262bc25e158f3b78f51ae0d6a41cc8cca1aa457221b2eb7fb

# tcId = 245
# special case hash
msg = 31303837343931313835
result = valid
sig = 303d021c2a2357e3d8fe34434582be4dabd58b77b388d1d52adcc664f45dece4021d0094be3a369b7c2788df4587ec5bd4163c4cbc40b77de1a85e8bcfb251

# tcId = 246
# special case hash
msg = 33323336363738353030
result = valid
sig = 303d021d00b6b0c0aba6c611300ecad8816242c415f183a2bd4d46cd7769033d9b021c7750b24be02f22dc0b656fe4af377413f9453dff99226915dbb6e08f

# tcId = 247
# special case hash
msg = 31343438393937373033
result = valid
sig = 303d021d00a5c1a75c2779f3eb83a65e295927cce4288e9d5c2132a4c7ca92526e021c10fe30f0be33a785385137b57d806140a402b9bd3c1df1b57de6da63

# tcId = 248
# special case hash
msg = 35373134363332383037
result = valid
sig = 303d021d00b92b5521d1a7abe77e8524dbd3001121cf83c08017e3917bc58b5d1c021c224b113779017f6a522171edf930f1b5d4f5e7dedc6d2d514fd7883c

# tcId = 249
# special case hash
msg = 323236343837343932
result = valid
sig = 303e021d00ebd3ecf3aa64cdcdd171585a141a4a673a8d5de0ca087dfcdf62432e021d00e0f1a0f7b8f5ac4a42632f87156ad1094079393b03f2051a9fd60249

# tcId = 250
# special case hash
msg = 35333533343439343739
result = valid
sig = 303d021c6c3854297e1f267b64a28e0cd6148e0fadcf85bc8d5c23947543bcb8021d00aa0594ee11312f5d4767d296e5ca83df68072811f81a8d27e178ca5d

# tcId = 251
# special case hash
msg = 34373837333033383830
result = valid
sig = 303c021c785ac8c956d7797ae67498775b3c446c41735eb15f3430b49f6a09f2021c5710879ab83994e809c8d2cbd6f2ac5c205b4b8d6226e98be03e7967

# tcId = 252
# special case hash
msg = 32323332313935383233
result = valid
sig = 303e021d00f1f3d016693125ba73981c8f9a1748e5dce1d9634355f26fa536190e021d00b574e97def60dcd0e9177106483791b2edb4ab0342b9f5ebb957d5b0

# tcId = 253
# special case hash
msg = 3130373339333931393137
result = valid
sig = 303e021d00e64f3371522cb1a5f0d1511b152b20e01deca0b3284786853cac279a021d00c9a2e5f4ffde22b9d4ed0179ce74fff408ea918dda7685c7980ae61a

# tcId = 254
# special case hash
msg = 31383831303237333135
result = valid
sig = 303d021c1f99dd6ef72feeeda6c123baa4fabb126d7dedb64130fae3f4230797021d00e441ec51dca6271b043e95753c4043d7cb4e76fdc13d6aea45fbf243

# tcId = 255
# special case hash
msg = 36303631363933393037
result = valid
sig = 303d021d008637a09627c52766bf96f0b6cea6f2ac3eb303001c5f6fe6d628e4ba021c10b66c599455d40077bb865ed43e2c2cc403473baa6d63b16be48c84

# tcId = 256
# special case hash
msg = 38383935323237303934
result = valid
sig = 303c021c52a010a23e4f9ebb459bbe9f3057e6c19761fb99d25c9b16b8f007d8021c526dc1f34444de00447ba23c76950f2c159579d548b6335d127ea321

# tcId = 257
# special case hash
msg = 31353830323334303934
result = valid
sig = 303d021d00fc49caaada528f3357e5a264f2e7f970ca1b15ca5fee28741d1202ac021c175e884d10d0bfd20b39311ce2c53083da167d1f3dfeb990e59ed628

# tcId = 258
# special case hash
msg = 33393635393931353132
result = valid
sig = 303d021d00d95d539a85c9edacd4e02ede27b0e0b737099612d166c637c83a9f34021c59936a2b90b7f3f3da83f64dec8e347a3bfa57baadf9acea18c071d8

# tcId = 259
# special case hash
msg = 32323838373332313938
result = valid
sig = 303c021c1895e65593d71e5635cce022dda19bd155bb9a7f2e307e5ce9127ade021c121b487c320c544dcdd471d46fcde2ce5dc9d17fda70544c4eab50a2

# tcId = 260
# special case hash
msg = 32323330383837333139
result = valid
sig = 303d021d00b5f4c85b13b51a5da81a236f1105937f3d98856d2aeb57101b6b499c021c3be74ae770fa6467f76a742eb9e504a815a4a60e74b38bcaa89f9b06

# tcId = 261
# special case hash
msg = 313239303536393337
result = valid
sig = 303c021c07a57197667a0c40423d4811ff96384c9330467e8a28eaa4c0d519f4021c011062c8694494baaed24ff610e1e4227efb59a163c33fafd40100f9

# tcId = 262
# special case hash
msg = 32373438363536343338
result = valid
sig = 303c021b7f718615ba1d0a9d27a8c5a678a6225ffe1233ed8b417c9343b310021d00cf6a87e4496725c6a2612f4034ddf4b31c7435e2fc3a163e92d463ba

# tcId = 263
# special case hash
msg = 37353833353032363034
result = valid
sig = 303e021d00ba8f95a4363c493a9f78bb61dbefaa9587d028bb8344420de2b0cf21021d00b3798c2d6e27a2591c2edc18320b78bf11df194b11b3fb498c595232

# tcId = 264
# special case hash
msg = 32333237373534323739
result = valid
sig = 303c021c596b74077801db2e889d3b2aaa3990fe180abc152d48528385ca955d021c38bffd416f779843fad389750003eb0708112a4834c554f476a3e0d1

# tcId = 265
# special case hash
msg = 373735353038353834
result = valid
sig = 303d021d008547f62967523a06c9690e5ff66c3f2254cda28f09ffccc222433d39021c3d9ebf664ee551bb7b33157d6c6c5fd456bda3d4ae460215ec1a5f94

# tcId = 266
# special case hash
msg = 3137393832363438333832
result = valid
sig = 303d021d0090ee3fab9c6ce373a1b35fc135fe878280ee25e58a4bd7529e91b4f0021c6451e7526505b44e88472b46eda3fd2679824dcdfc445e67f35ea382

# tcId = 267
# special case hash
msg = 32333936373737333635
result = valid
sig = 303c021c0a530530b6a9238d2d1a3cf468986c87f3b208f61ea0486d53140c17021c5f027a73f31a5cc2bee81ff0019477c542fd782ecde0e551fcd37e93

# tcId = 268
# special case hash
msg = 35393938313035383031
result = valid
sig = 303e021d00beab4abd23df5e2acfff07c82e245dfa7d587d0238c2c9ab9c88a96a021d0098c6507635536840edf604f9baae6408ce4d3fbee694db3abd825011

# tcId = 269
# special case hash
msg = 3136363737383237303537
result = valid
sig = 303d021c3ec8c36335cb98fa07b983c35b7fc256f44a5aa192d6087595145a15021d00c32b7a47ac6271f4593562bbbf91f9e07395a5e4d46970495cf29f05

# tcId = 270
# special case hash
msg = 323036323134333632
result = valid
sig = 303d021d00bd635a741f1f2a1d9ac1698baf5cfc491d5e3f8e15f1cacbe4ffe4dc021c4bb606cf7cc11d0d7d96b83966f42276095ccc445882ed5afddabf1e

# tcId = 271
# special case hash
msg = 36383432343936303435
result = valid
sig = 303d021d00812c08622c0a09d949b7628585c4f4f2db4c5591b5da148ff46d5cd4021c2104f9bc9d0079acb3077d2db58f388119500c5322cb9b5389b5c5d7

# tcId = 272
# special case hash
msg = 33323639383937333231
result = valid
sig = 303e021d00fa4e1c8b0006f269c855eb495fa3a113f643fa8b1fef2b08ab145994021d00fe85b8b522c7f9e8943e0f62643395bd1fcdabc892c315d108b75f65

# tcId = 273
# special case hash
msg = 31333837333234363932
result = valid
sig = 303e021d008c1d9b7911bacb6b4a09582648b992d46a1832eb006178c0c77fcb10021d00becbe12b99f243766da5bdad07461b9226a8298672b4f1adb35357ef

# tcId = 274
# special case hash
msg = 34313138383837353336
result = valid
sig = 303c021c78850a40530aa258e478e7c547d3a5e4944d3524f1676443e4dfb37d021c687058e1ca478f52a30c9a3f8e2eea9d8c40599cd47ef66b9430d17d

# tcId = 275
# special case hash
msg = 393838363036353435
result = valid
sig = 303c021c066e7268a6abefe1b4b916ca51c3e958e40dc78c3661313e0ed2e77d021c6404d8a332a837f2ab6bd13e3ee4aad1e9307d449e7f9b7d6332030c

# tcId = 276
# special case hash
msg = 32343739313135383435
result = valid
sig = 303c021c4eca73709a67c41603ca5af494c8132483ffc2e0bf171b52de5a5e81021c2c79137cd2add3ce3a76792270e347221a3ad786eafc2682b39bcf95

# tcId = 277
# special case hash
msg = 35303736383837333637
result = valid
sig = 303d021c0178512f8844984222393a63263e0a009601869e632f07eb7470aa05021d00e32657cded1122cee0a4f66ff50a32da1f05de4c5e217acdf5eb6fe2

# tcId = 278
# special case hash
msg = 393838353036393637
result = valid
sig = 303d021d00e2c7bf1222ca23a56492873c2d3fa6c7030cc166d693142dcea272b6021c715a4c82fda4404217dea6c0bbf3ac24f8faa2b435fbc6d51a32c4a8

# tcId = 279
# special case hash
msg = 32373231333036313331
result = valid
sig = 303c021c49886a8c26c91d649cbfecda6ce8d1441e764c66f5d81dceedb6c5ba021c4370d8bcd4f052fac9491d62850b6a6a85d5acc44d9248c3dff30bf2

# tcId = 280
# special case hash
msg = 33323034313031363535
result = valid
sig = 303e021d00e1ae225e1aeca40747ff3e7ad1f75eb9bc90d637160a7f58ce12e591021d00b97cbea3a9323110315760b7e2ede496514b30f0eec521ffeb07a634

# tcId = 281
# special case hash
msg = 33313530363830393530
result = valid
sig = 303d021d008a93b87b46512544fb9a7af5c41e3aa72e40235ef87ccb7108daae48021c157db617ac697df407af7a11626c52a1af7ef189514da39918c43010

# tcId = 282
# special case hash
msg = 31373237343630313033
result = valid
sig = 303e021d00ebdebe6388b9f460fce6d84faa67ded1e864ef09e77ea3ce58a5deff021d00be5052033eb40380c2b1325fe97dcc55841e147a89f02a296b4505ef

# tcId = 283
# special case hash
msg = 3134353731343631323235
result = valid
sig = 303e021d00e85d0667972d003c82afb9e18b702357119b4f38401a5ebdfcbea88c021d00eb7b3e5268a4ce6280f72d7e9a3d74e5cac50b1c3a5296cdb5a49d82

# tcId = 284
# special case hash
msg = 34313739353136303930
result = valid
sig = 303c021c3d243581c0874fd4eb4d80f896c5067429ad455314881951ab5ec6e3021c0ec47aba08ccba88c1a6ddc289f595bda08dc2dd34d12dcefb68094d

# tcId = 285
# special case hash
msg = 35383932373133303534
result = valid
sig = 303d021c75c966bbdcef9157d47a134231229f9f5ee8ce458775fc747d4509bd021d00e344fa716e2088d95a55d02a978a416da10f22a5cccf35a2863227cf

# tcId = 286
# special case hash
msg = 33383936313832323937
result = valid
sig = 303e021d00cfdf599e986d770b73784d97149f9945fd16d22c302bb796156e7fb4021d00c6409785047b0083f008771b40db8502583208b61c8984671acb0929

# tcId = 287
# special case hash
msg = 38323833333436373332
result = valid
sig = 303e021d00c53c4aeec8f2e7a5cc0e885a6031aa1a6c1b7b7fec83b5084cbe291f021d00b0e6d10a8fd86f958c3b0f4662ed8ca0d6eadbc892aac4200fcf8315

# tcId = 288
# special case hash
msg = 33333636393734383931
result = valid
sig = 303d021c2386550d6e75350bcc32507bfc9beb9c26d72ff82f671c1f5282e98b021d00a55b8de808c4359fb529b80a80d9fc6eddb5ce08082c3b677c689991

# tcId = 289
# special case hash
msg = 32313939313533323239
result = valid
sig = 303d021c1fbd192d955ce02b64a3be5bb21bef22b53a6c6f9576d8f889b09e4e021d00f5a9b673a4ee5aabf1ca8e8289f25b62a3e08b956f7418c03e2d3031

# tcId = 290
# special case hash
msg = 35363030333136383232
result = valid
sig = 303d021d00b80ffba451db9fc2194e450bdd43bc0f53a7d0f4a78900c09fb8d9bc021c0124eeeab9035b6c845959e70b04d1e187d554807d6751afabcc1802

# tcId = 291
# special case hash
msg = 383639363531363935
result = valid
sig = 303c021c187fb026ade3ad16dd4b2813e8ebda433cb6cc3af1615bedf486a9e2021c6fbee53fa884d296f34f7719f74919434d1b7090c485eeed2fb8fd6c

# tcId = 292
# special case hash
msg = 36353833393236333732
result = valid
sig = 303d021d00e598a16fe12da79e9814f6985c9a9334010f287dc9e38de857ca5fc0021c19e0ed54f0e08ad091a163b4c7b86d0634da2c86a7a8991f5d8706d8

# tcId = 293
# special case hash
msg = 3133323035303135373235
result = valid
sig = 303d021d00b31a10480e397c8aa46f52a0f2fb5c22ebc0534fba156718b50cf6ea021c602004df4b47a2065130ca3b05f1eb02d0b37b79b04b1eb799408346

# tcId = 294
# special case hash
msg = 35303835333330373931
result = valid
sig = 303e021d00bc47e242d19dcc6321913980d73923e430bc6623d219529d586619b6021d0081397dd2f52811b534ed754a937d904f04a7de278fa3bc8926de6946

# tcId = 295
# special case hash
msg = 37383636383133313139
result = valid
sig = 303c021c5be0e0dfb26b1caa88f866504aa8e76f035a82abe00028d962bcfafa021c3c3c1df06026123471bed324ca79c51b28b3d10b1ce877cef21b852d

# tcId = 296
# special case hash
msg = 32303832353339343239
result = valid
sig = 303e021d00fe79d0cfe455724792cb5ab0580ad4f2918c1403ec12f0bdd2ce6528021d00f1357cd4afc402994ab868b0163f41701e0f00e561fdd97e0db6f7b9

# tcId = 297
# special case hash
msg = 3130303635393536363937
result = valid
sig = 303d021c1858c5d857124cd703e7c2f5e99d5025d6d979539c6f50b1d00fbd34021d00d94a5adb6d9c5001162620415541d49334fb929bc86a350ca4591195

# tcId = 298
# special case hash
msg = 33303234313831363034
result = valid
sig = 303e021d00e6b2ec967cfa25f57234b9ef1d87c5945502cbbd5831c862f00774d1021d00caea26368bffc8e562b2bd03aa6c9dc41c529659fefe6597fce9cd9c

# tcId = 299
# special case hash
msg = 37373637383532383734
result = valid
sig = 303d021d00a59b438b2472074a93a289b33f5b13e604977dd3ab4d744d08e1061b021c699574a17dc8c7298c9321ca78552e5128ea801d056f387ba42f7a09

# tcId = 300
# special case hash
msg = 353434313939393734
result = valid
sig = 303d021c748481709c6882c4a130193834a57f4bc714906211ec6cc12c400dff021d00eec6c9d5a06786f821a8117eec3dc025ed3ac74e39e98a16a4aa285c

# tcId = 301
# special case hash
msg = 35383433343830333931
result = valid
sig = 303e021d00bc8991b506997403e123136a9c140a4336364733b0815f40d1dbd5fe021d00819503ea3b4c07fc157f948f6949705d560a881fc1c6af4b7391765c

# tcId = 302
# special case hash
msg = 373138383932363239
result = valid
sig = 303c021c1caece75c8e31bb0c5cceb0842f23683b8557a97036574ea88ceeabd021c645ad3276aaee22b693647f00dce5f91a03b678b789b667cd3b8e751

# tcId = 303
# special case hash
msg = 31373433323233343433
result = valid
sig = 303c021c3a7d582068aaecaba945203bc445b3312e5cb40886522987aced68d0021c39b3c612b6743a13bb2ffb83514d690cfcb9a7055e3a993cb0863938

# tcId = 304
# special case hash
msg = 32343036303035393336
result = valid
sig = 303e021d00f773c49fd0645716d16e559e22c39101df266cdfa7cb61ce46f85280021d00df6109fd77a241031cf03b376e001d8a3cd2a6b646edbf9e578133f1

# tcId = 305
# special case hash
msg = 31363134303336393838
result = valid
sig = 303c021c79cf893f66f7faa5ca08553ea03456107e7bb391a5e51260cedaea84021c32e8e3509468da7216c59975d4f3d5493848a03f864b2332044e68d1

# tcId = 306
# special case hash
msg = 32303935343235363835
result = valid
sig = 303d021c025ecd1a7ab765fbfd25a6d7cd3c461e17f465e6958bce9f492b7a5a021d00a1ca95038603d302761e416935acbd6b716a316c9b79c57d4053cb79

# tcId = 307
# special case hash
msg = 31303038303938393833
result = valid
sig = 303d021c3d14a4c21ba4dbd338fdd8b15fcdd0a9228f157cfaf2b09dd4f2aa67021d00e1640e8bd2a6110dc18d6f290b7325814710c0dc88b76f127c5e9e21

# tcId = 308
# special case hash
msg = 31353734313437393237
result = valid
sig = 303c021c258dce916ef78b9d8a87beaf6edd35bcccc08c5de488586e1b7b749a021c4ff500db4d665c7062179c099b2985a814f99fbfa44a3a709024d589

# tcId = 309
# special case hash
msg = 32383636373731353232
result = valid
sig = 303e021d00cecf0aec5357749f357c459575298a3384dc4ac381438ff99acd9993021d00da7adb092a6890e0918c235a62d4a949b0cae5e57856975108fb2b91

# tcId = 310
# special case hash
msg = 31363934323830373837
result = valid
sig = 303d021d00d77f2e547fd68d5db314901da1ff7ecaf3d0c17ec047a974a7cec33e021c443a97afdf882272bf0233c8c4a8d23c9352ad89b1770c26240f6650

# tcId = 311
# special case hash
msg = 39393231363932353638
result = valid
sig = 303e021d00d5dcf93e6e1b93323ea2642d3405a7423cb04f59c03420193f394886021d00ddd5842e4928ee4b5d77d43d4a4bfc7f991c899727b75fc941b52995

# tcId = 312
# special case hash
msg = 3131363039343339373938
result = valid
sig = 303d021d00a9bc3ebc6ee34421326711ce29518d02bd403ead806a3e4502efa0ce021c12610b89a61689a8eb6e062d2524278155fe499ffecc0e0d940d48a7

# tcId = 313
# special case hash
msg = 37313836313632313030
result = valid
sig = 303e021d00c703c508784ef71b596dcd61c5b01b45c6c69d2b36a5a3b7701e5976021d00f05444a777204118f3ac2afc92d0212831bf7002158e7c656f4c07db

# tcId = 314
# special case hash
msg = 33323934333437313737
result = valid
sig = 303e021d0080674b740b64d383677c049a6f4baeb214f4a6b5933033853e634578021d009b3a804c75ed790e31966bc25730b7428af8c73c65fb503c06c597eb

# tcId = 315
# special case hash
msg = 3138353134343535313230
result = valid
sig = 303c021c7ed658c30f4a0dcc894c39f9320f59a185509ffee45eac6023577c7c021c47ac94a501806d5adffea9fcf3ccd8cf79f3cc47eca9fe38fc4886b4

# tcId = 316
# special case hash
msg = 343736303433393330
result = valid
sig = 303d021c397f669cc399a91da96c16efd233f6fe60d1b7caa397cc257843b30b021d00f19375fe66eae4738ec9dc5b7ef51cb33d4cb258f36944d37dd245cb

# tcId = 317
# special case hash
msg = 32353637333738373431
result = valid
sig = 303c021c537ec369b3f0d891e166f6c74e5d73dd2c4822210c5fe5f978c34072021c0b183c48b5f6e69245cb76e1e2c39663eedfb74ba9538739ac495ff5

# tcId = 318
# special case hash
msg = 35373339393334393935
result = valid
sig = 303d021d00d0ed7159cc3a79988f3c279287ca8ed10bb8f02c8b5a6204aead1223021c75ee1e5c00e81899bfa8545edcc64fdf707dae1f61d976d2f0883777

# tcId = 319
# special case hash
msg = 33343738333636313339
result = valid
sig = 303e021d00cf43329a9781db8044a920758e58399866fe7748c0f5d6a3bcdcbcbd021d00d9740d2dd716290ad4160345bcd4af03af01c44b610b1e5953199075

# tcId = 320
# special case hash
msg = 363439303532363032
result = valid
sig = 303e021d008ab2e92c8c9143f9d8da3bdb1d935cce3ab60ae99b3ccfe863b15d14021d0088c89302e8a9c591c6ed16b1ae46f966004d0b2685449842e291d742

# tcId = 321
# special case hash
msg = 34373633383837343936
result = valid
sig = 303d021c04f60f8450b448198cf7981116de06d4c4888cd26be3a5947092238f021d00cb23fcb33c14f089c2ae030146d68fa65eb9b086fa792f95be8ecf35

# tcId = 322
# special case hash
msg = 353739303230303830
result = valid
sig = 303e021d00f270f7a70a96a0f916c7530c6dea7178e6c087ddbcc60aacd8a7c553021d008b2c378554121365a180ad4edf1a12e566ba55eeabf525356783e603

# tcId = 323
# special case hash
msg = 35333434373837383438
result = valid
sig = 303e021d0085ad01b236ca4a5451969242e16165d322428235a2af8fdcd6c4c7b9021d008eb2998c5e0aaf279793caff59a266ca2068d94ebf77bae81fd0fb6a

# tcId = 324
# special case hash
msg = 3139323636343130393230
result = valid
sig = 303d021d00cffdb8d64b5b84b490ff73d77e51cc7797bf67c5ee0a4999def17230021c3baf4b34e1a35e9606a460b395063a554264a9c43cc302ab5abf473e

# tcId = 325
# special case hash
msg = 33373033393135373035
result = valid
sig = 303c021c66cda58a5a6ddb9476e36dbad5df542be88d7e447bdc3dfe1d9e8b2c021c0d99d387486a964ebab4e29bad583e46a5a200391d1065768a4e35fd

# tcId = 326
# special case hash
msg = 3831353435373730
result = valid
sig = 303c021c3200761902825bd353908accd2be6b482645646971f96dc490706a37021c3ed77899efdbe418370fa7998df3b7c924bed6864535277f805c894f

# tcId = 327
# special case hash
msg = 313935353330333737
result = valid
sig = 303e021d00ba0eff0ee46aa9fca5ab8ad64aee4037931d3ad0b953d404ef9f7bdc021d00afdf21df0dcbe39c2f5fa9ef7e1a2bca87d1213d1eca438929ad8982

# tcId = 328
# special case hash
msg = 31323637383130393033
result = valid
sig = 303e021d00a20c6883fc6ec1ca4bb378ac88ed670a742a6284113d5fa3182a1858021d00e0a73b913b94163175d264224cc70736f2fb8e8d58e914b18c921323

# tcId = 329
# special case hash
msg = 3131313830373230383135
result = valid
sig = 303e021d00f2f4af956b0c5409949d377e9bc68e4f1abef7969b518f8beacf27db021d00df3a7b5993d2393ade70a2cfc1e8671a78ca4fecb56425a661a2d2fc

# tcId = 330
# special case hash
msg = 38333831383639323930
result = valid
sig = 303c021c331a1a553494f8524adb4e8a722d558965fb703ae432bf3cbdb255c2021c5ab6e3dee6a2516fc4e0ac88e6dfc81d2bc37c98949cc03e521d389d

# tcId = 331
# special case hash
msg = 33313331323837323737
result = valid
sig = 303e021d00867135558e06e19796ebce8e3555c607a6607d46f7c8da6b8552ffc1021d008e827e8b9a4f74efeec7d7ba5c23428fde0227df55a1efc179a353b1

# tcId = 332
# special case hash
msg = 3134333331393236353338
result = valid
sig = 303d021c6746903ca095bfd3f6378a70092723483ca190b2392d8b1ad337969f021d00f33bfae0835c23a80ec9f33ce9a9035c192836a0b2fadd347d803f96

# tcId = 333
# special case hash
msg = 333434393038323336
result = valid
sig = 303d021c7fc0d8739ecfe349e506e71203a6e60e628a1bb0c67d5e574cb8831c021d00cf8bb1557152c57550a0fde6571456fa752782f7f92f7bb235dde39f

# tcId = 334
# special case hash
msg = 36383239383335393239
result = valid
sig = 303e021d00b4486e3139e0b1542892db3d3f51b0524894e19cb00cd07b03ee9c97021d00ad9728d77a8b7b4fa435b3345847860c332d65d8152aa6503ab18755

# tcId = 335
# special case hash
msg = 33343435313538303233
result = valid
sig = 303e021d00afbbdc8e50e801ecbd2e3705079717f4f9d69f3b3d85215aeecb4fbc021d00eceadd4e2cc9cea10b56d16a03fa551fec3eb808bd8d9f0926d14ed3

# tcId = 336
# special case hash
msg = 3132363937393837363434
result = valid
sig = 303d021c4a762f7d146f9eafff5ad11a6978260c818b801c3488dd60411f5cf6021d009ea77512585620ef2cfae8b8c9d8171229a32197e1949561bb75a049

# tcId = 337
# special case hash
msg = 333939323432353533
result = valid
sig = 303d021c227fe52b579833feee16c287d29273e2256df68aff0b94d2752d877b021d00bd79935e5faa8e9356622fea0135ecf796daf60333d5ab125f71e512

# tcId = 338
# special case hash
msg = 31363031393737393737
result = valid
sig = 303e021d00cd5365983eb165db39ba0c66c3a45b2ce1370c9ad14a9aa76dd4633a021d00a8c77ce42ab1c888a6b5d04b71139fd882328622e15e80252e5cf7da

# tcId = 339
# special case hash
msg = 3130383738373535313435
result = valid
sig = 303c021c54d6d44373f7dfc98455a22cd39a0b320fabc33215216b37365b5a16021c29cc690f2467c02e07bc416ad47204975af8c5c3346973f2b03ded3d

# tcId = 340
# special case hash
msg = 37303034323532393939
result = valid
sig = 303c021c2f5048c9ef9f30da7cb3fe4624552200f9e57a46d79db0484a0d9cf2021c06dad3a4682725852869a1a459bec865661e1a38a9e546eeaac7cb84

# tcId = 341
# special case hash
msg = 31353635333235323833
result = valid
sig = 303e021d00abbf0a02332fbea779899d31d3abd2d22c9c02d4058ced639bf06c45021d00cce0570f3812e5cfcb23376c554c7fc35dbcfeb623a7958c664ac6a4

# tcId = 342
# special case hash
msg = 3233383236333432333530
result = valid
sig = 303d021c1c30cb8bc21087b77eb1216ee8629e3676d925f1ae15077cc631da4f021d00ee998157bdefb77d1044e983a6afec7d91a23d95c937fc5c6548c989

# tcId = 343
# special case hash
msg = 31343437383437303635
result = valid
sig = 303c021c43ee11a7ab62e2125e765c2ce5d4f84704183539810512268d87f195021c65897e54025777659ee802b39c6bfd5ccc5706a9d1b38f95c078abaf

# tcId = 344
# special case hash
msg = 3134323630323035353434
result = valid
sig = 303e021d00a1fe3f4d3f43aaa3dcafa79ed99fbc045c11c352caacd89f0f63847e021d00ca2e37bd2c13b9fb3f8a55b7a67eb034240395abd39fecde75141336

# tcId = 345
# special case hash
msg = 31393933383335323835
result = valid
sig = 303e021d00bd290286ca08485ea4137010c67203c2455e7b669d153c6be40087c7021d0097dd7502ba3637f33baea5b2398647ad24c0fe35072bd963149b5aa0

# tcId = 346
# special case hash
msg = 34323932313533353233
result = valid
sig = 303e021d00c917269a5a4ce80b7fe54a8bed49326b50527a4d2fb0a3093182b5a5021d00a195ec0e69e3172e854e87dd651b44433fcd7dcbb7bd59515d2afe8e

# tcId = 347
# special case hash
msg = 34343539393031343936
result = valid
sig = 303c021c0b7b5aab8364dd4b11001a0b986d5aa4fb61ee720237417a7f63722f021c7f13b411e645e819fed1b925ebe807d9560b44d0ba1b75bd2fbd1294

# tcId = 348
# special case hash
msg = 31333933393731313731
result = valid
sig = 303c021c505b974f8ecf07b60ffdbd2b2df9324de92b39476eb763a4c25f126a021c1c36ed1dee772c724205f717c383f49a87a5bc3caa0ef81360f9d800

# tcId = 349
# special case hash
msg = 32333930363936343935
result = valid
sig = 303d021c24219e49b98a9b64e56d21c908c870eb88b447d9f1ddb735083d6df2021d00bc4d7644faeff1e134443b2bb3bb2a20e2a4a7c193180626127ce937

# tcId = 350
# special case hash
msg = 3131343436303536323634
result = valid
sig = 303d021c083246081cf2f8c5e1cd42b60450fc6cac3b0ab03d38bdd271cd7370021d008d117ec32dbf939394499f7dbc2ab77290e9222d6d60ea02ce45c58a

# tcId = 351
# special case hash
msg = 363835303034373530
result = valid
sig = 303c021c24916961dd0d168c2878ca4fd065b81311c03b7f23f8416f4a23b14b021c1e37e3c03b2333b33bbb2ebe05b031042af19315adfdccdfc8d078ee

# tcId = 352
# special case hash
msg = 3232323035333630363139
result = valid
sig = 303e021d008df5468b123b92477a5c57ea86c54c5c9e41d119370dc18922aa8303021d0086bdf06b75f4d49d02c5806926f5d01b1a4f6a8146664a03fa820772

# tcId = 353
# special case hash
msg = 36323135363635313234
result = valid
sig = 303d021d00f65bf16f7ced97b0cdc22b08c62ef811306813134b001bc51140e828021c3a9b7c008cdaf803368df9ee50e274c7a9f9369344d9918e0c08dba9

# tcId = 354
# Signature generated without truncating the hash
msg = 313233343030
result = invalid
sig = 303c021c6239877430e268f1a3ada2c90357247c6ca6687f49023bed0fb5b597021c355c60c09f0dacb9d74b7ccde71806c50fda8750c6ecb7abba910ac7

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 044408e5c95e332ab6c2823a63959391d60a6d69c59eb1f7bd272206b9f5278e901fb4773aeeb2d8255ba4df3cf3db7e0557dbc6134c55f3a6]
[key.wx = 4408e5c95e332ab6c2823a63959391d60a6d69c59eb1f7bd272206b9]
[key.wy = 00f5278e901fb4773aeeb2d8255ba4df3cf3db7e0557dbc6134c55f3a6]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00044408e5c95e332ab6c2823a63959391d60a6d69c59eb1f7bd272206b9f5278e901fb4773aeeb2d8255ba4df3cf3db7e0557dbc6134c55f3a6]
[sha = SHA-512]

# tcId = 355
# k*G has a large x-coordinate
msg = 313233343030
result = valid
sig = 3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a

# tcId = 356
# r too large
msg = 313233343030
result = invalid
sig = 303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04315a83008dba00b351c3f9fca0811c3ae1884fa9a2a75e6d5e71f269504bbe6a25be253b582efab4b8b9e61372767a7a3a423c0943127296]
[key.wx = 315a83008dba00b351c3f9fca0811c3ae1884fa9a2a75e6d5e71f269]
[key.wy = 504bbe6a25be253b582efab4b8b9e61372767a7a3a423c0943127296]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004315a83008dba00b351c3f9fca0811c3ae1884fa9a2a75e6d5e71f269504bbe6a25be253b582efab4b8b9e61372767a7a3a423c0943127296]
[sha = SHA-512]

# tcId = 357
# r,s are large
msg = 313233343030
result = valid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 042f6983b6e9f8ef96c2d981f69be54b06591ed73fe40c8a546b936a7971bf57726c26c811d7625a9d851951c1fffe236b0eb3b896bc4c98ef]
[key.wx = 2f6983b6e9f8ef96c2d981f69be54b06591ed73fe40c8a546b936a79]
[key.wy = 71bf57726c26c811d7625a9d851951c1fffe236b0eb3b896bc4c98ef]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00042f6983b6e9f8ef96c2d981f69be54b06591ed73fe40c8a546b936a7971bf57726c26c811d7625a9d851951c1fffe236b0eb3b896bc4c98ef]
[sha = SHA-512]

# tcId = 358
# r and s^-1 have a large Hamming weight
msg = 313233343030
result = valid
sig = 303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d1f515971cc9391153569c2befa1f915e2931110757760ebd7e61f8641c3db8beea20b13205389dcc4ba8a6af4d6da2604cacd7184ec9dbc]
[key.wx = 00d1f515971cc9391153569c2befa1f915e2931110757760ebd7e61f86]
[key.wy = 41c3db8beea20b13205389dcc4ba8a6af4d6da2604cacd7184ec9dbc]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d1f515971cc9391153569c2befa1f915e2931110757760ebd7e61f8641c3db8beea20b13205389dcc4ba8a6af4d6da2604cacd7184ec9dbc]
[sha = SHA-512]

# tcId = 359
# r and s^-1 have a large Hamming weight
msg = 313233343030
result = valid
sig = 303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e8f90a717714f0158d9521f18c14ae8c83bf1eeba115c46cbdabb20b66f50ac13461c02da02edfe4296a1f543dde7b4359f905e04193d3cf]
[key.wx = 00e8f90a717714f0158d9521f18c14ae8c83bf1eeba115c46cbdabb20b]
[key.wy = 66f50ac13461c02da02edfe4296a1f543dde7b4359f905e04193d3cf]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e8f90a717714f0158d9521f18c14ae8c83bf1eeba115c46cbdabb20b66f50ac13461c02da02edfe4296a1f543dde7b4359f905e04193d3cf]
[sha = SHA-512]

# tcId = 360
# small r and s
msg = 313233343030
result = valid
sig = 3006020103020101

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04723bc0c9b7ce6ea784ec075036cede90452c76576bd8fb5be4dc0fb1cf405820d92f48552b551c7b11f49406dc892fd659971ae7f9e74b59]
[key.wx = 723bc0c9b7ce6ea784ec075036cede90452c76576bd8fb5be4dc0fb1]
[key.wy = 00cf405820d92f48552b551c7b11f49406dc892fd659971ae7f9e74b59]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004723bc0c9b7ce6ea784ec075036cede90452c76576bd8fb5be4dc0fb1cf405820d92f48552b551c7b11f49406dc892fd659971ae7f9e74b59]
[sha = SHA-512]

# tcId = 361
# small r and s
msg = 313233343030
result = valid
sig = 3006020103020103

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04a0dcce127084f955a4e49a7c86b9b91b05ae7afd6eb07225a6541d88f10a1d4fef93934967bb6c5d8792bbd47ab3abb406899a00b1c91b4a]
[key.wx = 00a0dcce127084f955a4e49a7c86b9b91b05ae7afd6eb07225a6541d88]
[key.wy = 00f10a1d4fef93934967bb6c5d8792bbd47ab3abb406899a00b1c91b4a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004a0dcce127084f955a4e49a7c86b9b91b05ae7afd6eb07225a6541d88f10a1d4fef93934967bb6c5d8792bbd47ab3abb406899a00b1c91b4a]
[sha = SHA-512]

# tcId = 362
# small r and s
msg = 313233343030
result = valid
sig = 3006020103020104

# tcId = 363
# r is larger than n
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e10abc9fe15bcc63f009e161aaee26602415bcb45bc6c99ce7ab2b10fbebff4e4de0dfaaf04594dd603cee80b5d9ab78b6707608a95e574d]
[key.wx = 00e10abc9fe15bcc63f009e161aaee26602415bcb45bc6c99ce7ab2b10]
[key.wy = 00fbebff4e4de0dfaaf04594dd603cee80b5d9ab78b6707608a95e574d]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e10abc9fe15bcc63f009e161aaee26602415bcb45bc6c99ce7ab2b10fbebff4e4de0dfaaf04594dd603cee80b5d9ab78b6707608a95e574d]
[sha = SHA-512]

# tcId = 364
# s is larger than n
msg = 313233343030
result = invalid
sig = 3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04fbfabe6c640856ae5dcdc9e4b706fb3db23ddca46b80b9057ab9e44f6b62d4697977ffe19bf3185083b1ede2161aa5725401a8f57851fc82]
[key.wx = 00fbfabe6c640856ae5dcdc9e4b706fb3db23ddca46b80b9057ab9e44f]
[key.wy = 6b62d4697977ffe19bf3185083b1ede2161aa5725401a8f57851fc82]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004fbfabe6c640856ae5dcdc9e4b706fb3db23ddca46b80b9057ab9e44f6b62d4697977ffe19bf3185083b1ede2161aa5725401a8f57851fc82]
[sha = SHA-512]

# tcId = 365
# small r and s^-1
msg = 313233343030
result = valid
sig = 302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0491a85b3c5e90b409f6b8d3bca9117a54a40f4162b388bb9367fd6439f1cedf20ab52eb7154b7ea1f2934a9c8292906e18a0e572002cd2f7c]
[key.wx = 0091a85b3c5e90b409f6b8d3bca9117a54a40f4162b388bb9367fd6439]
[key.wy = 00f1cedf20ab52eb7154b7ea1f2934a9c8292906e18a0e572002cd2f7c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000491a85b3c5e90b409f6b8d3bca9117a54a40f4162b388bb9367fd6439f1cedf20ab52eb7154b7ea1f2934a9c8292906e18a0e572002cd2f7c]
[sha = SHA-512]

# tcId = 366
# smallish r and s^-1
msg = 313233343030
result = valid
sig = 302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d1ca7a5c1aa086b2951c1ac14e005f0072fb28383973a05117f9652cce523c05ebe94991c47fecd241d0a07e86c88ab3c620eae792aba3d1]
[key.wx = 00d1ca7a5c1aa086b2951c1ac14e005f0072fb28383973a05117f9652c]
[key.wy = 00ce523c05ebe94991c47fecd241d0a07e86c88ab3c620eae792aba3d1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d1ca7a5c1aa086b2951c1ac14e005f0072fb28383973a05117f9652cce523c05ebe94991c47fecd241d0a07e86c88ab3c620eae792aba3d1]
[sha = SHA-512]

# tcId = 367
# 100-bit r and small s^-1
msg = 313233343030
result = valid
sig = 302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 043565af2a481f9390e71d7642717d0427e02e5e7de8a3c0c1ffd5f33e9474547e0d54dcaae85494c74faa23394a056e41c2839638b8523b72]
[key.wx = 3565af2a481f9390e71d7642717d0427e02e5e7de8a3c0c1ffd5f33e]
[key.wy = 009474547e0d54dcaae85494c74faa23394a056e41c2839638b8523b72]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00043565af2a481f9390e71d7642717d0427e02e5e7de8a3c0c1ffd5f33e9474547e0d54dcaae85494c74faa23394a056e41c2839638b8523b72]
[sha = SHA-512]

# tcId = 368
# small r and 100 bit s^-1
msg = 313233343030
result = valid
sig = 302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0429c694790fbd23777cfde434badcb061a326a5534264bcfe193c716c178a943f7bd4fb132565ba602358b13433a5217ac04cc035566c73f8]
[key.wx = 29c694790fbd23777cfde434badcb061a326a5534264bcfe193c716c]
[key.wy = 178a943f7bd4fb132565ba602358b13433a5217ac04cc035566c73f8]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000429c694790fbd23777cfde434badcb061a326a5534264bcfe193c716c178a943f7bd4fb132565ba602358b13433a5217ac04cc035566c73f8]
[sha = SHA-512]

# tcId = 369
# 100-bit r and s^-1
msg = 313233343030
result = valid
sig = 302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048fd43aac8556f4665fd4c13f4e151140f42a395763c5da247a398f979687d24a9fcd6b20a59451c348a6364d0ffaf0ecfe164313db6594ab]
[key.wx = 008fd43aac8556f4665fd4c13f4e151140f42a395763c5da247a398f97]
[key.wy = 009687d24a9fcd6b20a59451c348a6364d0ffaf0ecfe164313db6594ab]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048fd43aac8556f4665fd4c13f4e151140f42a395763c5da247a398f979687d24a9fcd6b20a59451c348a6364d0ffaf0ecfe164313db6594ab]
[sha = SHA-512]

# tcId = 370
# r and s^-1 are close to n
msg = 313233343030
result = valid
sig = 303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c2ae0e357a43f97549a725ae3704449051c96bf3633355c35b6eb7e96a84dfb6d4517d1de46b18786a506178724bf4ae4f9e418c75ab17ef]
[key.wx = 00c2ae0e357a43f97549a725ae3704449051c96bf3633355c35b6eb7e9]
[key.wy = 6a84dfb6d4517d1de46b18786a506178724bf4ae4f9e418c75ab17ef]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c2ae0e357a43f97549a725ae3704449051c96bf3633355c35b6eb7e96a84dfb6d4517d1de46b18786a506178724bf4ae4f9e418c75ab17ef]
[sha = SHA-512]

# tcId = 371
# s == 1
msg = 313233343030
result = valid
sig = 3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101

# tcId = 372
# s == 0
msg = 313233343030
result = invalid
sig = 3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04961617d9855f202fd600b584abe94a46674927cfdc6333c5be56ce7b89b4150d9ccdfbd77e7682ca862c0c3e96d89c918b7d3b7bbb92ff43]
[key.wx = 00961617d9855f202fd600b584abe94a46674927cfdc6333c5be56ce7b]
[key.wy = 0089b4150d9ccdfbd77e7682ca862c0c3e96d89c918b7d3b7bbb92ff43]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004961617d9855f202fd600b584abe94a46674927cfdc6333c5be56ce7b89b4150d9ccdfbd77e7682ca862c0c3e96d89c918b7d3b7bbb92ff43]
[sha = SHA-512]

# tcId = 373
# point at infinity during verify
msg = 313233343030
result = invalid
sig = 303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048db53fe4168df43ee538bc9d758b8c26fa433fb0101bcbad039585de2310dfc20835379ea406993036fd4bb0f67d14760e1eb414c32dd1f3]
[key.wx = 008db53fe4168df43ee538bc9d758b8c26fa433fb0101bcbad039585de]
[key.wy = 2310dfc20835379ea406993036fd4bb0f67d14760e1eb414c32dd1f3]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048db53fe4168df43ee538bc9d758b8c26fa433fb0101bcbad039585de2310dfc20835379ea406993036fd4bb0f67d14760e1eb414c32dd1f3]
[sha = SHA-512]

# tcId = 374
# edge case for signature malleability
msg = 313233343030
result = valid
sig = 303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 040b7fa61983e7a227f738847d457f3e8cf0a4085c312fb6dcec822570ee7434ce2ff3fbcc1d0960379876e9dd5bed28aad576eea233a44b0d]
[key.wx = 0b7fa61983e7a227f738847d457f3e8cf0a4085c312fb6dcec822570]
[key.wy = 00ee7434ce2ff3fbcc1d0960379876e9dd5bed28aad576eea233a44b0d]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00040b7fa61983e7a227f738847d457f3e8cf0a4085c312fb6dcec822570ee7434ce2ff3fbcc1d0960379876e9dd5bed28aad576eea233a44b0d]
[sha = SHA-512]

# tcId = 375
# edge case for signature malleability
msg = 313233343030
result = valid
sig = 303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0455b212919cd6886b13cd7a2556430ce442e86942f1bf6e4618ae363e795c664ae960ee1106308b7dba91240ab0c3ef8beb7d0a4d7a102a7f]
[key.wx = 55b212919cd6886b13cd7a2556430ce442e86942f1bf6e4618ae363e]
[key.wy = 795c664ae960ee1106308b7dba91240ab0c3ef8beb7d0a4d7a102a7f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000455b212919cd6886b13cd7a2556430ce442e86942f1bf6e4618ae363e795c664ae960ee1106308b7dba91240ab0c3ef8beb7d0a4d7a102a7f]
[sha = SHA-512]

# tcId = 376
# u1 == 1
msg = 313233343030
result = valid
sig = 303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c0288a63ce32263f3651198dab801c896fb9308362fc40e35959e14010d00bd1c228cfb6a5faa647387804e34fa1a7f9fcc472c05ea2eeda]
[key.wx = 00c0288a63ce32263f3651198dab801c896fb9308362fc40e35959e140]
[key.wy = 10d00bd1c228cfb6a5faa647387804e34fa1a7f9fcc472c05ea2eeda]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c0288a63ce32263f3651198dab801c896fb9308362fc40e35959e14010d00bd1c228cfb6a5faa647387804e34fa1a7f9fcc472c05ea2eeda]
[sha = SHA-512]

# tcId = 377
# u1 == n - 1
msg = 313233343030
result = valid
sig = 303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00bc07ff041506dc73a75086a4325211e696eb6b31da8ff5c2c728d38d

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 040c8e2cb5f6a903e1cccf3ac2d465f1d0dc3452237fd9e8a4df5d5341d044ca8ceecb54a1b951270971e5ab4eb226116c48c553499d1a4899]
[key.wx = 0c8e2cb5f6a903e1cccf3ac2d465f1d0dc3452237fd9e8a4df5d5341]
[key.wy = 00d044ca8ceecb54a1b951270971e5ab4eb226116c48c553499d1a4899]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00040c8e2cb5f6a903e1cccf3ac2d465f1d0dc3452237fd9e8a4df5d5341d044ca8ceecb54a1b951270971e5ab4eb226116c48c553499d1a4899]
[sha = SHA-512]

# tcId = 378
# u2 == 1
msg = 313233343030
result = valid
sig = 303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 041ff6b9901784d88b25527b3702622a2734b83d8a0fed0f740bb784e20e83ee0aa82933dcdc637a3760606a04974c2dc75f12095f8fdaf003]
[key.wx = 1ff6b9901784d88b25527b3702622a2734b83d8a0fed0f740bb784e2]
[key.wy = 0e83ee0aa82933dcdc637a3760606a04974c2dc75f12095f8fdaf003]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00041ff6b9901784d88b25527b3702622a2734b83d8a0fed0f740bb784e20e83ee0aa82933dcdc637a3760606a04974c2dc75f12095f8fdaf003]
[sha = SHA-512]

# tcId = 379
# u2 == n - 1
msg = 313233343030
result = valid
sig = 303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b21faca17b68058752d943a81f853b800562df8b2172e150953c624201c2c0f5ed3b342956cacd26f9097562d0fb0a3ddab91c5ae7e90c01]
[key.wx = 00b21faca17b68058752d943a81f853b800562df8b2172e150953c6242]
[key.wy = 01c2c0f5ed3b342956cacd26f9097562d0fb0a3ddab91c5ae7e90c01]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b21faca17b68058752d943a81f853b800562df8b2172e150953c624201c2c0f5ed3b342956cacd26f9097562d0fb0a3ddab91c5ae7e90c01]
[sha = SHA-512]

# tcId = 380
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c152aafea3a8612ec83a7dc9448e6600ae6a772d75ad2caf19f9390e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04f49278419e4f506889b0168b1fce1f87ee5b61efa0e73c7833eeb29cb1b334f81be8f05f3b2e98d38b030cff57947b96135ec4465c5e53f3]
[key.wx = 00f49278419e4f506889b0168b1fce1f87ee5b61efa0e73c7833eeb29c]
[key.wy = 00b1b334f81be8f05f3b2e98d38b030cff57947b96135ec4465c5e53f3]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004f49278419e4f506889b0168b1fce1f87ee5b61efa0e73c7833eeb29cb1b334f81be8f05f3b2e98d38b030cff57947b96135ec4465c5e53f3]
[sha = SHA-512]

# tcId = 381
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c4e158ef86cc53054f1635c74e65508206048929315e097a59f1519e2

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0475c6a886e22bc04b996d4a19575ce0c6686b449b6e05ef1301bd8ba233ab29f65df2d4144da2b21e90359a064765c95e325bb7e54ca28e40]
[key.wx = 75c6a886e22bc04b996d4a19575ce0c6686b449b6e05ef1301bd8ba2]
[key.wy = 33ab29f65df2d4144da2b21e90359a064765c95e325bb7e54ca28e40]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000475c6a886e22bc04b996d4a19575ce0c6686b449b6e05ef1301bd8ba233ab29f65df2d4144da2b21e90359a064765c95e325bb7e54ca28e40]
[sha = SHA-512]

# tcId = 382
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00e2ac0b24512e84f6fb015620d689d30d14736cf00c18838753c3814f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04f554014cc14f319c18f5fa6cd739249075ff35ba3b2afdab5329ef0fd2c501f25a704addbd85c0e022748956e5998d99c387fbfd343c89e0]
[key.wx = 00f554014cc14f319c18f5fa6cd739249075ff35ba3b2afdab5329ef0f]
[key.wy = 00d2c501f25a704addbd85c0e022748956e5998d99c387fbfd343c89e0]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004f554014cc14f319c18f5fa6cd739249075ff35ba3b2afdab5329ef0fd2c501f25a704addbd85c0e022748956e5998d99c387fbfd343c89e0]
[sha = SHA-512]

# tcId = 383
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c5221f3c2de0c6fbc07ff04150679b57f57512b814f413aebafe731

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04bcfa8db704aca56feb23bd4b4049213233aa652045a0a81a2e0da64c091b359f7be7ae00a0e9777d9510f847430b5dfda878e66d4fb0d62f]
[key.wx = 00bcfa8db704aca56feb23bd4b4049213233aa652045a0a81a2e0da64c]
[key.wy = 091b359f7be7ae00a0e9777d9510f847430b5dfda878e66d4fb0d62f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004bcfa8db704aca56feb23bd4b4049213233aa652045a0a81a2e0da64c091b359f7be7ae00a0e9777d9510f847430b5dfda878e66d4fb0d62f]
[sha = SHA-512]

# tcId = 384
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c221f3c2de0c6fbc07ff041506dc71b5a312063d87beb4c30c289210f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 049fd4d828ae98056be58fa69eaf9cde98ca0ed9b415d6463fa1864d9fb2a5e41f10e8789450217daafd259f204aed87b0e26100f43f7c5bad]
[key.wx = 009fd4d828ae98056be58fa69eaf9cde98ca0ed9b415d6463fa1864d9f]
[key.wy = 00b2a5e41f10e8789450217daafd259f204aed87b0e26100f43f7c5bad]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00049fd4d828ae98056be58fa69eaf9cde98ca0ed9b415d6463fa1864d9fb2a5e41f10e8789450217daafd259f204aed87b0e26100f43f7c5bad]
[sha = SHA-512]

# tcId = 385
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c443e785bc18df780ffe082a0db8e36b46240c7b0f7d698618512421e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 046123a33969f2e036fc27885f55755d391cb0c2d3fafb0c4056c1995da03bb490047e88fe7e608912a6205b65f950a8a0a360362d3339e62c]
[key.wx = 6123a33969f2e036fc27885f55755d391cb0c2d3fafb0c4056c1995d]
[key.wy = 00a03bb490047e88fe7e608912a6205b65f950a8a0a360362d3339e62c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00046123a33969f2e036fc27885f55755d391cb0c2d3fafb0c4056c1995da03bb490047e88fe7e608912a6205b65f950a8a0a360362d3339e62c]
[sha = SHA-512]

# tcId = 386
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c2de0c6fbc07ff041506dc73a74fd50136878b7e1341521b2f880b19

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04a10b7aa7785b2f2791b1d4c43e127aab5669612d740b38abaa0d306ec178f216fad379ad80baa0eac57bf9a56d446d685576371b74762382]
[key.wx = 00a10b7aa7785b2f2791b1d4c43e127aab5669612d740b38abaa0d306e]
[key.wy = 00c178f216fad379ad80baa0eac57bf9a56d446d685576371b74762382]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004a10b7aa7785b2f2791b1d4c43e127aab5669612d740b38abaa0d306ec178f216fad379ad80baa0eac57bf9a56d446d685576371b74762382]
[sha = SHA-512]

# tcId = 387
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d009f56aa80ae2bcf689be2c11b5db7e3a28983b4a7590692edcf5f8db6

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e012c23c6867e9553313d0179e9db953de7c368cdb59abe05f1c52bad352a57bb59c45159352c114eeb696ec3b79caa835ef5c2ae71ddcfa]
[key.wx = 00e012c23c6867e9553313d0179e9db953de7c368cdb59abe05f1c52ba]
[key.wy = 00d352a57bb59c45159352c114eeb696ec3b79caa835ef5c2ae71ddcfa]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e012c23c6867e9553313d0179e9db953de7c368cdb59abe05f1c52bad352a57bb59c45159352c114eeb696ec3b79caa835ef5c2ae71ddcfa]
[sha = SHA-512]

# tcId = 388
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3ead55015c579ed137c58236bb70b0a2324e79109e2ffc964262f12f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b9ccd7f0f3594954aa729bda4be883e107e7f1226465b64c2ca7105789829d787016c5c118d3ba3317a2da0a0daaf56d3004c10962333a9f]
[key.wx = 00b9ccd7f0f3594954aa729bda4be883e107e7f1226465b64c2ca71057]
[key.wy = 0089829d787016c5c118d3ba3317a2da0a0daaf56d3004c10962333a9f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b9ccd7f0f3594954aa729bda4be883e107e7f1226465b64c2ca7105789829d787016c5c118d3ba3317a2da0a0daaf56d3004c10962333a9f]
[sha = SHA-512]

# tcId = 389
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00de03ff820a836e39d3a8435219289444bbd22db7f7368f8411c27ee5

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04321a17de024fe89c1864e128b9e0af3e6b48800a70d6e802b8b6dffeb1a8ae96911ddbdeb83948a992b1b0fe316679c64814b6a45ec56fe9]
[key.wx = 321a17de024fe89c1864e128b9e0af3e6b48800a70d6e802b8b6dffe]
[key.wy = 00b1a8ae96911ddbdeb83948a992b1b0fe316679c64814b6a45ec56fe9]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004321a17de024fe89c1864e128b9e0af3e6b48800a70d6e802b8b6dffeb1a8ae96911ddbdeb83948a992b1b0fe316679c64814b6a45ec56fe9]
[sha = SHA-512]

# tcId = 390
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00f15605922897427b7d80ab106b4474d7fa962e970ffad666580fd5c6

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0408842f19b114d16be27bb4b6971377ed6b1d0915e133a9ebf01674ee4c97738b6912ff71553c4a747c782eddd9d2a20fbeae38864d217859]
[key.wx = 08842f19b114d16be27bb4b6971377ed6b1d0915e133a9ebf01674ee]
[key.wy = 4c97738b6912ff71553c4a747c782eddd9d2a20fbeae38864d217859]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000408842f19b114d16be27bb4b6971377ed6b1d0915e133a9ebf01674ee4c97738b6912ff71553c4a747c782eddd9d2a20fbeae38864d217859]
[sha = SHA-512]

# tcId = 391
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0484d651596fd2348f1bb5c8ae9d22c8b21c4f7509240b609abad5cc243196b67b4cfaffaf0dce25ab00bfeaa1a64821332efa6dedd87cc9e7]
[key.wx = 0084d651596fd2348f1bb5c8ae9d22c8b21c4f7509240b609abad5cc24]
[key.wy = 3196b67b4cfaffaf0dce25ab00bfeaa1a64821332efa6dedd87cc9e7]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000484d651596fd2348f1bb5c8ae9d22c8b21c4f7509240b609abad5cc243196b67b4cfaffaf0dce25ab00bfeaa1a64821332efa6dedd87cc9e7]
[sha = SHA-512]

# tcId = 392
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048fbe39e75bc4fd8a15e4b52e4bbebe2047d54385a7117e17a4d0b2b207abdb40824538e5787c718d6548583f523f6b5bbfa239a7f622c8a0]
[key.wx = 008fbe39e75bc4fd8a15e4b52e4bbebe2047d54385a7117e17a4d0b2b2]
[key.wy = 07abdb40824538e5787c718d6548583f523f6b5bbfa239a7f622c8a0]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048fbe39e75bc4fd8a15e4b52e4bbebe2047d54385a7117e17a4d0b2b207abdb40824538e5787c718d6548583f523f6b5bbfa239a7f622c8a0]
[sha = SHA-512]

# tcId = 393
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c336b340bc99d46c2c52df5428b6a0c4eb2da76c423530f767cc7652f3ab9981bd05d2955123935a379cbb2d4361a17d19878673e1e17dcc]
[key.wx = 00c336b340bc99d46c2c52df5428b6a0c4eb2da76c423530f767cc7652]
[key.wy = 00f3ab9981bd05d2955123935a379cbb2d4361a17d19878673e1e17dcc]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c336b340bc99d46c2c52df5428b6a0c4eb2da76c423530f767cc7652f3ab9981bd05d2955123935a379cbb2d4361a17d19878673e1e17dcc]
[sha = SHA-512]

# tcId = 394
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04816fdcf370827e3f7771564e1aa73ed73e62556deadad89711cef663edcda0ea42235f4c9a8c13f787351ffe5ceb32f15fc0ccb24e0a409c]
[key.wx = 00816fdcf370827e3f7771564e1aa73ed73e62556deadad89711cef663]
[key.wy = 00edcda0ea42235f4c9a8c13f787351ffe5ceb32f15fc0ccb24e0a409c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004816fdcf370827e3f7771564e1aa73ed73e62556deadad89711cef663edcda0ea42235f4c9a8c13f787351ffe5ceb32f15fc0ccb24e0a409c]
[sha = SHA-512]

# tcId = 395
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 046429d2b7b07ab0d5ea352902df0efc036d7270a0a6ed39f635d04f394f7932883bc45394151324aab26ae29bbd7385fa6a42c3db84432897]
[key.wx = 6429d2b7b07ab0d5ea352902df0efc036d7270a0a6ed39f635d04f39]
[key.wy = 4f7932883bc45394151324aab26ae29bbd7385fa6a42c3db84432897]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00046429d2b7b07ab0d5ea352902df0efc036d7270a0a6ed39f635d04f394f7932883bc45394151324aab26ae29bbd7385fa6a42c3db84432897]
[sha = SHA-512]

# tcId = 396
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04288f38fd77dd1603ff0275cb11cba280ae3408affa6a760f396f1a1ec84ca6fd772c6ac6cc523cc72c2e7e95eb6a36a66b5cca5a58ba078a]
[key.wx = 288f38fd77dd1603ff0275cb11cba280ae3408affa6a760f396f1a1e]
[key.wy = 00c84ca6fd772c6ac6cc523cc72c2e7e95eb6a36a66b5cca5a58ba078a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004288f38fd77dd1603ff0275cb11cba280ae3408affa6a760f396f1a1ec84ca6fd772c6ac6cc523cc72c2e7e95eb6a36a66b5cca5a58ba078a]
[sha = SHA-512]

# tcId = 397
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c769c138f9d71ffff113273b71a4afde4f9996a1c4be658a3903cf7f430e512b868b37bb96bc17a09b0ab01b262f2e23f34f00418f6b63d6]
[key.wx = 00c769c138f9d71ffff113273b71a4afde4f9996a1c4be658a3903cf7f]
[key.wy = 430e512b868b37bb96bc17a09b0ab01b262f2e23f34f00418f6b63d6]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c769c138f9d71ffff113273b71a4afde4f9996a1c4be658a3903cf7f430e512b868b37bb96bc17a09b0ab01b262f2e23f34f00418f6b63d6]
[sha = SHA-512]

# tcId = 398
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0475f007c11b93e6f46e9a815cb765990a8305d3ad8d22c76fe6b257cc71b5c1951b5d464c66df7c290cf0a4f156bbf52f1e41a79dc63abce5]
[key.wx = 75f007c11b93e6f46e9a815cb765990a8305d3ad8d22c76fe6b257cc]
[key.wy = 71b5c1951b5d464c66df7c290cf0a4f156bbf52f1e41a79dc63abce5]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000475f007c11b93e6f46e9a815cb765990a8305d3ad8d22c76fe6b257cc71b5c1951b5d464c66df7c290cf0a4f156bbf52f1e41a79dc63abce5]
[sha = SHA-512]

# tcId = 399
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 041255fb94a0f20e6faa2505c394cc3c39f07def4107127dffc4dacb6eea73c1044544a1496560bd1b049ff615e68ae0d483220327569884e1]
[key.wx = 1255fb94a0f20e6faa2505c394cc3c39f07def4107127dffc4dacb6e]
[key.wy = 00ea73c1044544a1496560bd1b049ff615e68ae0d483220327569884e1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00041255fb94a0f20e6faa2505c394cc3c39f07def4107127dffc4dacb6eea73c1044544a1496560bd1b049ff615e68ae0d483220327569884e1]
[sha = SHA-512]

# tcId = 400
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04f656a632a0804cf688446b261208f793373c5ff4454bd1e0a882113f30a25d6f586e02dd4dcbf73d96af3e483b7acb5f8f4c06450dec1982]
[key.wx = 00f656a632a0804cf688446b261208f793373c5ff4454bd1e0a882113f]
[key.wy = 30a25d6f586e02dd4dcbf73d96af3e483b7acb5f8f4c06450dec1982]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004f656a632a0804cf688446b261208f793373c5ff4454bd1e0a882113f30a25d6f586e02dd4dcbf73d96af3e483b7acb5f8f4c06450dec1982]
[sha = SHA-512]

# tcId = 401
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3f074d20e1da7232d279461732bc1bae0c5416ab9d696308622e7ffe8]
[key.wx = 008fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3]
[key.wy = 00f074d20e1da7232d279461732bc1bae0c5416ab9d696308622e7ffe8]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3f074d20e1da7232d279461732bc1bae0c5416ab9d696308622e7ffe8]
[sha = SHA-512]

# tcId = 402
# point duplication during verification
msg = 313233343030
result = valid
sig = 303e021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021d00ec0ce3fa725c1027475a5f5bf4ee980de61c3b4875afe8b654b24ee2
flags = PointDuplication

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a30f8b2df1e258dcd2d86b9e8cd43e451e3abe95462969cf79dd180019]
[key.wx = 008fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3]
[key.wy = 0f8b2df1e258dcd2d86b9e8cd43e451e3abe95462969cf79dd180019]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a30f8b2df1e258dcd2d86b9e8cd43e451e3abe95462969cf79dd180019]
[sha = SHA-512]

# tcId = 403
# duplication bug
msg = 313233343030
result = invalid
sig = 303e021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021d00ec0ce3fa725c1027475a5f5bf4ee980de61c3b4875afe8b654b24ee2
flags = PointDuplication

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e5462d3a838d4a14de96a7b0b1071eb622ae6e71ede8f95ff01c2121368e3a90d8584e194616d3211a7541f6a0960339cab28e8bfd6b1dfd]
[key.wx = 00e5462d3a838d4a14de96a7b0b1071eb622ae6e71ede8f95ff01c2121]
[key.wy = 368e3a90d8584e194616d3211a7541f6a0960339cab28e8bfd6b1dfd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e5462d3a838d4a14de96a7b0b1071eb622ae6e71ede8f95ff01c2121368e3a90d8584e194616d3211a7541f6a0960339cab28e8bfd6b1dfd]
[sha = SHA-512]

# tcId = 404
# comparison with point at infinity 
msg = 313233343030
result = invalid
sig = 303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 045d97670c1f121f7f1ba541505609f20143b312a7bb49d376690e1831c1b4567141a7b534e21bd2f706ae034169ab9c3f8536147904de8c5f]
[key.wx = 5d97670c1f121f7f1ba541505609f20143b312a7bb49d376690e1831]
[key.wy = 00c1b4567141a7b534e21bd2f706ae034169ab9c3f8536147904de8c5f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00045d97670c1f121f7f1ba541505609f20143b312a7bb49d376690e1831c1b4567141a7b534e21bd2f706ae034169ab9c3f8536147904de8c5f]
[sha = SHA-512]

# tcId = 405
# extreme value for k and edgecase s
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d2675278da2d7daa8373dd63b7aa46cb14766571c2d8098b83a102a5699b572d4b951497418a376930022d48fe59966b158fa08340e24b98]
[key.wx = 00d2675278da2d7daa8373dd63b7aa46cb14766571c2d8098b83a102a5]
[key.wy = 699b572d4b951497418a376930022d48fe59966b158fa08340e24b98]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d2675278da2d7daa8373dd63b7aa46cb14766571c2d8098b83a102a5699b572d4b951497418a376930022d48fe59966b158fa08340e24b98]
[sha = SHA-512]

# tcId = 406
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 045a5cd1162388348734dae20e2235ae2c464adef0a196f9aaf02482ca2ae94e8b9a024375036429b632ab485e02c5a9665b289b8a47bade8f]
[key.wx = 5a5cd1162388348734dae20e2235ae2c464adef0a196f9aaf02482ca]
[key.wy = 2ae94e8b9a024375036429b632ab485e02c5a9665b289b8a47bade8f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00045a5cd1162388348734dae20e2235ae2c464adef0a196f9aaf02482ca2ae94e8b9a024375036429b632ab485e02c5a9665b289b8a47bade8f]
[sha = SHA-512]

# tcId = 407
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04cacd93eb11a821de3d882bab7411e7c77f23c08da174189cc987dc41716fe378ab842161bc16def6e037d4ba9d30d8cb41ad30cf0656e50b]
[key.wx = 00cacd93eb11a821de3d882bab7411e7c77f23c08da174189cc987dc41]
[key.wy = 716fe378ab842161bc16def6e037d4ba9d30d8cb41ad30cf0656e50b]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004cacd93eb11a821de3d882bab7411e7c77f23c08da174189cc987dc41716fe378ab842161bc16def6e037d4ba9d30d8cb41ad30cf0656e50b]
[sha = SHA-512]

# tcId = 408
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04cf46960060453e55577f1bee6a9c4709e7cdcba45ca8020bb3536931ea4ec33309213864a1318aee0a86d8b6f0c1b9741cd6bd5dea4f4066]
[key.wx = 00cf46960060453e55577f1bee6a9c4709e7cdcba45ca8020bb3536931]
[key.wy = 00ea4ec33309213864a1318aee0a86d8b6f0c1b9741cd6bd5dea4f4066]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004cf46960060453e55577f1bee6a9c4709e7cdcba45ca8020bb3536931ea4ec33309213864a1318aee0a86d8b6f0c1b9741cd6bd5dea4f4066]
[sha = SHA-512]

# tcId = 409
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0462f4eaf3797bdc3d5d8cfaa07b5af7060e131b183ca4eded4819e561bff3eadd7b55db2dc01bd20569e6c47c9212f9b2d6793795b51e4f6c]
[key.wx = 62f4eaf3797bdc3d5d8cfaa07b5af7060e131b183ca4eded4819e561]
[key.wy = 00bff3eadd7b55db2dc01bd20569e6c47c9212f9b2d6793795b51e4f6c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000462f4eaf3797bdc3d5d8cfaa07b5af7060e131b183ca4eded4819e561bff3eadd7b55db2dc01bd20569e6c47c9212f9b2d6793795b51e4f6c]
[sha = SHA-512]

# tcId = 410
# extreme value for k
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c4a4bf5ae0138587f50ab7a2c336a430527a86f59f9765c2f3f5488df9419bf9df5f121de3a32db17b49c72b606b2be5ce56acb565cc12b7]
[key.wx = 00c4a4bf5ae0138587f50ab7a2c336a430527a86f59f9765c2f3f5488d]
[key.wy = 00f9419bf9df5f121de3a32db17b49c72b606b2be5ce56acb565cc12b7]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c4a4bf5ae0138587f50ab7a2c336a430527a86f59f9765c2f3f5488df9419bf9df5f121de3a32db17b49c72b606b2be5ce56acb565cc12b7]
[sha = SHA-512]

# tcId = 411
# extreme value for k and edgecase s
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e7cb5ae54dbe619ab5069f14566236b3c6b0b44f1c4c531e66d89b3e64be7fdc18789629dfddf7158f8ff27abd553bfac3f7c874bccdc31b]
[key.wx = 00e7cb5ae54dbe619ab5069f14566236b3c6b0b44f1c4c531e66d89b3e]
[key.wy = 64be7fdc18789629dfddf7158f8ff27abd553bfac3f7c874bccdc31b]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e7cb5ae54dbe619ab5069f14566236b3c6b0b44f1c4c531e66d89b3e64be7fdc18789629dfddf7158f8ff27abd553bfac3f7c874bccdc31b]
[sha = SHA-512]

# tcId = 412
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0430db5d8279319cf5a3b6768a0c5e5c84752f6314f735d63f6c5650cdd32fb54f74d4a5088e6774a13201683642790d2e69e55e4f47612934]
[key.wx = 30db5d8279319cf5a3b6768a0c5e5c84752f6314f735d63f6c5650cd]
[key.wy = 00d32fb54f74d4a5088e6774a13201683642790d2e69e55e4f47612934]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000430db5d8279319cf5a3b6768a0c5e5c84752f6314f735d63f6c5650cdd32fb54f74d4a5088e6774a13201683642790d2e69e55e4f47612934]
[sha = SHA-512]

# tcId = 413
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 047db27da4d67a2de0c78815809719bdf6976332c67ef0f3827df4adc22ab37aec2eed0d5e67acfd6a195f21032d9af71ce73e120fdda29f1a]
[key.wx = 7db27da4d67a2de0c78815809719bdf6976332c67ef0f3827df4adc2]
[key.wy = 2ab37aec2eed0d5e67acfd6a195f21032d9af71ce73e120fdda29f1a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00047db27da4d67a2de0c78815809719bdf6976332c67ef0f3827df4adc22ab37aec2eed0d5e67acfd6a195f21032d9af71ce73e120fdda29f1a]
[sha = SHA-512]

# tcId = 414
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d1c19d46b517bb3bd7bdf074ff975c0dbd2bde10d1ad217e58ebc8c55ac898c040a185804ddb032b48103d6c8d12043d3a4fec93aba7a6d7]
[key.wx = 00d1c19d46b517bb3bd7bdf074ff975c0dbd2bde10d1ad217e58ebc8c5]
[key.wy = 5ac898c040a185804ddb032b48103d6c8d12043d3a4fec93aba7a6d7]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d1c19d46b517bb3bd7bdf074ff975c0dbd2bde10d1ad217e58ebc8c55ac898c040a185804ddb032b48103d6c8d12043d3a4fec93aba7a6d7]
[sha = SHA-512]

# tcId = 415
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d95ac96ae9dbfb80911862e00a4cadbcb2359f499b53be007f0711c093d3da931acbb9242800dc521695b4f19ff2dffc3613f40bdb15c3cd]
[key.wx = 00d95ac96ae9dbfb80911862e00a4cadbcb2359f499b53be007f0711c0]
[key.wy = 0093d3da931acbb9242800dc521695b4f19ff2dffc3613f40bdb15c3cd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d95ac96ae9dbfb80911862e00a4cadbcb2359f499b53be007f0711c093d3da931acbb9242800dc521695b4f19ff2dffc3613f40bdb15c3cd]
[sha = SHA-512]

# tcId = 416
# extreme value for k
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34]
[key.wx = 00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21]
[key.wy = 00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34]
[sha = SHA-512]

# tcId = 417
# testing point duplication
msg = 313233343030
result = invalid
sig = 303c021c43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0021c249249249249249249249249249227ce201a6b76951f982e7ae89851

# tcId = 418
# testing point duplication
msg = 313233343030
result = invalid
sig = 303d021d00bc07ff041506dc73a75086a4325211e696eb6b31da8ff5c2c728d38d021c249249249249249249249249249227ce201a6b76951f982e7ae89851

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd]
[key.wx = 00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21]
[key.wy = 42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd]
[sha = SHA-512]

# tcId = 419
# testing point duplication
msg = 313233343030
result = invalid
sig = 303c021c43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0021c249249249249249249249249249227ce201a6b76951f982e7ae89851

# tcId = 420
# testing point duplication
msg = 313233343030
result = invalid
sig = 303d021d00bc07ff041506dc73a75086a4325211e696eb6b31da8ff5c2c728d38d021c249249249249249249249249249227ce201a6b76951f982e7ae89851

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176]
[key.wx = 4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466]
[key.wy = 00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176]
[sha = SHA-512]

# tcId = 421
# pseudorandom signature
msg = 
result = valid
sig = 303e021d00f72915d6d916014279616186869a01228fcd9f1b4078353018b399ab021d00b67f2b91eeeb910381f5b461a4a39c642aea4792013d4eb63da1832b

# tcId = 422
# pseudorandom signature
msg = 4d7367
result = valid
sig = 303e021d00a5d179c336ccdc760dfddd913cdf8ea468d0f4686f7b2d3825698ed7021d00a77f12060a4d1b94b0d1c443eae3ad6e21b7eacfdf6fbf39a2b29658

# tcId = 423
# pseudorandom signature
msg = 313233343030
result = valid
sig = 303e021d00b7c65dce56abe24fb4592ece5ac1e6ee8353431f7452409add736884021d00e5fe5db7988931026b937dc4ef983fe446ca134d29b94ac777cde317

# tcId = 424
# pseudorandom signature
msg = 0000000000000000000000000000000000000000
result = valid
sig = 303d021c05c563d3a4bad874e4610adfa57777a59f995bfa06ef97bf125a4988021d0097ed68f546cf4bb4998524c18356f3af162d2bf2744be93357bc4b4b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000]
[key.wx = 00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf]
[key.wy = 008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000]
[sha = SHA-512]

# tcId = 425
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303e021d00c7a6f358b7d93815189ae5d2c3ab4d4e05f43176a52dd4fc5b48a34a021d00a2458512bb8dbe6f1bd6eb01d2d77d5624e8547bf87d85fc731c0c86

# tcId = 426
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303d021c5f56ca587d16664a20dad13df85a475978e5cee81a8d0f49faaf6158021d00b64ef59d79461fe1a09a5864907435f70bd75f183afb11903f560b7c

# tcId = 427
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303e021d00dd94f5b56e9947d007e7c8efd894a5c882f1d0b5dd56c32b5b266521021d00fbc883741bd27c59958ae17ba6e4a41ad1edeca9a3ba31c8f233b5ac

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff]
[key.wx = 00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1]
[key.wy = 73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff]
[sha = SHA-512]

# tcId = 428
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303e021d008071e6682c6e8a32706dc7e411503946546b31fff27dcce188ae389f021d00dc396c797d44edf794432d1da091f8c762974d8ce1f06e08ca013622

# tcId = 429
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303c021c791624e5f234b8950d509d0b456ef6fa778b19dccd609d496b62a211021c6c51e846fa53d03d42f798e6bb90954f9a48c1794b47e84ac97b460a

# tcId = 430
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303c021b34befa1d25b756ce76b383a6e8753741c12a59266c2c7921ff6e8b021d00bc44e3823e4d807cbc92fa786a89e62a4b217b5fb0c0f1865d4a7e43

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0]
[key.wx = 26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000]
[key.wy = 00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0]
[sha = SHA-512]

# tcId = 431
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303c021c224a38e733ebd3fac274ecc50ecef2e7c3189be2b9d093a8dcc6fa3a021c134fa5a4f923d296b3c6dd4683d249ccf0ad272890e4149c9a0d7415

# tcId = 432
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303d021c338d07d990879ad844e24c1788e362269d8aca70500357d385768227021d00f745cc4ebaaf1cd42830026a66e5b95564cdbee5edf853bb2cc91259

# tcId = 433
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303c021c689fce4b33d8212a663640a1ae0efaa7a7d7711beba719374fe634ee021c04bd9981fa52293063076f0fd70fc31875d580ef94f020d2f95440e0

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd]
[key.wx = 00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff]
[key.wy = 41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd]
[sha = SHA-512]

# tcId = 434
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303c021c2a4287e01510e7fb5fed2e1ccc3f2a6929cf7d03850e49d7ae8a504a021c355c3915f3fa9637dc8001438a8c04e15d14934cabd430feb0cb5ba5

# tcId = 435
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303d021d00b5bf795a38adb052b401468ffcab81103d2d9fca2e15b8d08ab98ce8021c5ec0d2c6aec71888c941af324c7272bec192abb292f9df82a24e8a41

# tcId = 436
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303d021c100ed07f467133bf10917f7a15ab2bfda519bdbc2653b95955e22211021d00b38a081f7c2e2b775d1da868d0381c09ba1559c9613b5be7159363ad

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5]
[key.wx = 15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a]
[key.wy = 762d28f1fdc219184f81681fbff566d465b5f1f31e872df5]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5]
[sha = SHA-512]

# tcId = 437
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303c021c54e6add8ac910e52c6228fe3980d8f586218334d8d859ba9a3329917021c5836cc79ec88519eab4a6b2614c501628c9fee32fbafd93e32158409

# tcId = 438
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303c021c1230d5409f379584b4d548b7bccba64baf81d512a9f2e6398c4e3a66021c1937a298f8cbdfa85b8e6fcf0a12be4966d80270cade85a0c37ee6f3

# tcId = 439
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00862f43b044fb32adb45e00378ba083ae761c84452054f17b1341bf5b021d0095d8d8e5e3a6cc2b0a06c792252ca11a642257721831578520f96b9e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c]
[key.wx = 15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a]
[key.wy = 00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c]
[sha = SHA-512]

# tcId = 440
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021d00cb5cabb1ca01b847a6bc70558d1e5d3a204d1741bbe800f4b159af35021c3580cc85f218394130bddf1c4eac04fe96f59f14fb436686950398be

# tcId = 441
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021d00c9d83dc04cf4ee89c405045d0fd1d704f627ca5bbe350f40b826bbc1021c74fedc9e55045e9759f2124460fdfb991dc620cfee6effc0b4adaa9e

# tcId = 442
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c46dd65b6e7f10c0841841b01033a5befd3a0e78c85f1f390bb3cdf25021d00f33acea3d47cf0dd5273735b004104f6512ed641052509422c0325a7

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1]
[key.wx = 00f7e4713d085112112c37cdf4601ff688da796016b71a727a]
[key.wy = 00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1]
[sha = SHA-512]

# tcId = 443
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00ddb4a7e400a1e98118f474722da3f421f65a76eec61f4f7b699faf07021d00db80cba199859cdfe916d6ab3deb91d76aaf0ed554c8f9ed7e5aa59d

# tcId = 444
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021c4c260b546280604e4c80384721c9e803ef704e7fb70168e6730fc1f3021d00a8aceae219ac25c9f04231b4e0c171413db1d26df1c1e8430062eb2b

# tcId = 445
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00f4098d2c0240e78fceabb0183df0b39e7ad3e7f5d6da1587fa09853c021d00d42412b2abaa614c95eb11f9b9346282ce3a1c93aac35ce7aa372f4a

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e]
[key.wx = 00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725]
[key.wy = 0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e]
[sha = SHA-512]

# tcId = 446
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c48ddc497f9a4732c677e46c0e2bdabec54fc9d27e46ab595056db4d9021d00b8219ebbfaebc2fe4311efab0c35d4392751351bcc1971e8d01941e4

# tcId = 447
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021d00e1abaf51d27a6d7d4c9b28078325cac2d7ce3d5403916c68903760b7021c2c45a99e2770f782fee5ca1d713eaecf07e62d53c64b7cf93de9900d

# tcId = 448
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021d00868cd127c99e1149f7fc8d878cdfa986b62e99addea281149611ff15021c16e5953820135b7d462ce5434ef85920e973eec9e4d14d7cb3cc2a3f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc]
[key.wx = 00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1]
[key.wy = 0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc]
[sha = SHA-512]

# tcId = 449
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00a375929718ec4e6ada9c9370c51df6bdaee7ebab2a70675d42a0b6b3021d009eaf4802efaf7ca082ffbf5ed774af43792d9b3fd711c6b1c36112ff

# tcId = 450
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021d00d97b32f3bf8bc11ec2672dd6320418beeed99527a63fe4c52199ec61021c68dd9006b03319ccbe651d0bdaf84c63356f03cb007a6865ee3e0206

# tcId = 451
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021d008ee5794dc2e66f2584910ea1d8361e5b53db535adcf5c1c35e128309021c5d1d8b9b996c0a488e05af14421b86e9841f0cba706027fc827d4d95

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945]
[key.wx = 00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1]
[key.wy = 00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945]
[sha = SHA-512]

# tcId = 452
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c7999727c0cc02d88ef274012a762afcbb19e7fce19091a02acd00564021d00dbfacf67999f22c499d48a60a6fe4bbb746199c29957a1ec7a0900e0

# tcId = 453
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303c021c5797c21c0162e42f69693c6c0244dfdf9218c01e9235760177b61a54021c5452c887b27fb342a8a00d27579c7195dddb73df399233ed0dea567b

# tcId = 454
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c0eb9dc5d67bb0d4009544f8654977907dfe770e7fae4571d31d7b4fa021d00ab5cda53e868bff5198be4be3681b186cb0c1396d272c71f093f8b12

