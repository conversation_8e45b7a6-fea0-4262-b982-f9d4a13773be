# RealityTap 低通FIR滤波器系数深度分析

## 概述

本文档深入分析了RealityTap振动算法中低通FIR滤波器系数的设计原理、实现机制和技术特点。这些滤波器系数是实现跨平台一致触觉体验的关键技术组件。

## 1. 滤波器系数设计概览

### 1.1 四种采样率对应的滤波器配置

| 采样率 | 滤波器阶数 | 截止频率 | 群延迟 | 应用场景 | 计算复杂度 |
|--------|------------|----------|--------|----------|------------|
| 6kHz   | 6阶        | 1.8kHz   | 0.417ms | 低功耗设备、基础触觉反馈 | 最低 |
| 8kHz   | 7阶        | 2.4kHz   | 0.375ms | 标准触觉反馈应用 | 低 |
| 12kHz  | 11阶       | 3.6kHz   | 0.417ms | 高质量触觉反馈 | 中等 |
| 24kHz  | 21阶       | 7.2kHz   | 0.417ms | 专业级触觉反馈、高保真音频 | 最高(504K ops/sec) |

### 1.2 核心设计原理

所有滤波器都遵循**30% Nyquist频率**作为截止频率的设计原则：

```
截止频率 = 采样率 × 0.15
```

这确保了在不同采样率下，滤波器的相对频率响应特性保持一致，从而在不同设备上提供相同的触觉感知效果。

## 2. 滤波器系数定义

### 2.1 6kHz采样率滤波器
```cpp
/**
 * @brief 6kHz采样率低通FIR滤波器系数
 * @order 6阶
 * @type 线性相位、对称、归一化低通滤波器
 * @cutoff_freq 约1.8kHz (30% Nyquist频率)
 * @group_delay 2.5样本 = 0.417ms
 * @applications 低功耗设备、基础触觉反馈
 */
static constexpr double LOWPASS_FIR_COEFFS_6K[6] = {
    0.022378127, 0.135783019, 0.341838854,
    0.341838854, 0.135783019, 0.022378127
};
```

### 2.2 8kHz采样率滤波器
```cpp
/**
 * @brief 8kHz采样率低通FIR滤波器系数
 * @order 7阶
 * @cutoff_freq 约2.4kHz (30% Nyquist频率)
 * @group_delay 3.0样本 = 0.375ms
 * @applications 标准触觉反馈应用
 */
static constexpr double LOWPASS_FIR_COEFFS_8K[7] = {
    0.01970142, 0.087645605, 0.235637185, 0.314031581,
    0.235637185, 0.087645605, 0.01970142
};
```

### 2.3 12kHz采样率滤波器
```cpp
/**
 * @brief 12kHz采样率低通FIR滤波器系数
 * @order 11阶
 * @cutoff_freq 约3.6kHz (30% Nyquist频率)
 * @group_delay 5.0样本 = 0.417ms
 * @applications 高质量触觉反馈
 */
static constexpr double LOWPASS_FIR_COEFFS_12K[11] = {
    0.011312455, 0.026600556, 0.068640206, 0.124827879, 0.172804298,
    0.191629212, 0.172804298, 0.124827879, 0.068640206, 0.026600556, 0.011312455
};
```

### 2.4 24kHz采样率滤波器
```cpp
/**
 * @brief 24kHz采样率低通FIR滤波器系数
 * @order 21阶
 * @cutoff_freq 约7.2kHz (30% Nyquist频率)
 * @group_delay 10.0样本 = 0.417ms
 * @applications 专业级触觉反馈、高保真音频
 * @note 最高质量但计算复杂度最大(504K ops/sec)
 */
static constexpr double LOWPASS_FIR_COEFFS_24K[21] = {
    0.0056875, 0.0077454, 0.01337382, 0.02249089, 0.03450987,
    0.04839035, 0.06275905, 0.07608441, 0.0868799, 0.09390659, 0.09634441,
    0.09390659, 0.0868799, 0.07608441, 0.06275905, 0.04839035,
    0.03450987, 0.02249089, 0.01337382, 0.0077454, 0.0056875
};
```

## 3. 代码实现机制

### 3.1 滤波器初始化

在`DispEqualiza.cpp`中，根据采样率类型选择对应的滤波器系数：

```cpp
switch (params.samplingRate) {
    case SamplingRateType::SAMPLING_6KHZ: {
        SAMPLING_INTERVAL = 0.00016666667;  // 1/6000 秒
        FILTER_ORDER = 6;
        FILTER_COEFFICIENTS = (double *)LOWPASS_FIR_COEFFS_6K;
        SAMPLES_PER_SECOND = 6000;
        SAMPLES_PER_MS = 6;
        break;
    }
    case SamplingRateType::SAMPLING_8KHZ: {
        SAMPLING_INTERVAL = 0.000125;       // 1/8000 秒
        FILTER_ORDER = 7;
        FILTER_COEFFICIENTS = (double *)LOWPASS_FIR_COEFFS_8K;
        SAMPLES_PER_SECOND = 8000;
        SAMPLES_PER_MS = 8;
        break;
    }
    case SamplingRateType::SAMPLING_12KHZ: {
        SAMPLING_INTERVAL = 0.0000833333;   // 1/12000 秒
        FILTER_ORDER = 11;
        FILTER_COEFFICIENTS = (double *)LOWPASS_FIR_COEFFS_12K;
        SAMPLES_PER_SECOND = 12000;
        SAMPLES_PER_MS = 12;
        break;
    }
    case SamplingRateType::SAMPLING_24KHZ:
    default: {
        SAMPLING_INTERVAL = 0.0000416667;   // 1/24000 秒
        FILTER_ORDER = 21;
        FILTER_COEFFICIENTS = (double *)LOWPASS_FIR_COEFFS_24K;
        SAMPLES_PER_SECOND = 24000;
        SAMPLES_PER_MS = 24;
        break;
    }
}
```

### 3.2 滤波器应用条件

滤波器的应用通过智能条件判断实现：

```cpp
// 根据采样率决定是否应用FIR滤波
uint32_t filter_condition = (samplesPerMillisecond - 6) >> 1;
uint32_t filter_flag = filter_condition | (samplesPerMillisecond << 31);

// 条件判断逻辑：
// 6kHz: filter_condition = (6-6)>>1 = 0
// 8kHz: filter_condition = (8-6)>>1 = 1  
// 12kHz: filter_condition = (12-6)>>1 = 3
// 24kHz: filter_condition = (24-6)>>1 = 9

if ((filter_flag < 10) && ((523 >> (filter_condition & 31) & 1) != 0)) {
    calc_fir_filter_hi(signal, processedSignalBuffer, tempCalculationBuffer, length,
                       FILTER_COEFFICIENTS, FILTER_ORDER, filterStateBuffer);
}
```

### 3.3 高效FIR滤波器实现

`calc_fir_filter_hi`函数采用分块处理策略提高计算效率：

```cpp
// 分块处理，每块8个样本
const uint32_t block_size = 8;
uint32_t block_count = input_length / block_size;
uint32_t remaining_samples = input_length % block_size;

// 处理完整的块
for (uint32_t block = 0; block < block_count; block++) {
    double accumulators[block_size] = {0.0};
    
    // 读取当前块的样本数据
    double samples[block_size + filter_order - 1];
    std::memcpy(samples, current_temp, sizeof(double) * (block_size + filter_order - 1));
    
    // 卷积运算
    for (uint32_t k = 0; k < filter_order; k++) {
        double coeff = filter_coeffs[k];
        for (uint32_t i = 0; i < block_size; i++) {
            accumulators[i] += coeff * samples[i + k];
        }
    }
    
    // 存储结果
    for (uint32_t i = 0; i < block_size; i++) {
        output_ptr[i] = accumulators[i];
    }
    
    current_temp += block_size;
    output_ptr += block_size;
}
```

## 4. 技术特点分析

### 4.1 数学特性

1. **线性相位特性**
   - 所有滤波器系数数组都是对称的
   - 保证信号通过滤波器后无相位失真
   - 对触觉信号的时域特性保持完整

2. **归一化设计**
   - 所有滤波器系数的和为1
   - 不改变信号的直流增益
   - 保持信号的幅度特性

3. **群延迟控制**
   - 所有滤波器的群延迟都控制在0.375-0.417ms范围内
   - 满足触觉反馈≤10ms的实时性要求
   - 确保用户感知的同步性

### 4.2 性能优化策略

1. **预计算系数**
   - 滤波器系数在编译时确定
   - 避免运行时计算开销
   - 使用`constexpr`确保编译时优化

2. **分块处理算法**
   - 每块处理8个样本，提高缓存效率
   - 支持SIMD指令优化的可能性
   - 减少内存访问延迟

3. **状态维护机制**
   - 维护滤波器状态支持流式处理
   - 避免信号边界效应
   - 确保连续信号处理的平滑性

4. **条件应用逻辑**
   - 通过位掩码智能选择是否应用滤波
   - 避免不必要的计算开销
   - 根据采样率自适应优化

## 5. 应用场景和作用

### 5.1 信号处理作用

1. **抗混叠滤波**
   - 去除超过Nyquist频率的高频成分
   - 防止频谱混叠现象
   - 确保数字信号处理的正确性

2. **噪声抑制**
   - 滤除高频噪声干扰
   - 提高信号质量
   - 改善用户触觉体验

3. **硬件保护**
   - 限制输出信号的频率范围
   - 防止高频成分对线性马达造成损害
   - 延长硬件使用寿命

### 5.2 系统级优势

1. **跨平台一致性**
   - 在不同采样率设备上保持相同的相对频率响应
   - 确保用户在不同设备上获得一致的触觉体验
   - 简化跨平台开发和测试

2. **性能分级支持**
   - 根据设备性能选择合适的滤波器复杂度
   - 在低端设备上保证基本功能
   - 在高端设备上提供最佳体验

3. **实时性保证**
   - 所有滤波器都满足实时处理要求
   - 群延迟控制在亚毫秒级别
   - 支持低延迟触觉反馈应用

## 6. 工程实践价值

### 6.1 设计模式体现

1. **策略模式**
   - 根据采样率选择不同的滤波策略
   - 运行时动态切换滤波器配置
   - 提高代码的灵活性和可扩展性

2. **工厂模式思想**
   - 根据参数自动选择合适的滤波器
   - 封装滤波器创建和配置逻辑
   - 简化上层调用接口

3. **RAII原则**
   - 滤波器状态的自动管理
   - 资源的安全分配和释放
   - 异常安全的代码设计

### 6.2 代码质量特点

1. **可维护性**
   - 清晰的注释和文档
   - 模块化的设计结构
   - 易于理解的命名规范

2. **可扩展性**
   - 支持新采样率的轻松添加
   - 滤波器参数的灵活配置
   - 算法的模块化实现

3. **性能优化**
   - 编译时优化的充分利用
   - 缓存友好的数据访问模式
   - 高效的算法实现

## 7. 总结

RealityTap的低通FIR滤波器系数设计体现了工业级触觉反馈系统的专业水准：

1. **技术先进性**：采用现代数字信号处理理论，实现高质量的信号滤波
2. **工程实用性**：充分考虑跨平台兼容性和性能分级需求
3. **代码质量**：遵循现代C++最佳实践，代码结构清晰、性能优异
4. **用户体验**：确保在不同设备上提供一致、高质量的触觉反馈体验

这种多采样率滤波器设计是触觉反馈领域的典型工程实践，为其他类似系统的开发提供了宝贵的参考价值。

---

**文档版本**: 1.0  
**创建日期**: 2025-07-09  
**作者**: RealityTap开发团队  
**文件路径**: `core/docs/lowpass_fir_filter_analysis.md`
