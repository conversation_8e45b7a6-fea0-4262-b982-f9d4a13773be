# RealityTap CMake配置文件
# 此文件由CMake自动生成，用于其他项目查找和使用RealityTap库

@PACKAGE_INIT@

# 版本信息
set(RealityTap_VERSION "@PROJECT_VERSION@")
set(RealityTap_VERSION_MAJOR "@PROJECT_VERSION_MAJOR@")
set(RealityTap_VERSION_MINOR "@PROJECT_VERSION_MINOR@")
set(RealityTap_VERSION_PATCH "@PROJECT_VERSION_PATCH@")

# 构建配置
set(RealityTap_BUILD_TYPE "@CMAKE_BUILD_TYPE@")
set(RealityTap_CXX_STANDARD "@CMAKE_CXX_STANDARD@")
set(RealityTap_COMPILER_ID "@CMAKE_CXX_COMPILER_ID@")
set(RealityTap_COMPILER_VERSION "@CMAKE_CXX_COMPILER_VERSION@")

# 平台信息
set(RealityTap_SYSTEM_NAME "@CMAKE_SYSTEM_NAME@")
set(RealityTap_SYSTEM_PROCESSOR "@CMAKE_SYSTEM_PROCESSOR@")

# 功能选项
set(RealityTap_BUILD_SHARED_LIBS "@BUILD_SHARED_LIBS@")
set(RealityTap_BUILD_TESTS "@BUILD_TESTS@")
set(RealityTap_BUILD_DEMOS "@BUILD_DEMOS@")
set(RealityTap_ENABLE_PERF "@REALITYTAP_ENABLE_PERF@")

# 查找依赖
include(CMakeFindDependencyMacro)

# 根据平台查找不同的依赖
if(ANDROID)
    # Android平台依赖
    find_dependency(boringssl QUIET)
else()
    # 其他平台可能需要的依赖
    # find_dependency(OpenSSL QUIET)
endif()

# 包含目标文件
if(NOT TARGET RealityTap::realitytap_core)
    include("${CMAKE_CURRENT_LIST_DIR}/RealityTapTargets.cmake")
endif()

# 设置变量
set(RealityTap_LIBRARIES RealityTap::realitytap_core RealityTap::realitytap_platform)
set(RealityTap_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/include")

# 检查所有必需的组件
check_required_components(RealityTap)

# 显示配置信息
if(NOT RealityTap_FIND_QUIETLY)
    message(STATUS "Found RealityTap: ${RealityTap_VERSION}")
    message(STATUS "  Platform: ${RealityTap_SYSTEM_NAME}")
    message(STATUS "  Build Type: ${RealityTap_BUILD_TYPE}")
    message(STATUS "  C++ Standard: C++${RealityTap_CXX_STANDARD}")
    message(STATUS "  Compiler: ${RealityTap_COMPILER_ID} ${RealityTap_COMPILER_VERSION}")
endif()
