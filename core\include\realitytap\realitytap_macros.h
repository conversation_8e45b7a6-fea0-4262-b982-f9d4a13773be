#ifndef REALITYTAP_ALGORITHM_REALITYTAP_MACROS_H
#define REALITYTAP_ALGORITHM_REALITYTAP_MACROS_H

// 条件包含Android日志头文件
#if defined(ANDROID) || defined(__ANDROID__)
#include <android/log.h>
#endif

#define PRINT_CONSOLE 0

// 为不同平台定义LOG_TAG
#ifndef LOG_TAG
#if defined(ANDROID) || defined(__ANDROID__)
#define LOG_TAG "RealityTap"
#else
#define LOG_TAG "RealityTap"
#endif
#endif

#define DEBUG 1

// Windows Release版本禁用日志输出以避免控制台窗口创建
#if defined(_WIN32) && defined(NDEBUG)
    // Windows Release版本：完全禁用日志输出
    #define ALOGI(...) ((void)0)
    #define ALOGW(...) ((void)0)
    #define ALOGD(...) ((void)0)
    #define ALOGE(...) ((void)0)
#elif PRINT_CONSOLE
    // 开发模式：同时输出到Android日志和控制台
    #if defined(ANDROID) || defined(__ANDROID__)
        #define ALOGI(...) do { \
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__); \
            printf("INFO: " __VA_ARGS__); \
            printf("\n"); \
        } while(0)

        #define ALOGW(...) do { \
            __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__); \
            fprintf(stderr, "WARNING: " __VA_ARGS__); \
            fprintf(stderr, "\n"); \
        } while(0)

        #define ALOGD(...) do { \
            __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__); \
            printf("DEBUG: " __VA_ARGS__); \
            printf("\n"); \
        } while(0)

        #define ALOGE(...) do { \
            __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__); \
            fprintf(stderr, "ERROR: " __VA_ARGS__); \
            fprintf(stderr, "\n"); \
        } while(0)
    #else
        // 非Android平台的控制台输出
        #define ALOGI(...) do { \
            printf("INFO: " __VA_ARGS__); \
            printf("\n"); \
        } while(0)

        #define ALOGW(...) do { \
            fprintf(stderr, "WARNING: " __VA_ARGS__); \
            fprintf(stderr, "\n"); \
        } while(0)

        #define ALOGD(...) do { \
            printf("DEBUG: " __VA_ARGS__); \
            printf("\n"); \
        } while(0)

        #define ALOGE(...) do { \
            fprintf(stderr, "ERROR: " __VA_ARGS__); \
            fprintf(stderr, "\n"); \
        } while(0)
    #endif
#else
    // 标准模式：仅输出到平台日志系统
    #if defined(ANDROID) || defined(__ANDROID__)
        #define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
        #define ALOGW(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
        #define ALOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
        #define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
    #else
        // 非Android平台：在Debug版本输出到控制台，Release版本禁用
        #ifdef NDEBUG
            // Release版本：禁用日志输出
            #define ALOGI(...) ((void)0)
            #define ALOGW(...) ((void)0)
            #define ALOGD(...) ((void)0)
            #define ALOGE(...) ((void)0)
        #else
            // Debug版本：输出到控制台
            #define ALOGI(...) do { \
                printf("I/" LOG_TAG ": " __VA_ARGS__); \
                printf("\n"); \
            } while(0)

            #define ALOGW(...) do { \
                fprintf(stderr, "W/" LOG_TAG ": " __VA_ARGS__); \
                fprintf(stderr, "\n"); \
            } while(0)

            #define ALOGD(...) do { \
                printf("D/" LOG_TAG ": " __VA_ARGS__); \
                printf("\n"); \
            } while(0)

            #define ALOGE(...) do { \
                fprintf(stderr, "E/" LOG_TAG ": " __VA_ARGS__); \
                fprintf(stderr, "\n"); \
            } while(0)
        #endif
    #endif
#endif

#define CLAMP(x, lo, hi) (((x) < (lo)) ? (lo) : (((x) > (hi)) ? (hi) : (x)))

#endif //REALITYTAP_ALGORITHM_REALITYTAP_MACROS_H
