# RealityTap Windows GUI Demo - PowerShell Build Script
# Automated compilation of C++ wrapper and Python environment setup
# Author: RealityTap Team
# Version: 2.0 with BoringSSL caching optimization

param(
    [switch]$Clean,
    [switch]$Help,
    [switch]$ForceBoringSSL,
    [switch]$Rebuild,
    [string]$BuildType = "Release",
    [string]$OutputDir = "build",
    [switch]$CopyRuntimeDlls
)

# Set encoding to UTF-8 to handle Chinese characters properly
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Set console code page to UTF-8 for better Chinese character support
try {
    chcp 65001 > $null 2>&1
} catch {
    # Ignore errors if chcp is not available
}

# Color output functions
function Write-Header($message) {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host $message -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
}

function Write-Step($step, $message) {
    Write-Host ""
    Write-Host "$step. $message" -ForegroundColor Yellow
}

function Write-Info($message) {
    Write-Host "    * $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "    ! $message" -ForegroundColor Yellow
}

function Write-Error($message) {
    Write-Host "    X $message" -ForegroundColor Red
}

function Write-Success($message) {
    Write-Host "    + $message" -ForegroundColor Green
}

# Show help information
if ($Help) {
    Write-Header "RealityTap Windows GUI Demo Build Script"
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor White
    Write-Host "  .\build.ps1                    - Normal build (reuse existing BoringSSL if available)" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Clean             - Clean build (rebuild everything including BoringSSL)" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -ForceBoringSSL    - Force rebuild BoringSSL only" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Help              - Show this help" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor White
    Write-Host "  .\build.ps1                    # Quick incremental build with Clang" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Clean             # Full clean rebuild with Clang" -ForegroundColor Gray
    Write-Host ""
    exit 0
}

Write-Header "RealityTap Windows GUI Demo Build Script"

# Check if in correct directory
if (-not (Test-Path "main.py")) {
    Write-Error "Please run this script in demos/windows/gui directory"
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Set build variables from parameters
$BuildDir = $OutputDir
$BuildConfig = $BuildType

# Configure Clang compiler and Ninja generator
# Check for MSYS2 Clang configuration
$msys2ConfigFile = "../../../msys2_path.txt"
if (Test-Path $msys2ConfigFile) {
    $msys2Path = Get-Content $msys2ConfigFile | Where-Object { $_ -notmatch "^#" -and $_.Trim() -ne "" } | Select-Object -First 1
    if ($msys2Path) {
        $msys2Path = $msys2Path.Trim().Replace('\', '/')
        $clangPath = "$msys2Path/mingw64/bin"
        $clangC = "$clangPath/clang.exe"
        $clangCxx = "$clangPath/clang++.exe"

        if ((Test-Path $clangC) -and (Test-Path $clangCxx)) {
            $CMakeGenerator = "Ninja"
            $env:CMAKE_C_COMPILER = $clangC
            $env:CMAKE_CXX_COMPILER = $clangCxx
            Write-Info "Using Clang compiler from MSYS2: $clangPath"
        } else {
            Write-Warning "MSYS2 Clang not found at $clangPath, falling back to system Clang"
            $CMakeGenerator = "Ninja"
        }
    }
} else {
    Write-Info "MSYS2 config not found, using system Clang with Ninja generator"
    $CMakeGenerator = "Ninja"
}
$CMakeArch = ""  # Ninja doesn't use architecture parameter

Write-Step 1 "Environment setup"
Write-Info "Build directory: $BuildDir"
Write-Info "Build configuration: $BuildConfig"
Write-Info "CMake generator: $CMakeGenerator"
if ($CMakeArch) {
    Write-Info "Target architecture: $CMakeArch"
}

# Check for Clang compiler
Write-Info "Checking for Clang compiler..."
$compilerFound = $false

# Check if Clang is available
try {
    if ($env:CMAKE_C_COMPILER) {
        $clangVersion = & $env:CMAKE_C_COMPILER --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Clang compiler found: $env:CMAKE_C_COMPILER"
            Write-Info "Version: $($clangVersion[0])"
            $compilerFound = $true
        }
    } else {
        $null = Get-Command clang -ErrorAction Stop
        $clangVersion = clang --version 2>$null
        Write-Success "System Clang compiler found"
        Write-Info "Version: $($clangVersion[0])"
        $compilerFound = $true
    }
} catch {
    Write-Warning "Clang compiler not found in PATH"
}

# Check for Ninja
try {
    $null = Get-Command ninja -ErrorAction Stop
    Write-Success "Ninja build system found"
} catch {
    Write-Error "Ninja build system not found. Please install Ninja for Clang builds."
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not $compilerFound) {
    Write-Error "Clang compiler not found. Please install Clang or check MSYS2 configuration."
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Step 2 "Checking BoringSSL build status"

# Check if BoringSSL is already built
$boringSslBuilt = $false
$boringSslPaths = @(
    "$BuildDir\third_party\boringssl_build\crypto\crypto.lib",
    "$BuildDir\third_party\boringssl_build\crypto\Release\crypto.lib",
    "$BuildDir\third_party\boringssl_build\crypto\Debug\crypto.lib"
)

foreach ($path in $boringSslPaths) {
    if (Test-Path $path) {
        $fileSize = (Get-Item $path).Length
        Write-Success "Found existing BoringSSL library: $path (Size: $([math]::Round($fileSize/1MB, 2)) MB)"
        $boringSslBuilt = $true
        break
    }
}

if ($Clean -or $ForceBoringSSL) {
    Write-Warning "Force rebuild requested, will rebuild BoringSSL from source"
    $boringSslBuilt = $false
} elseif ($boringSslBuilt) {
    Write-Success "BoringSSL already compiled, will reuse existing library"
} else {
    Write-Info "BoringSSL not found, will compile from source"
}

Write-Step 3 "Preparing build directory"

if (Test-Path $BuildDir) {
    if ($Clean) {
        Write-Info "Cleaning entire build directory for fresh build"
        Remove-Item $BuildDir -Recurse -Force
        New-Item -ItemType Directory -Path $BuildDir | Out-Null
    } elseif ($boringSslBuilt) {
        Write-Info "Preserving build directory to reuse BoringSSL libraries"
        # Only clean wrapper-related build files, preserve third_party directory
        $filesToClean = @(
            "$BuildDir\CMakeCache.txt",
            "$BuildDir\cmake_install.cmake",
            "$BuildDir\Makefile"
        )

        foreach ($file in $filesToClean) {
            if (Test-Path $file) {
                Remove-Item $file -Force
                Write-Info "Cleaned: $file"
            }
        }

        # Clean project files
        Get-ChildItem "$BuildDir\*.vcxproj*" -ErrorAction SilentlyContinue | Remove-Item -Force
        Get-ChildItem "$BuildDir\*.sln" -ErrorAction SilentlyContinue | Remove-Item -Force

        # Clean output directories but preserve third_party
        $dirsToClean = @("$BuildDir\bin", "$BuildDir\lib")
        foreach ($dir in $dirsToClean) {
            if (Test-Path $dir) {
                Remove-Item $dir -Recurse -Force
                Write-Info "Cleaned directory: $dir"
            }
        }
    } else {
        Write-Info "Cleaning build directory for fresh build"
        Remove-Item $BuildDir -Recurse -Force
        New-Item -ItemType Directory -Path $BuildDir | Out-Null
    }
} else {
    Write-Info "Creating new build directory"
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
}

Write-Success "Build directory ready"

Write-Step 4 "Configuring CMake"

# Change to build directory
Push-Location $BuildDir

try {
    # Add CMake options to optimize BoringSSL build
    $cmakeOpts = @()
    if ($boringSslBuilt) {
        Write-Info "Using existing BoringSSL library"
        $cmakeOpts += "-DUSE_EXISTING_BORINGSSL=ON"
    } else {
        Write-Info "Will build BoringSSL from source"
    }

    # Configure CMake
    Write-Info "Running CMake configuration..."

    # Build CMake arguments properly - use direct cmake call instead of Start-Process
    $cmakeCmd = "cmake"
    $cmakeArgs = @("..", "-G", "`"$CMakeGenerator`"")

    # Add build type configuration
    $cmakeArgs += @("-DCMAKE_BUILD_TYPE=$BuildConfig")

    # Add compiler-specific arguments for Clang
    if ($env:CMAKE_C_COMPILER) {
        $cmakeArgs += @("-DCMAKE_C_COMPILER=`"$env:CMAKE_C_COMPILER`"")
        $cmakeArgs += @("-DCMAKE_CXX_COMPILER=`"$env:CMAKE_CXX_COMPILER`"")
    }

    $cmakeArgs += $cmakeOpts

    Write-Info "CMake command: $cmakeCmd $($cmakeArgs -join ' ')"

    # Use & operator for better argument handling
    & $cmakeCmd @cmakeArgs
    $cmakeExitCode = $LASTEXITCODE

    if ($cmakeExitCode -ne 0) {
        Write-Error "CMake configuration failed with exit code: $cmakeExitCode"
        Pop-Location
        Read-Host "Press Enter to exit"
        exit 1
    }

    Write-Success "CMake configuration successful"

    Write-Step 5 "Building C++ wrapper"

    # Record build start time
    $buildStartTime = Get-Date
    Write-Info "Build started at: $($buildStartTime.ToString('HH:mm:ss'))"

    if ($boringSslBuilt) {
        Write-Success "Using existing BoringSSL library - this will be much faster!"
    } else {
        Write-Warning "Building BoringSSL from source - this may take several minutes..."
    }

    # Build the project
    Write-Info "Running CMake build..."

    # Prepare build arguments
    $buildArgs = @("--build", ".", "--config", $BuildConfig)
    if ($Rebuild) {
        $buildArgs += "--clean-first"
        Write-Info "Performing clean rebuild..."
    }

    # Check if we're in the build directory or main directory
    if (Test-Path "CMakeCache.txt") {
        # We're in the correct directory with CMake files
        & cmake @buildArgs
    } else {
        # We need to build from the parent directory
        Pop-Location
        & cmake @buildArgs
        Push-Location $BuildDir
    }
    $buildExitCode = $LASTEXITCODE

    if ($buildExitCode -ne 0) {
        Write-Error "Build failed with exit code: $buildExitCode"
        Pop-Location
        Read-Host "Press Enter to exit"
        exit 1
    }

    # Record build end time
    $buildEndTime = Get-Date
    $buildDuration = $buildEndTime - $buildStartTime

    Write-Success "Build completed at: $($buildEndTime.ToString('HH:mm:ss'))"
    Write-Success "Build duration: $($buildDuration.ToString('mm\:ss'))"

    if ($boringSslBuilt) {
        Write-Success "Fast build completed! (BoringSSL reused)"
    } else {
        Write-Success "Full build completed (BoringSSL compiled from source)"
    }

    # 清理不必要的BoringSSL测试工具和命令行工具
    Write-Info "Cleaning up unnecessary BoringSSL tools..."
    $unnecessaryFiles = @(
        "urandom_test.exe",
        "test_fips.exe",
        "ssl_test.exe",
        "pki_test.exe",
        "decrepit_test.exe",
        "crypto_test.exe",
        "bssl_shim.exe",
        "bssl.exe"
    )

    $binPath = "bin"
    $cleanedCount = 0
    foreach ($file in $unnecessaryFiles) {
        $filePath = Join-Path $binPath $file
        if (Test-Path $filePath) {
            Remove-Item $filePath -Force
            Write-Info "Removed: $file"
            $cleanedCount++
        }
    }

    if ($cleanedCount -gt 0) {
        Write-Success "Cleaned up $cleanedCount unnecessary tool files"
    } else {
        Write-Info "No unnecessary tool files found to clean"
    }

} finally {
    # Always return to original directory
    Pop-Location
}

Write-Step 6 "Copying DLL files to output directory"

# Helper function to copy DLL files
function Copy-DllToBin {
    param(
        [string]$SourcePath,
        [string]$TargetDir,
        [string]$TargetName = $null,
        [string]$Description = "DLL"
    )

    if (Test-Path $SourcePath) {
        # Ensure target directory exists
        if (!(Test-Path $TargetDir)) {
            New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
        }

        $fileName = if ($TargetName) { $TargetName } else { Split-Path $SourcePath -Leaf }
        $targetPath = Join-Path $TargetDir $fileName

        Copy-Item $SourcePath $targetPath -Force
        Write-Info "* Copied $Description`: $fileName to $TargetDir"
        return $true
    }
    return $false
}

# Create output directory and bin subdirectory
$binDir = "$OutputDir\bin"
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
    Write-Info "Created output directory: $OutputDir"
}

# Copy main wrapper DLL
$wrapperDllPaths = @(
    "bin\realitytap_wrapper.dll",  # 优先查找无lib前缀的版本
    "$BuildDir\bin\$BuildConfig\realitytap_wrapper.dll",
    "$BuildDir\$BuildConfig\realitytap_wrapper.dll",
    "bin\librealitytap_wrapper.dll",  # 作为备选
    "realitytap_wrapper.dll"
)

$dllFound = $false
foreach ($dllPath in $wrapperDllPaths) {
    if (Copy-DllToBin -SourcePath $dllPath -TargetDir $binDir -TargetName "realitytap_wrapper.dll" -Description "Main wrapper DLL") {
        Write-Success "Main DLL copied to $binDir as: realitytap_wrapper.dll"
        $dllFound = $true
        break
    }
}

# Copy dependency DLLs
$dependencyDlls = @(
    "bin\librtcore.dll",
    "bin\librtssl.dll",
    "bin\librtutils.dll",
    "librtcore.dll",
    "librtssl.dll",
    "librtutils.dll",
    "libutils.dll"
)

foreach ($depDll in $dependencyDlls) {
    Copy-DllToBin -SourcePath $depDll -TargetDir $binDir -Description "dependency"
}

# Copy runtime DLLs from MSYS2 if requested
if ($CopyRuntimeDlls) {
    $runtimeDlls = @("libgcc_s_seh-1.dll", "libstdc++-6.dll", "libwinpthread-1.dll")
    $msys2ConfigFile = "../../../msys2_path.txt"

    # Get MSYS2 path from config file
    $msys2Path = if (Test-Path $msys2ConfigFile) {
        (Get-Content $msys2ConfigFile | Where-Object { $_.Trim() -and !$_.StartsWith("#") } | Select-Object -First 1).Trim() -replace "\\", "/"
    } else { $null }

    if ($msys2Path) {
        $msys2BinPath = "$msys2Path/mingw64/bin"
        Write-Info "Copying runtime DLLs from MSYS2: $msys2BinPath"

        foreach ($runtimeDll in $runtimeDlls) {
            $sourcePath = "$msys2BinPath/$runtimeDll"
            if (Copy-DllToBin -SourcePath $sourcePath -TargetDir $binDir -Description "runtime DLL") {
                # Success message already printed by Copy-DllToBin
            } else {
                Write-Warning "Runtime DLL not found: $sourcePath"
            }
        }
    } else {
        Write-Warning "MSYS2 config file not found or invalid: $msys2ConfigFile"
        Write-Warning "Runtime DLLs may need to be copied manually if not in PATH"
    }
} else {
    Write-Info "Skipping runtime DLL copy (use -CopyRuntimeDlls if needed)"
}

if (-not $dllFound) {
    Write-Warning "Generated DLL file not found, may need manual copy"
    Write-Info "Checked paths:"
    foreach ($path in $dllPaths) {
        Write-Info "  - $path"
    }
}

Write-Step 7 "Installing Python dependencies"

try {
    Write-Info "Installing Python packages from requirements.txt..."
    $pipProcess = Start-Process -FilePath "python" -ArgumentList @("-m", "pip", "install", "-r", "requirements.txt") -Wait -PassThru -NoNewWindow

    if ($pipProcess.ExitCode -eq 0) {
        Write-Success "Python dependencies installed successfully"
    } else {
        Write-Warning "Failed to install Python dependencies (exit code: $($pipProcess.ExitCode))"
    }
} catch {
    Write-Warning "Failed to install Python dependencies: $($_.Exception.Message)"
}

Write-Step 8 "Running tests"

try {
    if (Test-Path "test_wrapper.py") {
        Write-Info "Running Python wrapper tests..."
        $testProcess = Start-Process -FilePath "python" -ArgumentList @("test_wrapper.py") -Wait -PassThru -NoNewWindow

        if ($testProcess.ExitCode -eq 0) {
            Write-Success "Tests passed successfully"
        } else {
            Write-Warning "Tests failed (exit code: $($testProcess.ExitCode)), but build completed"
        }
    } else {
        Write-Info "No test_wrapper.py found, skipping tests"
    }
} catch {
    Write-Warning "Failed to run tests: $($_.Exception.Message)"
}

Write-Host ""
Write-Header "Build completed successfully!"
Write-Host ""
Write-Host "You can now run the GUI demo with:" -ForegroundColor White
Write-Host "  python main.py" -ForegroundColor Cyan
Write-Host ""

# Performance summary
if ($boringSslBuilt) {
    Write-Host "[FAST] Performance: Fast incremental build (BoringSSL reused)" -ForegroundColor Green
    Write-Host "[TIP] Use -Clean parameter for a full rebuild if needed" -ForegroundColor Gray
} else {
    Write-Host "[FULL] Performance: Full build completed (BoringSSL compiled from source)" -ForegroundColor Yellow
    Write-Host "[TIP] Next builds will be much faster as BoringSSL will be reused" -ForegroundColor Gray
}

Write-Host ""
Read-Host "Press Enter to exit"