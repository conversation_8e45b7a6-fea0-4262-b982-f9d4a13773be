{"algorithm": "ECDH", "generatorVersion": "0.8r12", "numberOfTests": 237, "header": ["Test vectors of type EcdhWebTest are intended for", "testing an ECDH implementations where the public key", "is just an ASN encoded point."], "notes": {"AddSubChain": "The private key has a special value. Implementations using addition subtraction chains for the point multiplication may get the point at infinity as an intermediate result. See CVE_2017_10176", "CVE_2017_10176": "This test vector leads to an EC point multiplication where an intermediate result can be the point at infinity, if addition-subtraction chains are used to speed up the point multiplication.", "CompressedPoint": "The point in the public key is compressed. Not every library supports points in compressed format."}, "schema": "ecdh_ecpoint_test_schema.json", "testGroups": [{"curve": "secp521r1", "encoding": "ecpoint", "type": "EcdhEcpointTest", "tests": [{"tcId": 1, "comment": "normal case", "public": "040064da3e94733db536a74a0d8a5cb2265a31c54a1da6529a198377fbd38575d9d79769ca2bdf2d4c972642926d444891a652e7f492337251adf1613cf3077999b5ce00e04ad19cf9fd4722b0c824c069f70c3c0e7ebc5288940dfa92422152ae4a4f79183ced375afb54db1409ddf338b85bb6dbfc5950163346bb63a90a70c5aba098f7", "private": "01939982b529596ce77a94bc6efd03e92c21a849eb4f87b8f619d506efc9bb22e7c61640c90d598f795b64566dc6df43992ae34a1341d458574440a7371f611c7dcd", "shared": "01f1e410f2c6262bce6879a3f46dfb7dd11d30eeee9ab49852102e1892201dd10f27266c2cf7cbccc7f6885099043dad80ff57f0df96acf283fb090de53df95f7d87", "result": "valid", "flags": []}, {"tcId": 2, "comment": "compressed public key", "public": "030064da3e94733db536a74a0d8a5cb2265a31c54a1da6529a198377fbd38575d9d79769ca2bdf2d4c972642926d444891a652e7f492337251adf1613cf3077999b5ce", "private": "01939982b529596ce77a94bc6efd03e92c21a849eb4f87b8f619d506efc9bb22e7c61640c90d598f795b64566dc6df43992ae34a1341d458574440a7371f611c7dcd", "shared": "01f1e410f2c6262bce6879a3f46dfb7dd11d30eeee9ab49852102e1892201dd10f27266c2cf7cbccc7f6885099043dad80ff57f0df96acf283fb090de53df95f7d87", "result": "acceptable", "flags": ["CompressedPoint"]}, {"tcId": 3, "comment": "edge case for shared secret", "public": "04014c643329691ba27459a40dfe7c4ce17b3ea14d0cd7aa47b01f1315404db51436fbbfe6de0842e0f7e1265f6ff3aca28750677d3370b2fb2a6ef497356f4b95811201051b14178639a09a41465c72d3743436ee1c191ff7388a40140b34d5317de5911ea03cdbb0329fdeb446695a3b92d437271a9f3c318b02dec4d473908158140e97", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 4, "comment": "edge case for shared secret", "public": "040029cd32125c23a41af24fd4b729da0faacbc35516ef0ba59096602571693cd282e26d67e18ef4643d0f6f158d7370d3394ca9a8de7938032ac178c6fd34e3702b8d008649834e2b41be3a8b7510bfe570f4c67075943cd0cbb9d9e1d1da52618b5b96d6aec9b650daf1ca6624c13e5116302b9c79c8c4d3d351915d1e8e1ab6ad76098e", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 5, "comment": "edge case for shared secret", "public": "040032c6f06ce6a15ea064464d35aa368d299c9a9e1e368f694aefb603876248f898f223ce0217bef37d61eb09b27c93187cf8e61ba7b14e3c9bee692b06ac6d95f836019fd19f8480e21c63211d48d45f96f6365cf55f958e1a0fe7ea6b6b9ff230a87b70bb1b14d3a5fb6669a91641c6acf4570c1d3a9e709913b7fe6b35ff81c394d6a7", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002", "result": "valid", "flags": []}, {"tcId": 6, "comment": "edge case for shared secret", "public": "0401f7eb96e64b1a62daf9e0801bfd96a0b15b68e5f5cb3e90b434495a473907338e53098e1c2e493335d09c6aae6fdda0345b98aaed588f2abe82910713fb6c20252901396b17cf250bc018f4cead097e7e09863f14cf1239b065e57d884949eee141926f7e7c9f7f34cf0536368767bc0e1ab5142877293a4c722693a73fe14a5390af93", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 7, "comment": "edge case for shared secret", "public": "04006ddf9b10965d5fc129e96f7a37667ccf66cc44384772906fedb21f9de4629e01aaa09ac7c9866112064bbc9bd58ebc123ab2fe19d8fed1a056d27bfef0630509c7001c441311ef20a16346332ea42d5c65788d68f6817b0267fcab11ea9c948ed108115dda8e823a380b601460742d3772d6424c67b240da24772ff0d2ccd9a1e0cea6", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "000000ffffffffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff0000000000000100000000000000", "result": "valid", "flags": []}, {"tcId": 8, "comment": "edge case for shared secret", "public": "04007a8c547268c948b626da636cf54428ea2ab23861d499a84ad7be1cf691b92872a06e26c6dba08ca9ed386f83d396156d5fa023f57d5ea6440ec7401dad2c08ad70018c3815b1b9a2e42555419a6c19043fa2b0ddcc4b5a6e372fee9fcb227d85bad704687e7e1a818b612d5c046cd75972f7a2dd5c9a200ac5582cd59fec47ac525ecf", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "00003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff", "result": "valid", "flags": []}, {"tcId": 9, "comment": "edge case for shared secret", "public": "040029153cf062f88f303e5d6f9aac968bd901076d5994ea7f831833b1e69b67e9e9fe20cf9c5623e00e0b9e3592fca2a03324b5df7c93186aff697aca864600d44ecc002801a62e2f4106f34106da23dc93d50e3e975a1d47510021835290649b7a4125109f656b6b0b5bd00b24d84ea1ba4e1ed49e61c526fb1011005131caee7ee0501e", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 10, "comment": "edge case for shared secret", "public": "0400a61eb994e28722c59b3c6007dfdf8b37893f6350f461b26a00e1a45104314aae9989da87e4facb2c4ef721185b7d96d9a45a28a102756501a1acc5d329a21bbf73010e8d0e12f5a9a40e0d59c90ce73043d39730aeadd3788e31d7c2bb62a1166161994664afa658ce2e60a13f45f27f914307c8d6f8d4ed16ab041b8f69908a62782f", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "010000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff", "result": "valid", "flags": []}, {"tcId": 11, "comment": "edge case for shared secret", "public": "04011dd497b30c73709906b164a9a79dc7f2a98c0148ed63016bb95243834fbcdf8eb74b0ff652d54f59f31aef51da6e8974d363655b1da138dc4de0f2a8d800f475ae0057bd4b84607400d863ffbf45a3cf58999ee24ba05e93eca7b0e4ae760eb1733559a45d15579d3370d716ffa3ec4bfdae418e32fb06138dfca213720a938577610e", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ff00000000000000000000000000000000ffffffffffffffffffffffffffffffff0000000000000000000000000000000100000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 12, "comment": "edge case for shared secret", "public": "0401283eb93fa369fe7012b647d21e0a97cf9950e5fbed819ef56158f20c8a9473a418eccbca4dc2b47f4cb6d322f917005859bf221e84ac9827cab82a801c627fb1ec0075c480cbafb352fcaf93baf23a1405fd81febe09729a908d1077e177dd8993d94b251a0d52652da3edb6fdf864e80cd51540e73d0b5107e3433576dcaa4e18db43", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ff0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 13, "comment": "edge case for shared secret", "public": "0400173beefe35ee868d497ff6601628f65ce18a1591f7e4a3a406622f3f508e2da68f101ed02febc38418c6ddfc26a5ec9848c42792463b1e945f9e167db34bdf2d660053070647aba7cd60eb295ab81a268a3903f393c5d28bbc5e022351c377cd84f02c19deb36442372cae1332e92f95ba60b6c852e0de0718e89d24e43cd479c9fb11", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff", "result": "valid", "flags": []}, {"tcId": 14, "comment": "edge case for shared secret", "public": "04009829cd5432687739ab6ae10af8ea73d2cb53b81ebb06b5961b7badc1676b3ef7b00454f7cde56774a01312d574a9193c1a5fe5336fbe62623ad9bf81143789f9f90012f955697ed578207197bf9aac3896521615dbacc8dc665d4f1715b08439f49c2aa6ed337023ffccc5075a85944936826db92f919737ca3afeadba1847084bdef7", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff00010000", "result": "valid", "flags": []}, {"tcId": 15, "comment": "edge case for shared secret", "public": "040126e3c959cd41120bb83693b1d6a034b385137c1bb3213b776122fed96056e329885718a73bee639c0ba4b68818682f498ce5496925002bd7652516405fcc4fecad0073a9c6e3b0c694bf7cc8ccbbd09800e81e3548ba44a0c2381cef0b07bf702a19054bb5d717a1b79294609cbdafd4e2018064f7b2c4c204d818eb7ce521c3268ce5", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ffc000001ffffff8000003ffffff0000007fffffe000000ffffffc000001ffffff8000003ffffff0000007fffffe000000ffffffc000001ffffff8000004000001", "result": "valid", "flags": []}, {"tcId": 16, "comment": "edge case for shared secret", "public": "040153dc481ab3c5dc8decd24ceaee1bec77f59f21f7f31c19538af047d281ac9e2567933fd3d21096b185d4098919571931bb9b0be7197995e2fbaf21c8a10007ade001ad69f08fcae164390be826256b50fae47502ce0e9ca46af0c490cb4033c886f88661a99ff2bd3c9c8e7da30faf2b4c769edc5831810ac05054c97e41063f496e1f", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff", "result": "valid", "flags": []}, {"tcId": 17, "comment": "edge case for shared secret", "public": "0401f586611c87150288c3e86116c5db94a26718978829d701ddac05e9b0ce22dee4b18e95f60cba783ed3384da373deaefc57b8265d3a34eeb458bf24b9d82be32819008456e0f1d80492ef0078cc246d32fc7c7fb6720b4d458b51b2098d35746752b0ef0345bd0d342dfee6dd2f12ed12b34bd95d058c2811fd479d2dde32180e6c9ef2", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01ffffff00000001fffffffc00000007fffffff00000001fffffffc00000007fffffff00000001fffffffc00000007fffffff00000001fffffffc000000080000002", "result": "valid", "flags": []}, {"tcId": 18, "comment": "edge case for shared secret", "public": "04015edc87fd499a73eabffd14d2b6a70a8fb69b6a39d0d9c4dda2337b53cc72e49a9e3d5a2d9e8930cfa11852dac33443227fba6684bd74732e6879884b6ef9dae98f010eeb8d2e3360ea9726628085268af3f2a05ad41235d0a892098bd661b636f7ef0a820282906eda3f1ff1980b98fb5937228e9edcd6332e3641216c7307e7f3f452", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd", "result": "valid", "flags": []}, {"tcId": 19, "comment": "edge case for shared secret", "public": "040131b43002f7e687eec1ecf6a253c2ccc9e48f04d86fccd18fee0d2d22191f1ea539c40d521970b4709dc03986f647e0e8bb3340cf8a3e643a3541035437cf25f01500b27a55ac45f0296f8c9656bcfd52b5cea9f4115c06e4c64319609847d45e92418400e7868672c0d3e6e5e6e004a7190476ed77cfc33ad19a4bd2c615ad9950f374", "private": "00a2b6442a37f8a3759d2cb91df5eca75af6b89e27baf2f6cbf971dee5058ffa9d8dac805c7bc72f3718489d6a9cb2787af8c93a17ddeb1a19211ab23604d47b7646", "shared": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe", "result": "valid", "flags": []}, {"tcId": 20, "comment": "edge cases for ephemeral key", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000d20ec9fea6b577c10d26ca1bb446f40b299e648b1ad508aad068896fee3f8e614bc63054d5772bf01a65d412e0bcaa8e965d2f5d332d7f39f846d440ae001f4f87", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "0053bf137fee8922769f8d0fe279caa4dac9c6054ad0460995588a845d0a959e24bc0fc2391a2b92f7bd400f50a11a9db37f07bef7fa8dad2a903fcf534abc8736f7", "result": "valid", "flags": []}, {"tcId": 21, "comment": "edge cases for ephemeral key", "public": "040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010010e59be93c4f269c0269c79e2afd65d6aeaa9b701eacc194fb3ee03df47849bf550ec636ebee0ddd4a16f1cd9406605af38f584567770e3f272d688c832e843564", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "01c95ac417c90a520149b29105cdab36f528a23efb5621520dbdafea95a7d43499c4c8be02cd1c2de000da18104fa84a1e9ece6386f0e0efa5234a24595d7c4c96f4", "result": "valid", "flags": []}, {"tcId": 22, "comment": "edge cases for ephemeral key", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200d9254fdf800496acb33790b103c5ee9fac12832fe546c632225b0f7fce3da4574b1a879b623d722fa8fc34d5fc2a8731aad691a9a8bb8b554c95a051d6aa505acf", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "01b47ec41e3a5abd9dd9808fc04d9078cbed72b9eba98d3c1ded70a29938f0efd5a27a7113ff721f122cb17411de307a355c685074f5766b6d1a033d2fa188c945b6", "result": "valid", "flags": []}, {"tcId": 23, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000000005f880f50ec94bfac6658fa2fce05945c6a36b266407b6fbd5437a83e2f2f9b9c50a734872e48e70df65457f13e47d06c6b8b29f4735acf105ea63e051904d18aea", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "013aefe3245728a08c904fe7d61cd9c2fdac63f29cf664d8f161bebacb93f8a710e9692f9689480ad498de00f00061e40e46e76e4754c1130ef4217a58933e0b1dc6", "result": "valid", "flags": []}, {"tcId": 24, "comment": "edge cases for ephemeral key", "public": "04000000ffffffffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff000000000000010000000000000000f33ffc45da3eac1baab727ab8fd355cfa134c42047d55262651654fb50df7e9a5a75f179c8c86c4388213b5687dc43dfebb37f30128703c44ccd5c3284833b8717", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "0168df272d53e3161926168c4aeab5f355b8d2a6689cfd567f2b6eb2011a18c775ac2a21f8dd497f6957217020b3b1afcb7021f24fccc2523be76a2bff44596e5a14", "result": "valid", "flags": []}, {"tcId": 25, "comment": "edge cases for ephemeral key", "public": "0400003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff00cd2839d857b4699f5c8e8a0194786e26a862f086b4ba80746ae5225ed3aa68f96b7aaec55225830bb98f52d75221141897ba49d7a31ebbf0b6d7d31352e5266190", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "013db1b9241b23d33860d32dec37a79e4546a41afdfdd9c438d04e1f8b566ac8d9d3f572c293e96943722a4ee290e113fffaa82a61867d9ca28d349982354c9b256f", "result": "valid", "flags": []}, {"tcId": 26, "comment": "edge cases for ephemeral key", "public": "04010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000813d9829119f42ffa95fea8ba9e81e4cd6a6ca97fb0778e12e5f5dfe35201dd4cca8eca0d2e395555997041381e6ac1f18ddf4c74e0b6e9041cfdca1d1c103091", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "01d2bbe9f754584ebbc7c7ad74136d1c8a144948948aa8be49989dd9b4c514db2e2ab1e0713ad1699f632dd2cea53da218ed549f030a113e282fd9e3be462d9aba84", "result": "valid", "flags": []}, {"tcId": 27, "comment": "edge cases for ephemeral key", "public": "04010000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff00878ad597d290db2cf660594aeed0f9b7c8dd68451d2d1b2cbc816b1ec4f35465b3964aff2edf1255163f5fca580132f85cade2887a017e7cd0b37196ad85221107", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "000f37a2e2caef54fff4126c0fa96e7c47f0cad74626ef91e589e12d2e1e8c221be7295be9dc2712b87bb0aa0f5880b738bc1242f2ba773bf9eb2a54e3c1ca4758d7", "result": "valid", "flags": []}, {"tcId": 28, "comment": "edge cases for ephemeral key", "public": "0401ff00000000000000000000000000000000ffffffffffffffffffffffffffffffff000000000000000000000000000000010000000000000000000000000000000000b5e1191b449fa1ebdbd677daa48f90e2d1d6c058c877087cafd9364d99dbb283c68402e6e6c5f5411b2ed42824d8b280ceb910aba6847883a7e3780e2132af41c1", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "017aeb254d9c8c8ee06215ff33811357da73bf7f6dd6d7f8f176d62c065a88a9005f680c630e9f2763585ea2ee76b6e4ab45e673f814ebfa95947c0c63fb24fa6e9b", "result": "valid", "flags": []}, {"tcId": 29, "comment": "edge cases for ephemeral key", "public": "0401ff0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff00207513d615656a1cc7505c18aa21b08e2b1d5a841de0816cc29c004efdb2d902ac1a7bb05e20722b576b64a3ddf4d2486421ac706bf4a424f252386368a5340fb6", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "0061bed42248a37b4625ef04c4f9c7ef69ee3c6f9503378351fcab1b8ce1343206997eec1b88449eb6f7355711ea1a818a486ee30a24126241a7e2289267cf5dd61f", "result": "valid", "flags": []}, {"tcId": 30, "comment": "edge cases for ephemeral key", "public": "0401ff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff001fe800c50e54012b75a33e4be7d07c8d60f29680a395e951a6a31c5096b0ea928fc2cbf327dd784dc0a7ca46ea73992b758b5641364b4aba39e93798a4d925a008", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "001067d9104e296ef42b944587de11b10df05d2d959ed44cac9e7ef1c7a05d90819c43bc79c7397918f957cc98db931763bbeb1bdfc35865e8a359a013f13d60c433", "result": "valid", "flags": []}, {"tcId": 31, "comment": "edge cases for ephemeral key", "public": "0401ff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff00010000008dd18a1f5e482140be79bb65a21ad60c8987e532c84345f0135affd46ec71ef02b1ca3ad56f301d955fa306c122d441d6fedcf8b855ef256350bf69d23a7207ad9", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "00b779d83035cf7bb0bb04c7b2f46d08f6791f0d1542c9bcce7250e772b12ad8e38fce1d2b063a06f0fa3a1b072dd976f5f8542979903075162f1f5c6ba3b76cc45d", "result": "valid", "flags": []}, {"tcId": 32, "comment": "edge cases for ephemeral key", "public": "0401ffc000001ffffff8000003ffffff0000007fffffe000000ffffffc000001ffffff8000003ffffff0000007fffffe000000ffffffc000001ffffff800000400000100566203dd325a081c4441f001f780365874fd3d0c9bc47227481afe76a93ae1bfde63af972203abfe22c63b80e83f7cc2184c3cb8cfd0152c54324c4759fd1f9a50", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "01afe5d23733728b79c743933b9ba7dfec5ed19b7737e393908a1d000918aa795d1ce0ad533983d018f927b35d2af6463356573f387febd75911a49486202ca69d3a", "result": "valid", "flags": []}, {"tcId": 33, "comment": "edge cases for ephemeral key", "public": "0401ffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff00b11c668fbd549f36889f7b63434051da26f15705839136b1b14a09152d7a182ea7806c35478a32d3aa3c9c1627a61519ebec71b36fa77449025b8829e27f307834", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "019612aeb386febb1a28096fe5b2f682dead02389785225b80a27df439510d08349a193839525f248b7f9bcabfd3dc8da8cc1724022299b7b5e72399d89464b82e44", "result": "valid", "flags": []}, {"tcId": 34, "comment": "edge cases for ephemeral key", "public": "0401ffffff00000001fffffffc00000007fffffff00000001fffffffc00000007fffffff00000001fffffffc00000007fffffff00000001fffffffc00000008000000200aa75efc0a8daac1d73f32c9c552414bccf44af8e74331b47439e7dcc49a135b3ee61e9f69717d89b4bba3567a195aeda13fbec634bf2984b5ec6b6f80f5978ed5a", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "00570673f87adcef49c1f011e8b9f1e11f7fd3b3c93114d08d3f515aa4a895a6c701c523063bdc13ad1db0a54f6e7b476fe10db2070441befc58c8cff3c08ef76e59", "result": "valid", "flags": []}, {"tcId": 35, "comment": "edge cases for ephemeral key", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0010e59be93c4f269c0269c79e2afd65d6aeaa9b701eacc194fb3ee03df47849bf550ec636ebee0ddd4a16f1cd9406605af38f584567770e3f272d688c832e843564", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "0016aaf228b0aec190d4e4e5b8138ff9cc46d705da1bf002901c6ab420f59314d5b641712b14ef3e4fb125652c47888676804fb5575b741a8408c5625bfccff4fdda", "result": "valid", "flags": []}, {"tcId": 36, "comment": "edge cases for ephemeral key", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe00d9254fdf800496acb33790b103c5ee9fac12832fe546c632225b0f7fce3da4574b1a879b623d722fa8fc34d5fc2a8731aad691a9a8bb8b554c95a051d6aa505acf", "private": "012bc15cf3981eab6102c39f9a925aa130763d01ed6edaf14306eb0a14dd75dff504070def7b88d8b165082f69992de0ffa5ee922cb3ab39917da8524cac73f0a09c", "shared": "00a5d6dfda2b269f4ab895a41c3b71b6ba10d5c9f0d9b3e730275345e4721594abfd39464c227716ded8ef3e60bb1ca0b551716e3f6eebb48d5ce8e0ab58cb1b73c9", "result": "valid", "flags": []}, {"tcId": 37, "comment": "edge case for Jacobian and projective coordinates", "public": "040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010010e59be93c4f269c0269c79e2afd65d6aeaa9b701eacc194fb3ee03df47849bf550ec636ebee0ddd4a16f1cd9406605af38f584567770e3f272d688c832e843564", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "008f61e15e8c8545dcdab188f10ba9111b6345d529d9c5470677342df7ef54c56a1fb9fbe8dea76afbe8f2dd4c3cfb4d5b749d743944c96d74fb47bc4bf601e5dc7e", "result": "valid", "flags": []}, {"tcId": 38, "comment": "edge case for Jacobian and projective coordinates", "public": "04004dc16cafca9833eeb97c136c154f3ae390830f26d300edef06f867efab1c4214f563c25e1c81e16a86eaac8272892d1b65b2ee7fb2b69ba1110b083bbeb6b8873a010dbb701266a8df32d17bd58bea365c2637d686272900a5ea7a19ff98db3bf92425a483c70fdd9db25b6ee69981cb69dcc9c418c32989e73f0a5fd7f3c3ba44b051", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "017e7eecdb0f52e3b8aedd0f5550f26cd5e27e711d6860c54f88cfd3ff075df8d363ee3be4dac2f42d036b7c64e2b50d90764ab4eef0b9d68c29682b9707d45ec283", "result": "valid", "flags": []}, {"tcId": 39, "comment": "edge case for Jacobian and projective coordinates", "public": "04018255c014f2533ad930e4320216496a3f4e0d78b50fa27d42209e270e39baf480a987a2c84098eee53889ea8b6cc9036ddeebefc00b5542d2425fafd5e1babae84101db817b332b297a003cffc4251b58d9c7ce0b90301ef5c65e8ac0f82517fd1730a167d83af50d92f7e25e08787130618920345c435337ef45eb2e9b1ace530d0eaf", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "0115ba7fc1ef5d1547bb259d7302cf84400160a599a3fd1d368e4d8f136848bc32b345a7926b95140c319db39d3d89479d44aeacc05c8af37d55450c1ae114beb583", "result": "valid", "flags": []}, {"tcId": 40, "comment": "edge case for Jacobian and projective coordinates", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe00d9254fdf800496acb33790b103c5ee9fac12832fe546c632225b0f7fce3da4574b1a879b623d722fa8fc34d5fc2a8731aad691a9a8bb8b554c95a051d6aa505acf", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01d63965c150dcc6c834dd2a9446a9006e011affb8241a38e3409752b7f478d32c6d461f978296a57339ff4ab1a308f8fd0330a932979b3fc2363d04538e72510176", "result": "valid", "flags": []}, {"tcId": 41, "comment": "edge case for Jacobian and projective coordinates", "public": "040173de990978da53a05c463a567304727bbc9dbed8d5dab3bad11d1804dea864fbed5bbec807c13e4128749cf8c11727a4c528f91ff0217f953a3048de5ba7a2b9ad00f0213d032cc674e1029c71d10cb3f91de53753a01d6ddc41064036a43d613f2bb83fb999874a0fadcf8a6d40b91713cc9114bd44c0f1333ef98f0efb6372a9a453", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00815a61f2c7622d1b9e3310104a41c576835a29cf3a875e3b1062e4588716d25777f0a90fa6a99a95226320ba225b8965d0ef5af674fba69a8acb850b7773be0c82", "result": "valid", "flags": []}, {"tcId": 42, "comment": "edge case for Jacobian and projective coordinates", "public": "04010f43662f4bad6187ad22a41dfc831dcba255af6c4b5f1c14b2ed5447f88b65d690b875848ea7a0c4efe55b821488d1b0f85cda6e7173d87e0d76441aaf60e960d10017fa4b51097c5ef9cb66d6c3eb851e1a8a41102452bd3f8902f17ee72ab0772241510144674686419c7cd55a930951165ba07d34c2c20c421069c1fe3d976737a4", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0110ea8af76567351766d7ddcb7343ee04edd95035f727e6bae0d89d5017019a3df79554d1d0e84fe339a7c0fd79829cdae1372e5d9900a0dc8bac63ae33066d3f11", "result": "valid", "flags": []}, {"tcId": 43, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04012183fff5777e319e8cd23ab5392baa9d4b1d43c7f8b01f78f295c0c37d4a3a122f88c163a8f9648d9da23b389ba56e8e9ca022c2fe9c273f2585429a2de43295b901e18e9eaaf8a406f10b1dcbaa684743a43d203f6cddd8709db90f43fe7b8b3815ead2046b6b37239e065da62d797cd0a73122ee9bf1bd00b0d801297a3d2bcefd0d", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01f3c9b44faf754be250c2ed756541c261cb53795a555e924782dc4f2c1dd4a3855c823f33bdfcb0f0503ea3f9a911e24235375a69da21a9ae4c647738b7e5c52909", "result": "valid", "flags": []}, {"tcId": 44, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040096068a512e317ec63588ff28d380a8d7c91e3a59279c85f5416e8ed321747b05d537d19d797364afce0e548bfb758e33cc6d751b5c217972de2ec4ff000cc15dee0085aff0f0252ab46ff80d849e14943de8145dbb307b4bc45c9eede78c040a0836d80406770c7c9459c065c53367e683128e8fc1dd89190e747893d9f27e49610bfb", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01f567d01152c90644be34fc7f4dcdacda02e1e14d80cdb377611a8c9a7a4d0fa5413b9d415410c9ac6ff1467cf37235e588d114a54e6f9e0a65b6d02cdb82889814", "result": "valid", "flags": []}, {"tcId": 45, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401bfd8c3e346ef884b4b1c01fd273e982fed38b5c8046165cc1e0de876787690c19c4ec24606bc8e1ed83a4ce10b7a42e6db19c7aa5e9971654e57842d41e70985a300b357351499600560bafaa25e243aff7b33602cf41d3518499e1b43ba1e814a0b45ec01fe694789e0157a3c281d17fa00e7019f47ae7a10ce40ed0cb7ca56b41e26", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01819cd3a3b36883c480c270db139a94d7f94afd8879b34ef65a304d0b9a6201a1dbcfcb8ee5c4e66698d98dd0e8bdd53e563628b15afe05f48230fa1c52952d8989", "result": "valid", "flags": []}, {"tcId": 46, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04000cc8c84d1db71fb91595f00703df257f2a03057847b4b9506d3b2615a8b724062d93a62954219df9ec986261fb7708eaa13954182e105e8e93c111c0d2a8bdc51d00e20cc6488c7761f31a3e762698553e5f2bf49b41a3a95ac5b4823c29759c738151c3019ce9d3d9ee7411a4106d5810c8b8aafd37f544f1caf37f6f5fad592e59bd", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "0080859c07cae1911b753964ee175c8884bd19abad666e2e472ed6329e71f951534227b2742805c6e6310f6ea3230bd403c2260e97b0d7f1274027f7e69fc7b81273", "result": "valid", "flags": []}, {"tcId": 47, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04005494023709ee6ae39d0c5b67d959f5f8cbd1bea96442933a8929e332b704f7146ecc689b7fdb85c83f3a60846e692dde0c2748cd7becbb0d6b4c7c0c0f793f3ade01a77d75c2600687241843e2eb6ac81ab19d0f8a9747988c03f2c770593dfa48048f2816e3856c5f11ce3cdcca3ae0e1f13718c2c5db2c1c859f7caeffffdfd629fc", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "009d532af8d99dfc0ce93e2b63305b1f055c42afff3fa90111b071341ccad45ba63ad9ed3a141e95c0cd0d70e78a7ebd82c22e68fac46aa5c9335a955631f9b7c66a", "result": "valid", "flags": []}, {"tcId": 48, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400912809b73f50475a3b97e9602b757eb2e94fd245e483040e167b833840a62df27912a9c2e0317dbfe58dc43ba2053deede5eca2b22bd06792001dbcaa7ea6b700000438e5bb84f3ef7865152b2317d8df0c691c3d2bed2467d5e7507268a7ba98cab4e4608cea0f5fbf50fd0a487d005b381905c95aaf6a18cd1a2dbd6742b5b05ba71", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "001b018fecf2c07811a54b43a0b284c996eecc7742210269373c39dba299d1ab91c778b0df40aaa52530766650ff1178de05b69c3bc46b29d1af193433fa125eb900", "result": "valid", "flags": []}, {"tcId": 49, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040043cfe2c30d3f9f0ff96e0e37f61eb2dea3816999c91c5b77957ef92b0ecde092c7504ba57406eadd744381906337cd13ae4555e50a9eb257168c2140e828a46ad30005c129fa9725a82d3e6da721ec0d1412ff6d6a2d8f75bfc7b2b6aadc5fad86fc1a8a67cd7e6b7aab42a546179c6e0560554db5f6138a7ef2dc79d2be385356c9ea", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "001450d8402f269dfb636499f1645445106c3d95f93080c994726db7e43fb68488f7430f6d171543a127038c930264e60dbf0f48d332af210ecd32320b1cb90acf60", "result": "valid", "flags": []}, {"tcId": 50, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04012eda9020c665ae69a506ac6b3287465e0fc0137b21075cfb6c69963d9acb39a26ca940c3a39e0d94c054672ffc1761e56ded3c180f006857d131dc34a5ace7fd5001709e8a2cd99e1e2016c24f1c5485e3c47947bcc6efbebd2211c0d529a83d097ac611aa50d7879979f4be2d9cecc16360fe4b949ea9970f1075cbf957f031ccfdb2", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "007846a20b43a498277904686f3766b9a3ad994dc61329db3ae9d97618b8130c2bbece0da7e2c79dbd0e1dc8303ae5f9903d97157d0a74db7f465abe9965cfa83eee", "result": "valid", "flags": []}, {"tcId": 51, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400084ba1cb8f33cfba89d1c14ed2b50da737a05ae0a4ca35fede3b26324d3be74f5e3320a90bf388bcf8bbcc4c3c1c88ed52103cc7c5ca2b59ccd3e7dfab6d2f4bdc01a3f175c3c8a89d4fda95d47b2d4f3047aebc75ca7ea416ed76d8e689d7ebe7e977737bc6eff9733e6a6c0593486e62834ff46121b72fde5b3359fa707fba6d67cc", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "018e645fbedd9f12d4474db1206aa67057e08df567c1eecbda395edb2049f770908e3515cce779e37169ebc026eb5c353040058da85fbabd674c8d52a78a39723196", "result": "valid", "flags": []}, {"tcId": 52, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401ea36ce4e51bbb333ea942a5325e1410896e73652c2833b462262da5d791b1835538b06cd9d7b9741f3414d520da87f8be0be218043b05cad61b93b0bc0484e0b2801ece28df4d9ca3ffb23d69c36966cc26b492f7010dfaca67b4bded71cf74c2b0a86e61bc77903397eb8a4362379f09492aa63d9c9f5ff30d105b530c01f46cd7cdc", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01912aa177ea8f8878447b4a19da17d482357a272dea19b542398a6bb48b80598754ae27c73958046aaa1c69951302727dddaaf966f03ff5b723acb7db7fccf3a6e4", "result": "valid", "flags": []}, {"tcId": 53, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04009c96024fac77e64b846f141cd1011f2ad52e3aa551103b96e35b439cf89c0304293f1bfb522f981fbb56921db87151e797d07f1127a01c3f3de56dd499656c103b0177b67fb2678b720d08ed05a5c06bcf88134201c192997fc6dc15f820b60bcc0f777d0661bd8f81cfed59deec33cd696e1c1ef72c7666cd5aed49eb325714739bed", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "006a5b0c09a7bdef316d25f39579279e286b781c2e430f201c54cfc9e154a38ec31eee9d09f6d5aed45056c5b7bb44213c6357c7492d1fd9b1c7dba116fa51d21cfa", "result": "valid", "flags": []}, {"tcId": 54, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04008d4d677aaaef3925a1d41fce4d30543a1fa33a2b3051b367cfdf1b8da7cd1ab67ce9bb255e60475684443cf19ea2e1f01e58fd79f47725661be208767cb2a17530010be175e24d3bbf27dcad2596b8090c7f2e005901038657284a80ff77d3894a29ee2d7daa5880b3ba052261e4796f132db9e8a9781facb32bd8a09babd9e0cba918", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "00652fbaa411ad316dea40a37d3798d0c03ca7ca590d8eeaee454c7d24ff1c4ec7dffd5ef3e90762d5539c5a57e745b9c0f88e18fff6f737729e67606ddfe3a77519", "result": "valid", "flags": []}, {"tcId": 55, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04009b76bfa396b3706381552ad08fb3b9f92b492c43d6102b7c02cca4017718e0643478c0d73765c27958138c7f6d23b4e83baaf27eaf217ed565e6602b4d080200b1017ba7aefc45677b8c4d4ca468e8a9f5a2fc411a4af4ff4593a9b781f1357aae46988642a1254d6cf588708efe60b2dfab15d371ba4f4d9e09e61f06d4d243e5ec57", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "00b1ae05c2a91f7e1e9d626b1a46bed2009dd6662e712416e19c1be9e5e512ec734127adafe6129a83d74ba7a797f41afc4b1145a16f6e389169c8b6592bf628f747", "result": "valid", "flags": []}, {"tcId": 56, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04010089190e9b63f5a29e41b5a1837d9bf41c0b2ca107d6d088f4c1d468773150d7d145cce70a240827268371144e4acdda79d09a51b31ca20bac1197e619d1a84f4d0122c638c06912586580c7a7dee429b1ba000f6285b7fda441388c323c0895ff90443f0615011caa332622e67ede867a8c445335444ea112e80532d5f6df1408e72b", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "00037d3e4359c326f74a8cdcc4bb82090e93bd48757b6caf749fb39a83f992a7fa9676ad466c8d7193902214dabcfc104bc8664eb934a2df648b2e1f401d745e16b3", "result": "valid", "flags": []}, {"tcId": 57, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040070550537e385e4601490ac5a78348ef26678a4ab3a7469f299dc493bd2976416f649ee3e3cdd77191d8f8f9d600e8cb9fde3afe635e224635b70fa79eb1053744f000a546959ae4abcda827429a47a8cbc6afe42fa0f8391293da778be296c722e02c7ff55e20c119531d211173f02eb108fe1b8bb10d465c7158dd3cb2d9f320d1711", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "018e22a63dfeb14f28d4f338b78455877851e95b349796245effa830e29ea1978499f07ad9704b352746dac30bfbb827e1f00b979f642bb886f156b538ac6be91746", "result": "valid", "flags": []}, {"tcId": 58, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040001b66958931ef30c54f83195cc40530c0e44baa223d28d8bfaabf10d16302f3a19601fb74efcbefc1b71d1e81ae4f60b3f8d7c21de1dfafd5cf19c94b396410c39008f13c42ff46d1a18f3d975fdbc0bbb43c31da9423fa935f4c211435518a01437bfaeb6aaf031f92b0c37d76290ff682378c2651cd975eac192cd6582dbbec09974", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "0032284ed3e15d5e9deda53ceaf5aff21c230e6038e6d7e5caae2a715a5288bcb8469ce2befaaf3b4184eab34869144095661788d3fdf41ac61ef1256890dc7a0beb", "result": "valid", "flags": []}, {"tcId": 59, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040050571167dc9f3b1f4ae301dd1fac47002d901398823f2e3dea4338288de8a8fb432a683bd52bfa380700b27a904ad60a5a4d363951ffe74540d54de8e7d48ea16c0085f6a7a743969ae27e9a96bc6037039a87d97c696aed99a39f6e2d56ace558074d49546e417d791aa5ff049c9d30c30502f40020ae41e7a512671cdcc49b6079b5", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01dfb46afe0bf064dff0b4177f7fd27fa2ee0832af7cdc921f66c0e5be30c80630c41e09202ea2f370f5bbd02ed298b08820ce86e2cb724a1962c6e7b1062f5032dd", "result": "valid", "flags": []}, {"tcId": 60, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04017e4f402b1ce209f673ddf50ae87b9ef61d814d51e14f43da3c23f40fac422f1058fd8930dfaf11710c41a7f6b79255e1c2cfbad69d257a0ecc102f5e38e1407f9d00e10a67d175b399bf1941bd0fc13127f7eb112e1a8681acc2cfae0dc2959e8237488146281ca3df2b1e6056ee932feb1ac6e6e9df3f7a6ee6021578a0ffdb50eca2", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "0017b99fab0ee0d7f93f7309ffdc9149ba3cb683042fb3d02bd5319eb42678ac7ac7de214e5c0f8473627ef15112c0d1a3194284b6fcccf561d0d252ad25fcc93953", "result": "valid", "flags": []}, {"tcId": 61, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040004cd97e12d062b7490be45d85e85bd401c1791fbb44e74a45438a8317c7ec9f025b34c595b4769c7b37f44296b4b8f73d5641da281fe35508a23ad503da91ee7db017c4ed0d84b401dab7488da839b20b57809fe7127fa66ef94dc029e44b250857b309a11c91f736a76f3c89d9aded97f5b0beec93a7b6d7882e428befcc20d90df15", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01b66a065e62ca0ea49c25fe2a37799b46867e274f8b853a534dd6cd1ad81cc13d27f279d1ee5bc2d96d0b7701abb2d213fa37836bbcd2d1d8937b6d20dcb35b53e0", "result": "valid", "flags": []}, {"tcId": 62, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401b23c60eefdbde07ce9751d9f9939bf75458e6f2af0afa3d90fa1762d01a6d6e3fa4082c37ad6bf03ba8ff17919e8b9625b5a909c925d9feb7c3ee19a0425385cea000df68406c674be5e1f42d14bf50c19460aeebdde379ad3130a332e8fbeea1569d314d507b37f1dcd283b1c1715852bd39b81c4edcae0f2e1ce5358c6598b8c970a", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "0059a320501bde6b9b268236f966ac881dede98ce6356a7a900a452c59cbf8d8d20630321625c689c79dffffae7f870a7fbf298695896cacdd2d0e13b702f22e7e43", "result": "valid", "flags": []}, {"tcId": 63, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04006836154477db9db03bb8a404c5942d32e1cdea20354eda9693c3333914b5190c9d63a0433a43062b8860d5e59c0f27ac633038491d46b91fc11ecef8326e75b50e014178e1bbf08473ee5ba856856416e6326cafe6f74227768e835c25c520bae05d1d451f639158dccaa293642a572ac7612d60e96df4c673ab8f2a4ed82e5e2af30b", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "00204ccc5356219beef3b6d90ea2ef3785f76f14aa3b84ca04f6b2a5e90700596431546e104e7788307147312ca890984f76b53c489de04cc728ef003d246fddb360", "result": "valid", "flags": []}, {"tcId": 64, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04019c26d815bbe92358e1ecd02145a5f45e1f8f46cb90bae131ee589e51bb093d8410206585c03ed7c5b3975445ba07fb7dd668a8e58c27e01dd00b08fe409b4440e7009fe859e9d22bb1652e384d5ae579e44a0f8c5c82e59b4639d42a9b27cb140599c1236e932338dce03de46a60fbece61fd34b8ece7da937b5cc1e3e7cebcce339ee", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "017a82403f78f8a51729e8bc63c5de9b67c572d41f2aac28bdd95792e658338f19bddc45a6a1cb1bb275f8a910fa64daae2502600aa97b71f49b28981b718f6ae066", "result": "valid", "flags": []}, {"tcId": 65, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401dd2ae95764078496804529a5eed0da6182be0f085c95789a2634a9569f0080861b183cefd1a1489d0f90465353b972c8450c2aeb5fd80be64f15a719d0873e0a1600e1098dd4b30f78e3fe6c1aaf0048b15b1d0f98ff0744c1c888641d17ac216329376be7113e9321ca7f9f7e1935dff661bce9ffbe86aedbc933f2438fc482c462bf", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "0088797d31f9b66faee97eeb9a695cf668c7960f49bdce051b2fc273d5ea6d86c78688ee3ef4be14eab0659c57ccecb2bdb190e50be04f65079950c5eb90d676ee57", "result": "valid", "flags": []}, {"tcId": 66, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04007124ed9d261bc3e5d29947ff49cc4420db90d3afac7eb6ab91bbab090bd84ffcc3ef33e963bcc05685910ee63b063bb9adae7fcea41199619988d34d6477d11ea90039738ed5529f31a557970f89e6c927fe0bdd6413dc31237e75270fee8e3ba6f3a24ee56516ece117733c385a8bb759e57ee79533f3c857327b7542677d834b8dfb", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "002ba25d016989ea24d55fcdd9dae69bc767a4f3281935f5066203812977737b6a256ae839cde9b273281672d7d85dfb40ca895fc1449ce4283f7416cd913a56d8e0", "result": "valid", "flags": []}, {"tcId": 67, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04004220b1642db63810f96cf54f93ba6c3c9b16baa8ab42ecacc281a2fca368be876024b6ae18a8aaf9e433799fb43c0fa175d4ca4a5e121739c7249e9a26fc9044d701b06ae58dc3f7b773013e2dc800e001220bad2e68115ca67516e9c81763d17eadfad2c6d38226f66623a2d5c11786e07face42d7b0a1df96d095bbbedfcd348e4d6", "private": "018f3dbe37135cd8c8c04182952f6e6f9bfdbb6af4c98f31ba30abc68d88e1ea980d4edcdb5b19f0610082194137ebb019e7a664b522189a186cbe5a03376c0713fb", "shared": "01f5646e724f637e1d4ea8997cc67268da3f741b9af6d21f30775a9a9b0977ed75bfa0ca5a80db2d44822de494d15c87d392cff4acecfb50609b43d7845928286541", "result": "valid", "flags": []}, {"tcId": 68, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04006db3dbd1af249980effd624e9f500e4d9fa446c9be94e39b4860767b55b1310889e178caf6c859f60ffb0d87612c5790833cad160a5b23f741221a123e36f0eeb7002ce839f2ba3072919297ee5a6829b8746af39fbce9fa70dbbf59bd209bb500d6129419ba96020a2550be0a19426ee9e111e6987af0c326e1ae1da4365d15a7bbf5", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0177701749c0c54bd6d54ada39aa2222e5b4d32273ea165941d1a9a6114d2cbe609ee62c2306bc5c1ababe082272157fdac8dda39c7b452820c943db4267fed8c2eb", "result": "valid", "flags": []}, {"tcId": 69, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04000d2fcab62b467f978a0267ff3598e6fdff087d3237f8fb00c17d0f5accbb4eded3c4c4858d4553033e59946030c6f5223779a21205ba2a49b4545be596b76f91b20057c975da0520e1dcaa518951db057864966c818e4e64fa910a0c0c7f9b894c06461fed9b44f7ec43b70e3339da0f566aac07fde501bbf22871452dba5c93e5d623", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00f524b1726e889d14d5fd70bbfc76fb2eb32dc2425f112a5fca5203305416b85609f337cf87ef878e64e375fa29eb32f9f139bf775c0d3229a72c2e880979f822bf", "result": "valid", "flags": []}, {"tcId": 70, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04002cca1f37dd94a0a47c8168acea473fb057652713015e585743c9d33f5b7d01d638bc0d039147581a5f3096139e5ee8ed38bdcb72cde493924776579bcbb2522961015bd4859c2015c081696ab105b8922c1f81ce4a6104e5b56b3cd99ccc3c5066cbc339ae685a904df5dd15485cc3ca72d2c84f15f625397f4605905b361701e00d2b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01ca20d556bf1baf10153e2c1d99b58fa8d8f21cd7eef30afc94efb47d38bb364abdb59a326dffc41fc056efb714384b5d3f09270b4806fcb0e271383cdaec023baf", "result": "valid", "flags": []}, {"tcId": 71, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401b5386fc61597307632b3f2c984fe0ee8e22c5fd904b1d14b3568e9eb76500379d917f2430b7f2f55e0bac356e74672a23df2a5ddd46fc0a58f9f5407093726c25d009cbf983303fcbe0b5a926e28c0654ed457437e4010ccc1d1e02e28688c9f210c76af02ed0df6b727fb18b938718e9dcc2c2421a639d732a61711a5a45faa1ba144", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01206d9bc747f0e7591dbe7a88950a78db2770cebac4e91f71b3052a336bd19f0f906b7298eb92790be7bda33c28216b2816b100fe31ba8a7489a9eca3ba82b9d198", "result": "valid", "flags": []}, {"tcId": 72, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040012d9ebd3feb543b43cf38f7baba9c20fcd5fe2dee9049ca8854184a2ead64aa5405e169ae827413725958307939ee2440c21b8da58b3ff516ae569953cf48737ee00cd5bec6cf728632d31e3a199a298240dded0ab5d2b3513bcb2d114c6b8f279a075ff0c56a3ee9e4058acec8936bf86bb2844ba1307a434fb43d289746662a83dd5", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0128dde1341ca17b37efb34b30ee479b763e878dc8006c8cee435c0bd21073c5bb3df52865bd4131f5cec41588ef778ef14af4ca47dc49a16d5b7008ede0b6e6b1b6", "result": "valid", "flags": []}, {"tcId": 73, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400f3aa49feaf686fcffe3a0a360692ef02feb14de6e0fd8dc922857b29d6837a59ce4013bee99bf87f8323c3ab7f57e02e7b0a0dfaa072bf1ada4836be4908ddae49006af81ab3e305fbe5ce8a819f8168a8b44e40b16eae8268a60ea5d66c0f284365e609a19123370beee420d0f019a1984156ce29ade49510e7a3163705333b858914", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0058bd4d206df1cfb3fe349cda27b013d59dd6f507c066c0b6cf458aa54a1c13832554c66ff27f6175f1bf4058378c9f7c7a9d8b810e40a3a8af19fe0e48c7284ed3", "result": "valid", "flags": []}, {"tcId": 74, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040054682c186f9b80c4c76d7050f6a180fad9fc8519419574bfafca9678c59ca95e2253348e0b157949461e9551ca7ddfb9fae8fd7fd23a8855d9962913c66f2fce5400f405297310ed41239c565c044cfc4cad2868126d444b373d52e0518245600a99c40f1bfa5cca7d1f397c18ead33c4d3fd4d431ef84f1129238e296e3ed7be0f99e", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01612ebacb6c31af2008aa264c45d1cee7aa533d78e1fcf480a3118d23418e23621f0ce9c7509f7182bf23a02a6530659ba62c5f16f8634a4f74d4c32b26c6eecc27", "result": "valid", "flags": []}, {"tcId": 75, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401eabf319253f883417a0ce265d3142396a75f830f33589486889bbd4e2091b827fef6090ab65768a38ffb3a9c97796464634d070622adbc3e9af198efb9d729d7ca0002fa30581a7b2681f7e224ead135ec7a6163af4f40aa333043a2badeceac1353ab5ffbeef929609137cd437e8accc0b02411603959d61c7a70f1391a7a992327a7", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "006a3235df6b85343eab82350620889feba273604e59eb98d78fe63bd441ce39411ead8402e2507fe10a03fad9562c3be4488378cf2a1f4c42785e7d93218d99fe8e", "result": "valid", "flags": []}, {"tcId": 76, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401d36e696aadbbc409b4733fc3c86dcb2e980a1b7d0158f82a27e5b1337fd2272e52a27fe67cc5872b5b46040d88325b2b524a2d5b873bc54ff051c1d16c99d9bc81003e3f6b1a737da16802430f809e084cd8b3420019c2f9f1b978a1128ffcb900b9c4faf177a7877e4a8384728a1c309d2bcfb412fa983982ae6b459fe07eb9c79edd", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0009ca929a3ad9525d0ae19d36b40dc543d2348f8b0d95c59c4207f86a898849858e939b4458b568f57c054ee9d95632aa24c5d5f1d4eaa89faf8fac4d11da28dde9", "result": "valid", "flags": []}, {"tcId": 77, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401061aa89a317c63b824f7ebfc8bd9534e297df9bacf80b8350cbd94cdc4d3d8127a80db9555b77d50ea82c99e667f6fe5379a77c9078bf9c7cc049abe9eac7f174c00a9abfc2a892f104e30cb20cc2bfba6b948c6637ac062e83ae478f096869f17dc1bb4eaf52f7d63518031edecb220ec14b0652e6c96e023490d5ed08fb4259da9fa", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "013950ddb6b915454125e37a03845794fe32037eddc04143d91cbf2ab842495012896460d3824374b214ece10f39d2ac606bb4b2f9b27548e21720ae13266d14beb0", "result": "valid", "flags": []}, {"tcId": 78, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400df58f193b6d4fdba1d61a4e073a305c95dba4ca0fdd58f86ee32a9304a62097ac3c0d1fae32cf2e4e65ba4efc6d0408725f57274f9bc2289ed426ec27079ae0b6e01f07d98e2aba8d38e6d54b1496d68251004547f50789754c4097662827bc83f2cf59192eb9e09011b30c54cad9f9241c092415a366ffe5b6bdb4cfcbcdc46edcc12", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01a1bc2b5e8e0a6210ef8a514ded99eee30ac8e9aa7758adecc4910301fc626a5666e4541cea03a90f1e59fd82b8cd87086f261052124cd43b1c89fd048dad5f2911", "result": "valid", "flags": []}, {"tcId": 79, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040073b917a356c0de8fef14593f66d5dab1f0d56b042ba65d203ec139c6cb71d90bb7dc0444d0e3b01bed97a5c69f27549c92e95bbc7e8e0ce932e6c43a75550e86ec009f2014e29acb2b255bf6ec9118bebe7325e1f11ff7885d1a2e142945cb741e26b4ffc6de72b715a148b840df549b49c9a05f8827f3ef05db98a4f664f6dd8478f0", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00f34f3b8ae2f0fec368396633f581b93c38e329770085a51a93457087f8116386d58dea54b99ce56a80896aad40c2dfe69b2dfe126be17bf15ab7f19ce667bff4f7", "result": "valid", "flags": []}, {"tcId": 80, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401d860abb5c65f6d1791bc3fd626e3da1ce890514417d67b407168dc7fc96641fda56fb1830f3695ab0171d7866730747b3dce3de61ce70f831af85e5c6eae0b7c7701b1eba92b65ba7ee9691d908a14ad0a116a4e09b40c9ea4197e60c0b54f948422baa97d80b4ccaf7b35be681ded0aecdd8bdd3059054af838057888489c00a3b759", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01203df60db39732207bd8db87501bcd8481664a9111c8c65b46f002b0b83159960e4710eba548f0039db24f66190c56053370b3c9884d445971a1296da4d656f9c3", "result": "valid", "flags": []}, {"tcId": 81, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401b341a7b58122c70b72e70b051a335186c89fe47b8f68c35d099b7759fb6208718875d8cd8c45d1151a370201a1b047002f619b05cafe39d494b6a69a52b42653330127e70b8fc840891395afd3b03732d0a52f4777651c3d8a8579d6658510d868b69f143c7eecd2be412e0ad2b2d7aef0717796f3f8480fa924b09df90188debe51bb", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00b22d94b53fb00d48e12f12560eafcdec7ea157c9b042436fc66681de96af98ba98adb7faf79d12b399e901ec7bc9d5117883da205ac26b38f788a7885d434b6e18", "result": "valid", "flags": []}, {"tcId": 82, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040001ff249aaba806d838c1a1d00d9d2259e60ea1e8eec6686405462ab22a32a5b673932bbf63fd74eab3f63348a6c22e0d6e594b253f01ea211bb76f0b9dce66d671005052c1f44d17c334e522a0d8e3d99ae32fa48df6fb9117bee431b7a37440310985e5fdd82170817b0f68c0bb7300d217854e45fb694b46f5f50300627390900087", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00e17a9ebd3ce6a19e5eaaf889b120ac1e38778e1c1129c383fec2bf9780d1c412ebc8fbc29b0646d77b84e45493376e991d5954ce3b3caf479cf8f701d88351aed1", "result": "valid", "flags": []}, {"tcId": 83, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400d2cb243bdfc5ffb2bacbf437161f02cf22971d8b7f9cd0e65c174989f5ed7c9cc752a0c6a7719dc1bf3cbb67af5cd6025e313a4b0c46429eac1320a6ceb279dac2018093c8ffb1767b2b50109e40f18dbe748a357de1e284664aa7b10a8df3092a81edc1889d153965eb2e4f7b314827512b018baaf392581e2a49941f606135648f8c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00ff5caf7fd6da41a1a8bb47de3a083883e2e382477c1ff1d6ecaa44a163e7bd375b67d7013763a776df38e79662310a74873a4ce6126d2ec6f68263f24879699700", "result": "valid", "flags": []}, {"tcId": 84, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401da72330fd00ebaf4e6ff8a303d25c2527dff19d10d8e8aad05fd759d98073f262e693ce2fc49747aed29e27e323e535d469f0fcd1a8a1d08604fafe00d1badc4b300b7de13e7dcedb7376004a9f589922f187bd1be72e2de7893873d2392bbb84aa871100f8b95548f5df89662e98b631c4ee4d0558096d4170be0c1e21cc05955823b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01a64da0503e2df5d383837d05cd436ea627381e48e3695891175ac8ede9169ddf573b66726c28132277beda84bb47f2792b41113a0d2aca71084618f55daae55f79", "result": "valid", "flags": []}, {"tcId": 85, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040077fe7958119535ca6c1a7c87bb4dc9b936678ab3b757e7816938bc857643606e61647d231b9dad3e03fc4dd29ace5fc823e15f3464d8e236d5862b1f6280c836920061ba736c11ffa6d0fae6bc8f3e66babd3c6d79b89ee639459ec65a71f12eabcdaca447a993121dc63dde6ae0ccebe415f4bfe60fe3fec3d2f48a084b759f078ac4", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0140952c273ab512e3a58934e3754f272463407e6189b9c1e259c27f6d433c4827532df627745acda64fd17993508bfdef54711f575cffad61c729e8d6dd8784204f", "result": "valid", "flags": []}, {"tcId": 86, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04011d3ff14f8906db60c7bc09c786be6847768b8684957bf3928c556bf5ce7a8cc82f9ab0e63b4bd7eb87767b3db2624957c3f103e87b5c197dc8c6d2507333ea569d004ab9eeeeab950086e030fc60b6b2e47973582a282e3b019e62744f19c60ca0208e3a537d6ca3532c59a77c540bce03999887483ddddfd524b6f9d7bfb6b1139bba", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "001a3a4343a4295b07d4b28f71697caf7e363e2f3ca205ebb828c44deb2f5bb3d95116f36e25e90eb00424c9880b4553554ff01f38b52cb34da643207d6f29312f94", "result": "valid", "flags": []}, {"tcId": 87, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "04009c9eb4cc6bed689da815f05e3ccd2675473c427d31158f1210fd8a6afcfb272971ef0a0696e48fc01ed9d82c9b1d4be086fa5a1363702780838da4fbd6481536d300b387bdba82a5fe57732ebed26a487d031572c223403c063039617f8e5c2702f602e4b87524f298392ae1d5862fa0ff992e51fce7f1457a0991bf25f66cf845639e", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00103eda707b4d5e9a4adc17b88cc341966798b64c242cd13abdf319b77cb9ef2f06816ee820898235198f62071ee8b5c1735e288dd75bb0e5d5479287a02d21e833", "result": "valid", "flags": []}, {"tcId": 88, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401993d74f0a4a12d2804bf9e25752fab9dc0ae60abc6772dfe10afb056b3261f14fc51584baf256340b3652120f24fac700b729dedec97b421a1e931c17c528d5a4300903f97782ec1c5aacd7c3ab02082d7a2f631d9944dc52c9ad4e0258a899efb33bd81068ad43d7dc643b4d7f239a7ac42a26f4d2780215a68495f79eda81e8ead64", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01087ad97d7121928c8e35e2fe915f8c90f3450dee09d3f6761c290426c74318e88fd04464c0fcc0432171667b571d834ae77ecca8a410379685d30b8cd28dc04bd7", "result": "valid", "flags": []}, {"tcId": 89, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "040107d8b63bc0958510eddab9f11fa35ae4e5d97c92304038d8ec27f6e97005d0143e969c5419e82eba509336e23498c5b73e8f0b21e3d9f5cb561609669ef678c7d40196c535b623be6efa504e969ea71cb925287608740d04acb342b4135882783db8d255a6d556ffc16524babeee06d1f280ab8171848e7db54b76a3b31e4157219614", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0074c332bec2028372dac82bb74c3f3b4bd0f0a6521dfdf0cce877b1a99e506ef0e4fd9bcefd3365428f257ed1595dcedbefe99928e7fa7af891fdc1b6ff9de12536", "result": "valid", "flags": []}, {"tcId": 90, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0401d3f5704dbc67fe3e9ec3a02612edf04a82fcf9d5a41c5a330f4dc65b7c1e8a927fedf31a52b28bfe2b84e08f9dd98a2689440553dd8919fddd56ed736b9d064cc3000179aada2320049f18430d32a65889ab5a9840ff43388f7e2253be3e0ea41d6abe9e33b0c8a805c80c52fa66dab45f7ed2043900e3ada8d025648c4822743b5194", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "018d9f7f732023d2f87097c6f58afc7cee91c67b3df3b318517bbb402664f5829408d911b0a9b41cb2f7dcb96d4fba36d90f0275e0bf6b1253d17ce3f267a7b56bff", "result": "valid", "flags": []}, {"tcId": 91, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400d3dc62a070af81ad1dbe02ff075868aa35e3c3b555f58da4485e15bde789b6802b579887d753aa13589167dd26b12489734e80a34941c638c102fa5c199e4d11180190a3534120b8ff4cf7a9386185cd2c7aee6667f130cff1b060242d340029c15eb7d521e1f6a9663f19b2fe740b4384bf3480e76cbdee1da77b8f1c45b9e926884f", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "016f129531c671388010d41c934a2592d57de3376de7abfd32db1b42b34fdb13e5c8371db8c36101ea35e26d04e19ba891b0816a821c5103a1f68c9dc399b1e4ed50", "result": "valid", "flags": []}, {"tcId": 92, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "0400d51e31634926269b2a0885c828a5ab76b1fe4e5e3d941d9678e4bb079002bd3c8e6c1f157e63c275731d57379142a8df9587d10b75ffe62eb4530e75d7634026d7000780e8bcea8d1399943f5bd0f4fd1837db2f9be7fc4669d0422c428c223da53b9566ef0d27bb64cae01d9f949d1a744e8b0e792780d1a243a079395418166d176b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00f55504f1714e07702acb1c594dd246603fb71f11e49de5906acf6dc3bef6a2402e1c5dad8831aa18894531cae087a2dda40fe2db582d3c20d602af62dc12b7e295", "result": "valid", "flags": []}, {"tcId": 93, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "04019d7770d5d9dbe50786ec909d071f12d0b6d10c8b1ddbf546d0e6f4a3ff7e0476f04472eee33d8775586504f005e82329cd6b6cae3f63012a37ab16c47f27ceb36c009d6b670eb33fefcafa11857f5c3bab4fbe41c174650e25e49c65ac63cdd2c2a8f8f605d7e267ec2023f39996b8e825dd0431822108e2c01ff07757d2495805b44b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01b63a08a8fea820ed8c01f0b8482549a13606bd95a7f3689fdfca6c3f4e3d349c7d41847f134d06fabc012bffe98cc6e16ef573b534b3d64792d12d0cd3f1ccf1af", "result": "valid", "flags": []}, {"tcId": 94, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0401f383ca1fa8fba4ec0131927e6688a374f1fbf04f2e793b6b0631d19c24ec6cd75c52d0b19ec27c377a4ac4834bc99001624204552154bea298566300643c63287c00bd95617d1655dccc5b8ad50fe7b1c00bd5111b00338d6e5987a0742f608b74a93502b1260fd193b3bd3a90c843c1344af1638773189036637c3d7504d50e806555", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "015353c08ca7c484e27a3e04c1d8dfeeecac173c2b6e2a5dbcb0da5223e634bd2d527d8b34ba0e592caf9d6234fc54452c5fb505833a97708ccb8debc4df5dcacbb3", "result": "valid", "flags": []}, {"tcId": 95, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "04005d11ffc03c08ae323cb938fb6f2a33efbcbd61a65840c33b207265ec8add0764d59e5840a85a062bfb1f705559c1954aee96e23c888e0ab6704f362bf5c58882f7013d7a4af3b3a58ad19612a1c371234388d8c06f53f03b39e84c22557cc6641a5580687bb08632eed31e0cc114d179da6865376a9d9b0eec67c1c598ee972b771b6e", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01740f76c8d2bbd7179fbed614123fe2fdbb85fe8220300b40cbbb539d4754c5ef63f7b2bc27e129478e7aceec834713ceba27e2f6795344372d3c4d935a9c9e4696", "result": "valid", "flags": []}, {"tcId": 96, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0401e9e395fe1b0b1662256a4b5953e98971dceee098b34f9faa87c07a15e2d9618e713f98cc4f8e89d66e06bf7f6b5b2e012491851b11d71141385fa9b43347c4823f008205a391bb03750c7951f410691dd42cf3d71df5f76b98c0d082460c21f96361013952224dda7463cde16f242c34c62673d9156aed7260e7b01757eee6bea60ee5", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01734253e8d5e9308cd51bf04104ee08d163acb590b1a955896447b8a255fcd3c1256b4e86b9f316eff4d7613b3829f5785961eda4ee9eddeda4db4d60af19e31973", "result": "valid", "flags": []}, {"tcId": 97, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "040164016e2bc36fa9ef56713da89acf0b048082dc631156740ec8ecd22c1238488371284345a96ecef6dcdda2e13b4f2b9491c386274897fa13c6b2313c500041975501c90a405431005532377c204bac9a0d9d226542584fbe3c89f785c708a23399a1bd14daa3e68cc76aed9f5fd4bfbe4eecab4a7ad2a00170dad37e6fe56e925e6bfe", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "009c6234171b4c43f79ff0ad95812e36b0784ad59253d20ea0cb98633fb063ffd8aa22a2dcba29d4116cd70737c1b1f6ebab4731c5b8a455788597a607b5a45bd547", "result": "valid", "flags": []}, {"tcId": 98, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0401d36d2b3ec425a6c2507c4ddfb5b4a10ec4d4a150c5c2b80a263baddebd3198797e97ed7d0cca818cf4ddd47f6134b7a6d2b0a15ee60973f9d7da220f62800c5467011bbb5f8e9df65b9b2c105e4d8f6dbdc4f23f256b53b47bc2892c295e7cbdb4aae81858ba66443255659beaa308aea4c509c99d578fdfb873f51ff53681aea622e0", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01cec1f027a10de4a6905ea32f534851a2f23ddc9e7ecd3d24b8b88be01f7639cac7847c9abdbe746c171a913d6719d7c2a0c0359c4036360be1e75e8b2f7aaa678e", "result": "valid", "flags": []}, {"tcId": 99, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0400db63bd5aee53bd63fce054063dc364aae4f320f7ad2df294140431ad1550c7eab371a13d4dbb78f63d7188e61dc8e268888d9319906a28f360bf7ce868eb67d0c7007d66371152954f48581db91c1533ddf3b148b7e96a5e9964f706cb3f6ee805793fd7c5767e79f426233308c8effc1da71ec73744a49a69fb5f662407ff54d94879", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "005f900efbbad497ee904b80fce07029ad3f979cb3423dc7d16c9cf0853f89a487dfdc5cc34530afa8ccdf5a0e376e278a091bd4f6077c941ded6f1b0fc8840100a3", "result": "valid", "flags": []}, {"tcId": 100, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "04015681f7786b77079c6aa9ccdaa58f39e9ec4e14e24d1bb3f0d6cb56bf7b78761c3bf58c7fb76a22943bb25db80eb0af7410d607154ef927152a8921fdefe7288dba01e5b19c827b288558da2df5f9fca3137ed0694078d3f600aa7a495fe28f418d458bc55276bf7d2969ac2429e2af8d05d4112edbe93b041f2d5d56a4fee5fb918b69", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0124c23c00d30329065611075b02e55c3c667bbe3d65160fa189128af455240eb677a1ee07ca8789be7a8ed074ce17504a5903d9a752681afe6342b054866cbbf199", "result": "valid", "flags": []}, {"tcId": 101, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0400b3e7c9774c523bbef857d59f8ccecd067fd18c7d7777c82c68146b338e14c5d0998b3b962354d9b107337afd4137233047adb352b8b1e215fcac25f1e3395940770148e5ced58410183d15c719f8ae4c19d95f910f3583de6e0b63b938e2d5ec670755dc24cbf08f9340f914c5534bf7f1483947b6b40626f1c7fe0e4d8ed843027b14", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "017a940dca15b1db9a1e3f54c0aa8eb072dce11650ed920a2931c68b930c34b5811d076c0a3d10397a18bd0a7a87faa78a6d984fb48b3ee53d20bcda84cb2ea7baad", "result": "valid", "flags": []}, {"tcId": 102, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0400aff0e5216ee7cd115cc630909d20fd3c382357f0744480d86b177af4ff9471a1e360532a92a58252d93d1ea16ab47f4c090b6aefdc6a7a3778d15c68a866758b9a0098e23c4372372a527be2c6c3117380b4836d5ebd0807b80cd9c6efd96a7cf0757c2a635b9ad6dfe01157ff92ded313a78cad968082af33d136873f808d8e491626", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01b98447f0c3a57def592cc0599ce7406f148186f94cd307bf87e4ab273d3a9b5ae6369b26692cab5c24bce59c13959844077783ecfeb777fcf3ce998f656a2382f7", "result": "valid", "flags": []}, {"tcId": 103, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0401ad13af79e8bda33120a2a924b481001117479c8fbeccd0a6af4f50edbf7df8e395e32ff6a1e5b2c1b874336c44c65e1eed209e547b3c5761cab96f56daf17bc69300286bd6b0206cbcc7a99e7807f15df0ff83cc17e4474f1769c473e7141c384b8a39f5b5abcdcfc70497057ae7e09b707ac3a71b824a55d4d8648c5a95d63bd82241", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00a3cd900b9885629ae8d83fd8143e7d8a291cd933163bc658f37913511837355082d5f39425ea322b19d61db3dda05f255e140eec4e98a7a10121eb2918dcf126a6", "result": "valid", "flags": []}, {"tcId": 104, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "0400d4d76b40d97046ccf02d5bd131a5452d91195a13204b6eb4f71a5da9dc9156f619af8bfae8fbaced475c27ca89456f35e547688a6d0d9948fea49ef4b75b5e839c01738db8b3d3fffb1ab697fbc8146a70cdd657d307a6e6ca8a338661a08131e05c680ee9da4c88a90fde96c5630227c2fd4323f302d53a0b5121163ccc40befaf533", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00931d8aa38c530db8d5e77daa406d166169c9ceb74ccf7020965673d09133253ca230287ec9997447f1ccbbbbe868cb73ea0e28f4b67ed49132995e75f417838d57", "result": "valid", "flags": []}, {"tcId": 105, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "040103f36ec425dd88e5d82d1f8d747c93eecc4b46ac98cd364fc678bc0d6c79f8bf1fe0bd28235ee43fbd0dc237332cc2ed6eba8c7a5610b5651c9e0f2644587ca3bc014dde8fbe83b569739b860d7b1edadc7bf73f7f0e7a8702eb488d230da284322ce020e9c8831298da14180ab008465e6ded1f1ebaf65640d92ccf29abb6751a6f6c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01270cfe9b869d88e7dff6d45fc752a4ac72ccde73cf12ec227c274328b001a9f24d92b00ca0326fe19d69573d5b3e35f822ccbf4c83a3b3bf6e3153f0984281b3b6", "result": "valid", "flags": []}, {"tcId": 106, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "04013f8cc5569346bf3325d8405fdde9fde71dd1e953c10ed6215b4f4010f5bbe173718a8e2e6d9f802726cd916e16ea1cd3148c879f0acebd8db2628f589c19aaa5be01595d669cfa786bfeb9dcdacedf563d04059867898f42e8a157c91133c952a97d90389891b3647875c822eb48f761930afef9b068853efec0d260d8e51dbcb6d24b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "014df04e39e8bf5f6c34472f94f6f428962ac8a39d273377d0007231fa7f5f79ed896269f65bf84eca44e0739cd8b99f2e67977eefd768553d464c79e7d4d90a0ce8", "result": "valid", "flags": []}, {"tcId": 107, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "0401375cc4e1a928513598081ac4e4c0632c24bd1997e4850d1472507bc71422fccb64cd823d3365c06e3f08c9fd6e6e476a68120f203ef69ca11c70889e3809eb75dc00d1ed8d7ab74a72462367cb93e843c60389bfceeb2f869aa33491961c4b8389238f5db1f78b39fc0923a54d9a7e0be8edcb9572b2dbff0a729585382bffa6901925", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01d3772b252c9ba35ff3472b280a7635ca04513ed02e4d8c25596c900c37994d26ea774737d9a9a8f8618a6ef0c1019344cb3f9656a6369dfb1d8f2a3f28b4f059cc", "result": "valid", "flags": []}, {"tcId": 108, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "04006b6c4149ce3b4fa619e360f0979427d2c6bfeb3fa41205a0ced3a437287c711cc6e5875992cea313ae3d2ec0df6e4217c8c42bbcffa732c403e7b4471d0cf3953300ad7f74c69b7308b78747f73b4e3e66e33bfe4b21d45ac82a0b293b0f635addbee42a718b726aeeb9df1355700497900d9cdd0a42e0228cd1794f767d3bffb1c7a7", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01c548cbdfdb40541dab744cbe5c45d96a4f0d1cf2e537d33a9fb4e4675c1ef54582c19294fd17c5fae2c51a9b18c37195e0eac0d9b5ff47a643df375a708dec71a1", "result": "valid", "flags": []}, {"tcId": 109, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "0401a7bae08725b97e8ad3d91843a4714d9205000dedea86076105330730bd8b6e5a683049496cd9f08b321cb2f3f8a05badf2370bb90ea1a49236e3fefd91b9c3d76c008c50244040c65bcb86cdbe193eaf6daaae2ba3a328adebc6b72c12736608abaf9c1de7e7b351c8e75d4ab801ffdd6e84b7e46b50976247c32515bf91cea3348906", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0052c6c1b82b8e26f7ede705a8b484068b501a8da469caa5c203be018143618d984fa9421b135e1ae2212a27cbbbce3a745780d1847059d56c3c3dda474862a7904a", "result": "valid", "flags": []}, {"tcId": 110, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "040090f74a2c8a5d2a990cdf38b185c7176b37ee50b70559ed85daff2fd45b94b7f9df34ce57eea654427bfad8991f6a5d385342af3a5688b1f8c724fb7e78dc18f20e016141f2b7bc942286dbe8344b68e0f9a2d0950da65191f47eba738abef20b9d107cc0cccf60bc9299b75f0d14147a6c7a7d5ae67a0087eb1f4b48a2780ec086428b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0113de20aea691f5d38b80bad93c80d92377ebb7302014f64ccd893fa9e67d384a4ed1a5e3fce2c84ba5fc379a0a6bdacdb6176594df876d3d13fdf1dc824da825a5", "result": "valid", "flags": []}, {"tcId": 111, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "0401f7be229dcb35e444d3d891bb04ff5aaf270e8f8d90afd65afb1c56f8c77eab3d32e55daa31da9aebda76fb67298e3a1bb71505d3a5b6c5af37366c88041090e96e00cf3e132fd88384fae8753ae32ba669e96793b69d6a815365387cd9d4d21ad2862e4b2ffca1fa8ea05e34275fea95ff1dccab750bc742de12611cc3a135a9daf9c0", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "012090847293ba80dc7a8a4d5038ad6f3ed436b5a82e7a4a6d7cfaea1c19d6c74d3f707fa0ffc9f15a8c95c3e040b4f1b5d2b5ee08b9436410e36fda203fd6c5fc7f", "result": "valid", "flags": []}, {"tcId": 112, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "0401d8578b222092103bb17764fda334974b121305c4c1cfaee7d86e814dee33057a03bea234546605cf7fba50d2d57abd0808760a4f8d23f724c00f9f5dd4b026f62801923325a37b0314c7a1d185173b48c14051bc0efc9bf3e57ae778ba537e90c82cc0d42dd98e9ff5a8235ede5c42867e961def8f9f66a631168c2e3a1099c9c2de06", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0199aaaf777a6c29b85e0ec9902f4f8d004a297b4570dd4677622305f4339ec726afb98b554175b99b8b3243f12a60b79ffe259471053e14c768a18cbf9ae59e149b", "result": "valid", "flags": []}, {"tcId": 113, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "0401badf6a411a405652d3e54b8fb2c5aaed8eb9ada9f06effc2e65299053b8a3216e0b5ee7b256561dfa1779970547b1072424b86bb6ef408cef575bdb02b79de35d4014ef3a47c25265b1cb2f28d5963f533f7acfbde34d45fa1ae5fae77b4c4e0c894280247664aad91c31a011fba50eb6e34bdfdf28f3e406a602c5071eebb632cdbe5", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "016b74ca4a588fa48cc5fe738aa84952e7d4908ef9fb69a4bc470eb72ed86b1c6bca0b6314939c311b0e3dae7fedd8daf96a37b7420dc556ac2bbcbd0dfdda281748", "result": "valid", "flags": []}, {"tcId": 114, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "04018b9237723c029c551b1e14492460e7d2e4957dceee536dec92bb35fe8ede36e6b1cce155b69a0d7212c2b4f0ba89d3f6fc0e7f6777ff5c2eed8c71eff3c44da8480125bee78039fbd3c339a58bbf625e50346a3e8a9c73460ec68fb028fd4d14cd6315310e0311a0ec4f39fb3408519377dd4ea9d57779003862c312bfd09a9a1f9659", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01d81c812e14804d562e4a89e9eea7d26302cfd755fe9221fca577e32fd5bc534e3fb2d81b1701afa0b4d9f25bbd1b2d7ec984da404014b5c23d8b2958aed864db7f", "result": "valid", "flags": []}, {"tcId": 115, "comment": "point with coordinate x = 0", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000d20ec9fea6b577c10d26ca1bb446f40b299e648b1ad508aad068896fee3f8e614bc63054d5772bf01a65d412e0bcaa8e965d2f5d332d7f39f846d440ae001f4f87", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "017fdede9470801568298e0a94cd49a0e81702b6f4ef916983ff89e4f01ade2ee13001c6eaa677499a9de7a48b7c7956faac580e63933853134dc96852264fb23f2c", "result": "valid", "flags": []}, {"tcId": 116, "comment": "point with coordinate x = 0", "public": "040199ff89acaa60d0e479c8168736a7795ee0c1b5c0b21f6cbf90e30682cf4464df9232343c91f3650cf01f7bc2b148d8f3b707fd2e3dff502bca0c03941a9afe631c0084e27155c0903dfe65d7b5d8b1bdec2456ca50c977a43f44e4fc4dd70d94b29c4469655981af4f0c2181f19dc4481307e69e206d4e0a59d48e43f55809139ada7c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "005ff9d0e33b19a1ed65408254957a43c0050b195dfd8feb56472d3fefc463d95910862f9bb0a32d98053763333f92332637dabe2a4f9eebcf48a4630ec50bbf132d", "result": "valid", "flags": []}, {"tcId": 117, "comment": "point with coordinate x = 0", "public": "04014369b6e6717e0854380833d974beea9cccc01d7fbcc39245bc1427104c214326809fd1a678f89612d087df5e3d5ab1855178e01fbc6712482b3443cdbbfec3c77a007e5a8786aacae75f9f7e59b3b00cf136acdac42ff0da06a0de82d5e3c4169a9b5d63cd7c0c68308585f6f31a300d33c010042b44cdd0e6a811ad614ef18b6d4e06", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00014a36b98ab57d1d599351272b3131fd12e18cb0f665cc9805b2402c670ee309a85b6e0c2a9cca28d5a4583ccccde7b8d5aaf30c6780cedf8a0b9a4f6a49e9c1e9", "result": "valid", "flags": []}, {"tcId": 118, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0400b08df0612ddbbd8abe1cadf654f7b1500942971cb17fb63936750308bdf3e91daba0e2005575de7e7038f8379e997596d6696e5722489f7fd5f41a85f9b27b206e006d4f3558ec858e8b7b8f31af480a80484223a6f3652868a36d8aa0412e79d57c5d32297d80b508bf5a1bb52fb439163acc26b473033dc24834e0a44dfa43480a9e", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "015415d54abdb8536e1ae2ac00051e2af3c042a821530165d1febb6ebdd74781ebd3387f9dd8845d31a0331e4eb542085b61f0f9f408ba5b4635dd24dabadaef4701", "result": "valid", "flags": []}, {"tcId": 119, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0400b574f6df47932a1827e36c9441e9a61670969817735632f1ec8fcde60509f6cc99a5c6530a96003d033f4ab63836ca292652b860641a2b98df83ceff2bf2419f7d016c80742e0392dde7016b106e3fb976bd5d8f46a8f0e74b900a8d26f6928b02d4fc1a97d84844c2380f6fd6249bebbba6e5f87fc5ea2edc1362d77e1c246651a56c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00e5a33aa0d346ba57866741f99011f145adbf2dfcc10cbc988c86e8a26e977b419ac748b106f14ff2fdee5a326616ab53ccbe2d80e809811377fe9af2032ca18464", "result": "valid", "flags": []}, {"tcId": 120, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04003174c3d34077f946f8986af218ca01f31efe57a236718eb88df26a3cccb24b30f1f8e9d4fbba2b953e161bd19447039dbaf1bce04c3460e1e3a2170e267302d2df0010cfe18ae6cb536ac3e14b3e60ccbe52b29a1952a47b5b3b1f4c2637ada534b61a5d94f658fc386beed612467f3dd1cbd8e4d6d154566ab994328d2a58e69ca7a8", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "018779cbba1718bd2b7fc2ac5cb471308576250ad412a5f98e28d14d76dafecb8455603bb24591b8e83e8a5c2c5505609445bf7a019a229861351d165801285b19e8", "result": "valid", "flags": []}, {"tcId": 121, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0400f87bb74f77e9bb4611af79dc582e368a51f45e6ba1e0f3a56ff25a600a5afe0a20edf6496204fbd2e73a9a54d9c8807fc0b95301df19ca7d67299f44b280bc69630144cd54358b301bf341e7b0db7efff4b0010f179262e5fa181b30d19139129f42464b71ea2e0688ba3244d4f7cbe2f7a8c2a2b379c64a72d6bfbf87f6f1a87bc06b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01f455816acb59e6189e6c14dad7a779958b9b60a9549728d628b0af1a38bee029a717b311fb2b6cea5ff3c8aea096236c67c6a3ace04264ac7ed1beea2a4f5e9044", "result": "valid", "flags": []}, {"tcId": 122, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04005820a12b529aa3906afb061203c124d3b81f399b67f48f5cc3b5fd45907051ce0536dbdeeb4b3e4ea3e2c37991bf68be9a1a1c609a2ec7b4ab55dc006eae440dda01cb77d7df78b9874470c3dd268e9199f98f832953aeb7b07449d37904054c9dd217daf2f0ae6db058975123c9eaf9d886d5c3d6036907a5cff3c71c7a7a85f62f0b", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "002007fd98b4e334d9dd7686ae5da379c3c3a0512232844fa00ece76ea9421178b9c154942c0981ca2b8187161d59ad36ab2daf1753e3b59d8fa5b4c9bf224a7377d", "result": "valid", "flags": []}, {"tcId": 123, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040058e0d20d8a188d48ca57d4b73ea4e9f453a389d5d8fb948737ef55eec30ab60fad580d9e12bb840f82e74950da3d12be7266457bbdd943b831f8ad0d3b57e6243400d70c27ab9cb29f9d753967ffafd27551c78a1bae47441ad38204134f83495deb198247d875f19af99322c2937390340b327d9e6a58b41c76a326c6efbec02c9956", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00199e65e06f7c56f3c47aaf9fb6651f5d2fce23fa4b93a21dbbff96559b491bb4a60cb2b9f35b7b45c3a838828a53c547543656c657c916597ac04cc005b0205bce", "result": "valid", "flags": []}, {"tcId": 124, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040014251e9b87ce81f7f149770c007f1d6320b2d813d27545773ccff4ce7484354fdfd19f9380de8cce76ceabd6530a0c812a34e4404bd82269cd012e1a17c71c9d6600fbcd443052cfaf03760bd5366378308fdd6bf29379f9b6b677c9e88b036ef26a3a276b64a7519e0d019a00760fef387dd483d0d58ab3d30158cd18b260e5041589", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "002ee30bb081a04b7c585d12cf2e0dcce94938deb0b72a5fba80160863854cbf7495fc4e8399ad06aa6ea05957b76281057f0701196b983371aeec0e4e31f6b42b6c", "result": "valid", "flags": []}, {"tcId": 125, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0401f51838aee36d1dfbb000c0a3dd84755a87d6dcc5ee5c78c88b30d3fe1549a8918204afbb742acd9b5120f109dbfdf9e16f0eae84bc82caf41349252fa9812a944101e0b3a7669bb3aee54e64bb03b68a622831150719a21d4328f732a649666a2598d5ad0f62b00a265a9e08f5ec0b7b6f9e1f8cada04378580b4a287a3af4ae395315", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0189786700902cf0c1f5479d839ee4e2401bd249f413c04aa3018732fc5ff9c46a637479f93427e030fa474660e18b2cab46f6ae78f331c5bd351cbf32ea6791fed8", "result": "valid", "flags": []}, {"tcId": 126, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0400a8bb7b5f4257a3319c6dcb1df58988cdf60453e439d230f4251632ff92b2424b147ca40824b25e6ec5f71599ccfd13ea62139e0eadf250c0d0da626f4f1ef0ccbe00494fa65939d5f5d144cea542dea27d7b26dcb5d5d6e813bc5307753b618410465b1b96bed79c98b6a60b58a0bdf83608c81a9ed362b66a4c61323b34d2a6416c83", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "007068311b5c58397c1637bd9af38b6ba87f10851eee675cbadc27860e78d448abf33dec174d982dab6d100d315c557872bc8f7aff2ba0ffd5082ee203840a7d64c7", "result": "valid", "flags": []}, {"tcId": 127, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0401ec49de21d692fcd7084280460dc1410b5ce14855f530214447b6f53d03b019b8e1fa7a862eae55fa9018fc93579936747c96592c98eec62571572e4b40ac8165e1014573cee65cae4d54389e8d74e120b308298f15b075a44ed0f50ecf3e4ab081aca152c614c51a7b312117df7fc607a861d2dad102379bbabb8d72fc836ec2c8230c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "004e26f46b5204c8ea45f80115741ac6b0ad0fbc34ae30d89b85c1a390ca28b6b83dcd7448cd413a0057283dae285625bec2932e933ff348099821587beed65d37d7", "result": "valid", "flags": []}, {"tcId": 128, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0400558582568ff68175cecaa0652bef373d6875a196a30d77715638cd45059e9c207595f92ba5a02990a9b7a05fc8649365518cefea24601ec1187a9b6a6fd37d3e82002c2524983d5c3d4c093599f00f96a7a388e2dcd18ddd24923ab6f8236dfcd544721fd22cdaf20fa51e1bf9f9a9e998f34154f26624827cd2cfa1b0e634c83056d9", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0159f2d6523ed3f373f2f955bb302bb94efb742c39fc081da1b96b36ce5f3fe07bff07fbd225dba9ea2a82c3bff8939b3cffecb5844be0ece9abe5c51dbf0212647f", "result": "valid", "flags": []}, {"tcId": 129, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040188dcd8cdf3855cf7cf7f622511c649aa96824bdcfe318485970ab2eeb2428e49ae46e279e3b02110839d5a4471f6a236adee760361043b3106488488ccbd2cafb8004b584ac1d3223c7a01a725c38c182b4ca92858dd2e769f83051db953e20c63d45d6972c6659fca664d9708b6973905e7304c396c5f739a0fc66813acaac1ec1b0c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "015b29e33f7959e2ca91f58a7e9e4b132a0989687f5a39f8bd61f7f42275cc256059a04f05a5a7a386edf071f7c981235c3d052ff733843bc9738fa104b9deb9245e", "result": "valid", "flags": []}, {"tcId": 130, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040113c71cb3b6b32f6d2087978f3f0aee36ea8cb1f028d2d98161f7758bbbeeb3b96588af97bcb4f3fffc457b90b51739e7894f238116e985caacff3e751b56518b3b005b71f5230598689e6254788b894003dc91ebd953a2fedbed23a068b9f943798bfe4d045f7b0bad536727a73a66f250136f8f5006753ff04b00a148afbe70efc143", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0039cf5009c6411a5b71b0453f85b41f506b72dccb33f344ad257244dcf9c524caa8fdd376b523480ba22531904215d26c8a818b8f80a4aa1dd111acd8ee84cd453d", "result": "valid", "flags": []}, {"tcId": 131, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04017575a3f85834ca4832bf64c59df1bf30ac8304291c815397dd9d90c02ae055964110814b158f029d40b36b2a85a9568679fb56a5ab4973068d9edd80edb7a6a76a00efa3ca3101158694ba599d27b4ed0c439977d2855dd57dd4bd311acc3882b09967af64fa34ab08caf24d2acaaf32d93cac8839d391aac51e2cd067f4046b5f87d3", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01ba069254a951a961aade066ff04797bfc9cdfebf2a19e4f4b724024e316e8afd9ed48b636a4a0d78cc4d37d9402ad1c981640f90d28f4e566ceac9b66c4000d3ef", "result": "valid", "flags": []}, {"tcId": 132, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04006dd0ee8e43f615e30b63dc455701661c9dec69dfde65ab7cf9721039e3e5d3cc5c12f8e4c0b6a3422bf13d32e4108255290f9cc9d6e4cfb9c9ea165f9345e1759c004649f03b78e5dffabba70e74e96c5362fa83186bdc44820bb0eaf1bbd1a51976cbbcfc03a986bddf93996d960fa77aac46f9b7a5c32c62c3402c400b3165c1ba21", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "016c4bfe17c91c3ab2c60e5c9c2fac1be4893f80fca05647c34c82ee8ee5e3b3c89c6e87bc3af444dfdd1d692a3da45a45975bd50774471313d5b7d149a5395ba7ed", "result": "valid", "flags": []}, {"tcId": 133, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0401a6d5d068b1908177a1b18fe078df8f89945870ca8811695b4470048ce0c34443b9a7f95f8af9fb6a4ca0285d1791ce975ad5a65a5c9f6b3df805cc69b3984efed401025339e40a26a3f6f4fe54101706b7f6d475b90f58f7ca5817ab5c3df4d83844f42979dcc88dab0785ea242643159529bd1ceed9be4eeeb2302facc6b440b58264", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "005191849104cde5be2563c0133065ffcbf4bf411af32e09865989b394ec75e82b4e4649d33125034f2b8cae1c29592aaa9e50700da74c088318d720a68f58653e12", "result": "valid", "flags": []}, {"tcId": 134, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0401bd7d67379b3096728a366796f7014977d11eff92f1fd30983c7452b82fea61f77ae4a431873e2c5588951b663504610d1391b8f9122fd7896ed45b27d2320382a3019547260a021bd86bb149032bad3c6ae194445899573c4007f44cec88e232e43d027a362cf59cf128367cdd595360e0bbc2a25aca863b097757167de4410d261b16", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "001f8e925d5b2d22f7d1d7fddaa3575fa32d446f99e774db282c93d00beecabda9aacfc644b9cecd76e7f122515b6ebef3b56075f7a2101f9c492e83e62147d09b46", "result": "valid", "flags": []}, {"tcId": 135, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04009143dcc8d445d7f664481fbace68900453949537fad73756d818561e59448ffb49022b737fc3e725d6c1c45bb0b068809228b0fd8961eef220612be05040c5402e01f8bd056f5f9fb3083e9083374864731d75441a1dc52b41904d6b2eaab805030b04cda6fb42eaccc7c9d50656f0a85225120a0671b1892f2c118e7b538e5c55aa64", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0072a7f99e291a15c45bc78c484e4fe94903a958048126f3c2405c5ff8bac7dd07dd96d450b017f6cbf6acf69fe113fb7a6766de8a956ec06f772c15c84de9f4d462", "result": "valid", "flags": []}, {"tcId": 136, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040080faa5685c19f085101f010e83b2f0100962a18478c9b51aa0d752bd0e439537892a107c34a553c18db4f7515f2ef38caf8253ad2af8c59d84b7f79172cf2eb4cb01e9c0328270b761a0c915a6ed29ee223133eb466efe2317c6f8ad2b3c8d88794e4aba3f966b4aca5594c9a74b37b4503ee5a0794d59c108666897f5ec9bc7bbcbd1", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00fe7b90eda1f723c5e442017dadde8affe3ca80afc9f4703267626f4774f06e2c21a5d9c6472882ea8806a9139c4a740f2654d10caa5e3d7bbb7e59713fa032b9fd", "result": "valid", "flags": []}, {"tcId": 137, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04014d6f8fa157eebea8bc272862d9f8fbee63da5be953e333d06b7443cbb96d0c464a171943b82565ea710c126498aeb7979823b2eb1e81bed3ffdd6e40f4521e6cb900701bcf9e45d6ae4899d96ed59a8015c454735f9efddb47598fbe047e8c4021d786db3539da79760358c0f928b2b72cb936cb66e4fc81ff079ecde8e18989e87950", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "014063b3dce4c99f5f53e5f360875c02bd4534a83bbb779be9eb05e670595e57a708691c5df0a0062b43d0fa349db201d6279c131d84e9662cf82fbeeab6ea0294b6", "result": "valid", "flags": []}, {"tcId": 138, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04016361d19207fad3b9cf68a0d7cb224744daf5c18c0d79f060ab1369d7f026c21da2dea098739fc79e664bceed06bd8b8f29471907754319cefd9f216f3226a4a87b00b7007b2df9a123dd81df4f5d5213580c174fd68aa5de23e5386c1f76bc6711babfab72cf9bfa328e97fbe9a3b79ad0d39a778e3c64b74a34319aea0423fa270067", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00765188ff6d0db190914b3ee9cbf633e9e5c2607b7323e70e76c3cf3e2b3f1cdceda62b5075a8d43b306fc83e5e4394cba6be0098877b2000385508d0942cdba46a", "result": "valid", "flags": []}, {"tcId": 139, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0401123d8bb17d84760ceb4e3ec98387478f9587dcc05b4720d8834ffcd82c06f59aba278365e834bca2736ed6333757f57920256bd9e1b1bf3d43b620896dbfd877a3012135f587ed6a6bd6b095d41c7abc2a70c9d49461d3ed7cf4e781c8200583a0e8e94ab8a00b52a7d0bbf182cb3bc0832fe82cca18e53e78b049bf9ea6ac6017e3d8", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01b1c9e8a4748e6e1aea3dabf37ae74baf2861e482af32309dc93e7c7eaceaa584416445a1ec4f0f24b3938e118191a5bf9e72885bba5c282275c49f5e4bd82e2567", "result": "valid", "flags": []}, {"tcId": 140, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040061aa4aa680e5a327ee0495beff81c9a9601a4a42a830725538f9940d234f8fec38ffd21537094492b58ecc4102eddfe18ee8f5947c4736dc60066fa8d9b8230f4001730296bd1d13ab65e85adc69c0822e20e7dd739755bc28d7231e79ba1c31250c1972252c280896f30a26c880a348041b0958ee0443c2e403eb9828599cda90f7a8", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0153a4709fc474476e76948b04d430c2ed708ab697bb54589d35a7c96d33632072b4a37deea3428d8d0a933c7bffed068d92a9061f0fa39e62e7d552b31e4a150509", "result": "valid", "flags": []}, {"tcId": 141, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "040086b5bcf4bbd05a5b5651bca9791e550fec3e9b2ff0d6bb255b2d9190f5946439b53f9513a122e5ca0a9ff12e3386b7524e46d7f56167ca6127ce83e67bf5dce6ce00865b2083d5c0200285ee7ff4950922a8abd4eef93d8ded25b53cf9af327e6e4c30c4438ed2c40c67e87835518361c98fd743d859f6c173a951a95a99409e831959", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0167d0da4c5dadd25f8787a84a96445c254d8e17feeeb7d21af6712e307cd7351ebe185c0a527402a5fedaa1b26ace5549ab13b208156d14389fd8133c6f4fe31e2d", "result": "valid", "flags": []}, {"tcId": 142, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0401a09aca765c949b656abdb5968055316384245ab775e8584119405c85ab79fdc7ef1e079a35bd299ae149d65f15ab0d64912c5c9a62bca41bdb586782e8eb0cff9600258a50f1bb542dd164b8943bde1f2078ea7d3e89181efab242da30b1a12562184104449e4d759dae9cb9d075c30456b1a4e48740dccad7b59b17dc1c81d517f2cd", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01528f5ab758d9b6112f19e1021f5089d526cc50911026e27d44ca6d77220c816b2326a2dd6b9152408813bf8d399906cb1ae33a52cf67e74709d796c70701710457", "result": "valid", "flags": []}, {"tcId": 143, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "0400a6e216a09b636032f39eea552192c242be7ff5478f7f7f9b0caa25b8aeedeaded33ba66b5feec7e75b0de3e7fee142f250e6cb4c7b09a1686ca0bd9cf2d2d48bae00fcfafca2391a55ade0a77fb9a381cb0af16253cd3bc58723f8527670b59567698e7fc3a17e6d26b3a8a4a617e2b12e6d60391f503117fea2ddb46978b6d2d30a26", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01d937db2fcaae795f4aec6365be81a514939c548357c6140075db4a076be8c0d3a6126b720b105b3d9cf6a8307346cb832fbe3a936017a6e62059bab6723e885176", "result": "valid", "flags": []}, {"tcId": 144, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "040050b862a82a0ba92ab45b396c51aa519aa44d117f55760e5a4f1f177dfb5bcdf0d54fb99565e9f7d6757c04f49456aea87075560b1dc5145bfa4068684b04178a8501e1c68a57b38e2281806f3db7b0af0e3c2b5e52d5d5d4890c5b9f5d737df9a706e631e4453f6e79cbc28d57bdc604aea8010eb491f5c09feb20e3bc0c0ed4ae44bf", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00cbeb5c9df0bb30a8b45bc17a5bd255e2c8249dbf9133705bf1e502d4e730edd3131c8d370aa7fc78d46b2291181bb40ca85e7a0ac6077cf4367927d8c5b6744306", "result": "valid", "flags": []}, {"tcId": 145, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "040057f27cc7400b6f9bb3a0f723bc0670938ff888f87917100a932115b12753a1fbb086e5b225209b4f70703aba374443f11fcbcf8779491e2297fbae7f18be2d4f230006a7f04e4de374149dcf596d94a6d7ef8d30d9888bc7500226ed7bc8560729b7c4b4bf28f4f92d7f3b25a5ace886e8be3ed01af02169caea765dc763d0da9495c7", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01a26aa99222e44ea4d6dcee229f21f40f723b5cbbe92d56561252e7b09f96eb271ad92b3e16464608745615182fbb7050e2991b0afc040f27ca74e358346466cb8a", "result": "valid", "flags": []}, {"tcId": 146, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "0400e04a04d1e2bfa1a5f6f37867ec251450d46e6bd28067cb99a612f6efabf1f72c70eaf72d70daab53890d54806c46047766293de085f0f40462b2f56ac4fc6811bb00a20fa00fe8ccd8b007efdd6dafbdaf91b10924c0b8d583ce829202c8533e9815451719f12cafefc3d3bdca5d510247d59ffd20d539e22165f11b5d10e912f1f9e4", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "009a6c017dae44957db6ab3476a9dbeb660ea54e20ab785d5a341e0e0265050fde217900718908e1763eeb66530475fc0d0a1346c6c3bc2952da7cf034df18650645", "result": "valid", "flags": []}, {"tcId": 147, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "04008d3e0c9820d2b49711780849ab9349de295851b69ac0ff6b2eda1fddc17e97c7ac2695f28ef6ddc1e16062ee48295fff5711cd871992a8a184b8e5b49d7178b5ff01709e435ee6c7ccfe6692c7f169ae357e94feebcad5d6ff0da7ff55a19d1409006aa6083f897824de4c0c59bc80d40607a9bcfdf666b9aa388fa3b865d76c492916", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01efd3feffad8d28373dffc0e3a9cd544085ffc28f5bb80697801db4ccb983fa2328222a429e7d6367ddd4a40811ac2adc8fbd8ea06ea408809d5b82fbed3c8a95cb", "result": "valid", "flags": []}, {"tcId": 148, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "0401c02ebd54765a3825d7bfad5635a98d97f73cfb57da9c42c1f464205fac01cd899f7e095a1039b47e0fcbebd65b6ea2c486a7bde4900e9548d8a64a0e6f39d61c9501f5eb9d2491b42e3d02c20ff853080aa2db823f6091a62a2a6bb52d09d3c162a3ad02e0242d243a61ce0063ff8f8e93ec21aeca4ae2b152b8f68d40dd876f80e7c3", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01424784d1fca3b4c7811083855fbf8662e1f0ee52b5ffaaa0cf890bb904150512a75a1077ead39ae37ec67d5a3c03d2fcb5f9f695f9918cf4872c5b4a757d1d0fe2", "result": "valid", "flags": []}, {"tcId": 149, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "04004b04d3d1c6c47069c0bd44973e75ac753c9f284b0b1d52308bcdd9a1e06307a8ce7af09362f8cc6a2b5b4b2349e2830956abb1191c7e84582db1228c2ec25021cc00c2af05e5c310388c8a16835d06042e490a9ea8c4c9bc532c2e7d46f200107a97e0fbc5d9faca9dcb2249510e199896ac36e0399b0f17650485eb748e1fff9c77fa", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0132b97632e57be55586e887d26dad41e500bffcfa96368484557f347c4be821302de8f3f72b9865834d579cf696406145eab512e2f96d2aa589748f64420869121d", "result": "valid", "flags": []}, {"tcId": 150, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "0400cda2db7fc4f9d4c1ce36ba82a184b2fbfee206d9a5d050d2bf1b2fc92c0d0b78eca3b51d724aa5fa48f666972b473accedc49724ac2b3e779d4c894198d9ab937c00cd13857d84871837f77a54f0ba9d18f6e942d1d6d2e7167fb53e3585b249438289abc68927ac8c29d65377cc73f85d113511f2622bff697465294f5cd1a3772839", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01baeff727530ded5f903b2090f922b479c4631afced97ef1780984d9d98fe06a47ef0714ee4f2a32f08afac5a583a516229539c2c4c82f361b6f159222d5c1376b0", "result": "valid", "flags": []}, {"tcId": 151, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "04003ab4db21d5645f42bbf1622e26cfe7603bf222af84549dd484a62e98ffef26c13da61aeb5157a91d70a828e96873e25f06c45d652509307b0c998391f951d611dd014e599e94f3275ce60190aa63bd8c8b6edd7ed11eeb1385d0d1137168624eb6d8125ae9b85461187201988cc11bc557680d5bbbb4f54c27b4549831b7b6f1e59e93", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01855a6685db05fee3eec9dd11750d31449e40696e8e1c6a5e63343de2dae83147fad337ad9020ea131fab8c4ca30f4a03e0bc628a4a5ab78a09e071fead711543be", "result": "valid", "flags": []}, {"tcId": 152, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "0400bcaf9df5986efa542d7ce9eea05e557c393a3a3e3ddfabeb47549ef9a2924ebff58f7fb5989d404258791659cf421cd9d2eb1ef6cf21fe428182acb72aadb3fdc0003d638efe16363a8af869ee85dad1c6f003d4f4f827a7a18c75bd7feb330133e5bd297abf56159c50c0d04aa2e3b0194d8de2e7d0fe4d6d7a8901fd362e310ed4e6", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00a57bfff03140611be23ad52b3f0d525abc854f830d3e4835d801fa9754e1cfc12a4e6018958b44ba828087ecfab3ae917aba090be1464154ace55d4c481ad83145", "result": "valid", "flags": []}, {"tcId": 153, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "0400118c04c32a75b07702323ca226a4ace054424b1a6e6edcd4011a035f4bc0789f6151ff849eff8949554fe4d542af1f03c79b369ff4c38d1d29b4a3bc41d5e05bb2010f4b8d511ac5b1a6534aac9dd2486b8cd07cf3d5babc24f74f537b04115a0a8f6d797798fc807fb002746b27892ae30f751034b680b1723c2ee859bdaed0540e77", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01519ba4179ef85a4b51b30db2f6e9e2f3d49b1f3e8611c9ba3286d78ca645d29d7a163055fe44accfc724091d0842081a0fc39f0ff7b7d742346ef293e51a1d7107", "result": "valid", "flags": []}, {"tcId": 154, "comment": "point with coordinate y = 1", "public": "0400d9cb7a32dab342f863edb340f3ea61ddf833e755ce66bb1a918a42714ba05bcdf4ff10994f616a9d80cd0b48b326e3a8a2a8f5634d824875b6e71fb7cddd7b5018000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01b43cd9280faff242c6eb21243f54477a0dd91ff1b0b1a31d6011acb9211bd7c4e7602e488d4cd384face2aa243db2cf8c8220c566dcf9511feade8fc26b07b1d73", "result": "valid", "flags": []}, {"tcId": 155, "comment": "point with coordinate y = 1", "public": "0400703dde202ea03d1d673735002cc62cc740536104d81fc9fd8ebdb7dfa908f599d8fea46debc190a5b2ef5f4493f9b5ecd8da9407bf4fc8e1732803a74ee65f747b017c9b038d86afc941403facaa1e2a6376dec075c035ab2c1f42db5fcda3ad3fec67bcf22baf6c81b4241b4a9257f8c2126880e1d6a69a3e5ac7e98710fb24d505df", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01b248dbd8dfa667a10ab32af68fa8967c69496ebf80c11fd0efb769ea93f84f5a2968b7ed81b2fd9aa913accec701ddce0d1f8b43b1c671f547822f796efb12d559", "result": "valid", "flags": []}, {"tcId": 156, "comment": "point with coordinate y = 1", "public": "040004fabe53e63193571d44521d36c4b646e299b390efe50e5fa1a738e700586fe41bf543b07fe4fafcb724301246e8c096c499b8a5d063233aa748db9d2163d1000400928a59f3e4bec0464f021c5ad086456231a4e44f162fe6aefa7a2caef9031ba83768b54762ef90b1e508eddbef69e53f3f9ae215d4a0612f737d16096ddda322aa", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01081c5a3289354db20a929fa3d9607c2ac58cab7b1ffb2802e6a778af1b79ca7a68c7bc1bd37a05772ef8c28f4609557f43387b271fb5a274ae3e8814c7505444c4", "result": "valid", "flags": []}, {"tcId": 157, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400b108b6cb1e04341473646c80f8c9c51014cec7f079f968110ab35c0f05b24ea7722327b5eb5bca748c35c771aba67b232c820ed544f9d4efd43d37ec49960db2d700ac758a1e225e3db19a1f3ea9583ad9ea6e994568f6ddc124b8dab49bae8357b3c70537404ed4d02370e1637ed5916b43334859ece3dbe6dd26065c24df11b8f281", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "010e64a6dd87610bfb99a134039b518c8cc87019ed5a2fa0b3f98be8fb3b2d900381a50755739bff483e400e5ad92c016ee3174df8d528fdc08a176d6080c183e094", "result": "valid", "flags": []}, {"tcId": 158, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040124508b182dccea6ace2136e53a2d9b6149dd0ae450830666f0a9c844918f0fb3eb87f7f3f7707addd77bb12cd1e552ef12105c6867a4fe81cd1f6a4001c3fe6e0801576d60d07c02b2559cd189abaa703e36e29c66d3da18d34926ae821bd21694e15319093db25f620b6480e04a4c6c53b1fa388f959b65fbf8a8829b3b262f55e4f2", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "004af556f09b9d7a024c4dc941931d655d2231932ea045a7faf322b14f97341999a5a5605c7d31b2e93d56f9d8136306a899d82bbe2b61b36af2336a0ede70dcd392", "result": "valid", "flags": []}, {"tcId": 159, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400e2b5d2bcd2b483871b7d83c2db0e957ef223f65b30f45d4ada33725373785c0d664a8ca2c35bc35a952fd822b0072a960c60e319f4e06de6c785fd8ddcbbea18d200300234471a92ac2c5f778cba8a97f3b3c45cc8eac1d815b45dc02f9b74079ac56649093d43613005867b38f3f6695d50273ea2f68ffa81000c7895e91ec53856e3", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0184771f10891b6dab953663dc41600bb7b2bcd0cc6bd319795bc89ce2cca1883cf92fd6fa7b16fb39c3737454bd3c22f4fb2c2e86914db418b653a9e0dce3be5ebf", "result": "valid", "flags": []}, {"tcId": 160, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400aee0e3097441d50138803ff9b17806bfca1064feec209fd0b5ae57c6d87d9e5eb32cb0670f12fbde06c3f3ed8b1861b6d18f6bc6a2552266a240686c529f044f570100898b3c6501ebaa81e69ee019b879d6ee3715d5096df83961e10bc805d5c674caa98a1ba29ed004808931a9615dfeda8673cf39f0ce4a618d181c04a866189037", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0024f79251c86e06ac18e6404cd303c2fabcaa9564218c160c1fb7a99fb2c699b3ae65ca2da8f66ad9d75cee42f19f1d77f194fdf2ad7854186b977ba26c0e87cdab", "result": "valid", "flags": []}, {"tcId": 161, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040047ad264a373c4994f942ad0942dae8b23a329fb7a46e987cb921c28bcaed4aed60e5ea6fcced4b3ece4ab7d91b349e3e45abce93e9765ee2fe9f13d5f230715a230059654010a57f0e9d1ed46afac4aaf5b3228763ed2cb2b4d78a131636bd1333f12799779a9f0fec3ef24452c51e8e4f31ef6dc1129a454f079258eac10adf8879af", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01646e6ed95c47f60851b16b8da121b7a027ae9b05facb08bf52ef17e67a0efb1c49ef903de89082d3b34acacc5cfb63eb6a620d0e1498720e04559ee476bb9a75d5", "result": "valid", "flags": []}, {"tcId": 162, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400075daa66c8b4298a8a8cd395022bf2f27e52830868b13b1760f22bc29c99666f392385805b9490eca476defdf7df0d6b49181c3723770933c82761a2f7d3cea39a01bd89c2ffbbfb461f212d16e9a9047253cdcdf179ce763fd49172f8bfeeb68d1c1fd6e2fd6e6416a8b015513222734360f51280cef5f39ec5ffdc756e44a5942764", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0129e92c8bcf5e735732a7f876321aeb2655e8d226c228e51d5ab6fc7c05b63ca9400a0c25bfa12b0a7ebc84efd62f695da7cd44c1ced1ed5cb788c4de12a517f1b7", "result": "valid", "flags": []}, {"tcId": 163, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04004abcd8469f194d0ccc0db46492cddfb0552bc13062b7487acc38f59a074f682001db1addef6b28c5479eaf5d6b95b37c394eb91ecfe02f0087fc639700b490eddf0121398673af0d639191e3122dec7b58526df7054627f696a569892851f904382dbff7b61ed4ab1953910d27aa356095a2ca45956456c8d4e1ac56b1ffec95fbe8cf", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00cee0328e75f016d056986b4c7fceee7610237f69dc2cbbb6266659535541269f851e36e0888d635b506b8c00a8dee7d987745d8d06519d15bc752bd5756fe327aa", "result": "valid", "flags": []}, {"tcId": 164, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0401bd163989c407d5f8f9fd2e087b1473710c4bc2d6a97d281984c12cbb0615be9ac806c292c9d90cf35ffec665760193b1d7681c47f8bdae37ac50d8a40760a047fc0167cce4dc54e67ebf56407aec33a5aa20ba867c856f929fad778b39b0dc51249d24e390d7e33c72382c4a1d02bf73d605948a73a481bdf329ef7b7f04cf3a333c76", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "001e872a534bd079550d50a2def1d439083bb3423a58a923ded2ed7de768022981b7cfc75c970caa2cbbf2c4c7cfd9cf9846844e9c5400356700080a7097f00e1548", "result": "valid", "flags": []}, {"tcId": 165, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0401f3d659378997a75e456f770f34bfac638f1d38777cd0a77207cbc42b540c1fcbad583b93873163426eb91699f8c0834b13695400de49796788c592410c520e859f01946225c71e241a3a785d26d121d56145ea8f9768f3a009a2e3c54f9c876b899e81ab1261b2bd5ef99f54f44535f9cc76bf0f44e91a5f139e9927b88d16dccc4b39", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01f9717e8a71cfa94b943d41bf12c70bcf51437419b2da0b6e160d06a3187d781769c75d2a9f97a8499e396069a059725f47a10103b78e568e0768499112f2f16b79", "result": "valid", "flags": []}, {"tcId": 166, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04000520d4f93ad15bd8399697da58b203a581a3868c55c8ad9af9aef613e214046e56f2a82382fd9eafb1f5281e6f6e9f0bcbb4386ffd8cdf5dce09257cadab97a010011b425853ccf7987de724596be0c23f1d5e1c7d0932d2fe72c5f223b9d03a78b88cb09eba6c4245240f8549c7216e53a879c96668cddc01d51c7a52db201ab77b89", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0191c0565fdd561298755620d9a7693cfce185c57b6391e4f9b90f7b22288b4f3ea3d95dd4d91b731615646aa9cf66db8aa0c302ff0703026c303a8c3b1f3e15ecad", "result": "valid", "flags": []}, {"tcId": 167, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400568a78ce5d2d030ff850879ee03c201e4dc64c58588d2c8feb3ba9b2d03857af3c29cfbd789b797f8dba4b7470f0c84121231f356b63e613d0fff5f8aaea8c86f9003d67dd0bbb1d8588f18ccf31cbe5cd286422b708c386c1f81008647c824f694c1153553009773c657b2b84cdae98e52fb6240872a31bfb0fee3f2d8a94e5319e49", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01f86b35038af9371a8412d75e5d46a4fa76eda9d7be740b14e8cf19416fd5df2a4ced3064fc3846ae99542488d69e3879619fa9078cb987dd0d14860b724da9339f", "result": "valid", "flags": []}, {"tcId": 168, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400d724ed03cea80c5794c0f077b8060662dd744e4e8d4190f7f313c40597fe94bd2aa70b20f73ac4dcef99313608c2031c73f13ed1f5d9ace837c580fd02a4f2d6d50155ff556f046657ee5f50757b9078c5467fec8a0f7566ac15db168afecb7f514541d2d1ff87c5f67c511a4f61a91c579991489ad5a1c8c0c554d9b36d1486b27e72", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01d1047bf7e622cd428e1c76aa2c044a2058a2d9f4b5c179cf9c9d4607008181b159c156473e7d25fe49dd63ad150073cbc2de2fa9cad50dbad9a08d56eb22d8d341", "result": "valid", "flags": []}, {"tcId": 169, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04009339ef1c1f93ca5fedec1ff5fe30334eb123c30e6a17c7b65f3ac8461fb779075fe69889a42837b01eaf44bb7ff8984c0beda0e1b5278a62c07ec128caaf52d8bd0019c095ffad69ed800e223a8bfa55d21f588dc7f9f41b9d75dc010792b6fe243d2308f12dfcd312729ebef9cc407443c04b0cdbe57714615241dab8745cf6487bc9", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "003eef535275e15f779ec13e5cc7ff939c3dc152e79aad0c32a03b8cb9f8c449b4d9469b362e959887c5a6f20962783c667b32b3791fa701ed52e82d1e3d2229733b", "result": "valid", "flags": []}, {"tcId": 170, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400c25a9065377d2d84372580a2687667cdcecea580be1a5e72e1fd2adb42ebda8d6a70ac053e49da485732bf13430282fa4cedac64e1b0693051d1354d8d6efcec1201e2d2bfb7b44f535b87312ae67bea3d62800b69d43422fcd3689a1bceca6fcfe4399b7c318a5688d0b4d2333922d595cf8142584fa898c69426f4bc478576edc8bf", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "019034896afa681fdcfb11ee0c03e5c588a65239be8e555a89280b7a9814174254fdb496ec0faeb2a1d6816d276aceaa98a53e09efe84a752c09c501b2bacaba69b8", "result": "valid", "flags": []}, {"tcId": 171, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0401c2402f341b4e1c9755b3e102223c80563c61c41a64cb119958c41bec2dd9519a475ab84baafb708397000b80b9d275c13d7cdb49127e1eb29a65d2d374904d090b019e06c3da4e884d6ed935c4b92c84bc63e91cbd665cee86cc151344a3a363fa75ad56a9b804b4651ed85663114188e63cebb2c1887f96994db9703c1d0643812cd6", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "002865551ba95fccdb0c814b145b5a57c808b47d5b08259ee41174770a7302d74a2cc78c7822a6edfa19b22e8a0d616260f1851e0d5dba50a9188e45afcb7dbbab40", "result": "valid", "flags": []}, {"tcId": 172, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040083f99cf2767ce0cc8898e461f9104060a9c9ea1342a2aff705610469286592aa41f319c50c83524881fc42169d072550825cf92e5ad110abd77e8ecdd8ca09f95e0037af6a24d6dbc9c85dfaada6c5a945dd6349b9f1d1ee10f25f8dfcf0522f3623337bfcba204e7e8e08c25564b4fe933cb6fc145ffef82baa2fd03a89678ffb7585", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01b384b48405396fdcb2c57f680be52f91b7c885225ecfb4f786ce6c1490162b3c15ebd8e6755e469a7ffcb91cfd7406cf0ab934a4645c3a039941e7d8cdc07ddace", "result": "valid", "flags": []}, {"tcId": 173, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0400d7fb4d5592e7cf724e57e6ab95e158d818f197f74031318dc83d3c2bec5cd8486481fac97ad6b481e837abbf352b992c2264b16f563f8442526bc6dd05a6374df80161dd90c908f5524ee6b157a86f6734a25e140638bdd839276fb09b3fad93e7ba899d6b6b3ed24fff8d499ad98cc45a35d62b8c461f7cc25699723a033e5b1dbb03", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "008b00b65cbd72a51c279d1b59628d4e7a1a847277f6d8bd5311e5eae945c887e4c8024dc412eb4205c76103ea493e25df4cdf0011619e3efb290d1cea8c290581a5", "result": "valid", "flags": []}, {"tcId": 174, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040058499315bb2297d78f8e74d3634bbf83bdc10eae306d8260ad0c62a41cbf78929144bbb69371ac9218d18eee59fe8200c10173d380cd7843808eb16b2c4cdc56dc004ecdecafe59b4aeed084d2d73af0911a7d54474874689bd6f7716c16ee5f4f293f72ba7d26989e551adac0568cd345c5c948231d5e49bc26290115186b185b5ded", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "007df53f8d32a1ea1f5734b59456564cac461f1c3a949d4fedff5ea96928e7f2b4753520334760f14ede15eaa964f6a0e520346c4f6b3b401ba6e301b581dd49bd21", "result": "valid", "flags": []}, {"tcId": 175, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04011d2660b0f8fa5bbaaeb0e1d9e2a9642558969f13cd08c51c5725b19f18481af2be6edf0bc2ce59065bbed4b1f383f32f52fc559174f2a402275ee951adea00091d005a3426945e6cb706b8e42010a69a47c09948fec3f2c55f8da5cef5ecbf3f0ea3dc2cde92cbe2eeb76dc7fd33825435ee5457beb7a6069e3e348d26ed33e07dcdb8", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00725e7b5b8f0d7eb2d4fc6045d61b5b8505a156fef8fa4b1a2e351760b2f635a1837f23a4c6b724df076b2187b7e213da636b06250c66193a235a4e3c90ed86eb5b", "result": "valid", "flags": []}, {"tcId": 176, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04009c93065d67cd839187233fbfb33f808f7a6cd444924ee4ea0fd4bb9b3dbc8b4affa7d3c8e3ac2abf82449d7cf4166c6c181b4f609fe09e3ff7a2b6640f8163b08100b5f44a947b7d9bc78069d15d5049ac1ee9da21e78131523eba4d9c9ee6726d32bd4ad849dbd71270710cccb3cb88d243188fd04ac394c125dcb62735570e123890", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00718e900a7ef993d671ab0dc5417c98f61f6eb0731641e552ff48fa4f93318b8bfffaff2a1cfa4b759d351e56036a61dd1061d85c3d144b8a882f469810c58e0646", "result": "valid", "flags": []}, {"tcId": 177, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04003c1f1be461d3cf4302cf1fa8eb921e5c2806fe5673f27f6da3a5bc2b3d78a8aae7ba410236dd9e650942ccdb110423abf53c5d13167638ff8162b4b931a0bcbba900608d6517009d99fc3bf1165e8199221a7989f2ebda3b739a748cc938d2db3e697c5f75c32dd5bcc5578e549e429f454a039579ea1e796851e7578efd280858cc96", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0161c84c64cbd65e718abfab1f827af33064e7029ae6feeb11e057181b4b7ff3dbce4d856862b49abbc0b923164361728f6274d890603b7ce187d7ccfc3ff157b42a", "result": "valid", "flags": []}, {"tcId": 178, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04010b7b6fada1f602c9e97da4acd7ebfd2f9f8e30e0b30b77fb3954deaafaa2ae2628ba042b9257a0b611df770a7ad70da7b9a20bf4d6f80512cee74ba214c61c6d66002061f890cd81b9b49c91007a325ddb67c46062bb64266d3e72934249cb7262b8b92a737234f6e85b74f818b954abbc3529da116c0321f82e21dd25e53c073abbe1", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0172bdb24ac7ef741a94c15acc7e208d8d01183be1c45f5e510da1c0809102a9603fe30dcbb03325086aed94ba23422be9404287cdeae4a288afddc2aaa0e8cbc415", "result": "valid", "flags": []}, {"tcId": 179, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04011d28b1a95fbf27a4ad21ef4613e1983cce354c018aa757330fe13572f848abfa255898d1398f13ceb06b3b53292d000e87dd20dd1954763a94fdceb128fe63af1b0111ffc1b26866dcff9e42104a121a24ef5a15d78141cc93c26167398975916eafc8ad8baae13f59df23fe79aafb6e2078ba465c6b14e44c060eeaaa1ae6f4c7c979", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0124eacfd598b6fc20b9f01873ed3c55b0dbb3f9dbc18a05233e59a4ec7270d012b790840d22051c4f1c55a252e8adb10dae855599e22abcb3e23904aa0767e3cc84", "result": "valid", "flags": []}, {"tcId": 180, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040187b3111a718b18077d9789125ebdb6fdec77890875440d36692e30159b4cfbde1807b3ebb1b1a8c7db00c1fa66cf32c5f9ae0f5941e32d7c7d2af0fa98832f01ba008f7def2063a7797625236797fbeaf8d07d74e07d139e6d73583d2d450a7794a8f712ff7852aaa23da6ee8142d9b697568f7f4a63a87f97d6354fc469596406926c", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "012fdcff44a9af94f18b50f49c19a1e0e6f999964d5ca4327f280b2edbabf8235d2e2ba2e101b391d0dc1aa067d3593cafb3c5a99c053165fa28d677990ea886fc7a", "result": "valid", "flags": []}, {"tcId": 181, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04009a64ba8c6662bb51553f85608aa2e522bbffd1b44ef646de0938f6267990dd9d7f52505136b67620afc1f6d25acf6d1dc3972c3a88493ae3927230225c03a8135d008952c15dc94461873c232a2fbeb4a7a4687e641d10b3348d17a31ea3fbf17ced9065bfcddfd92ac742cee61d5bf582b958d253e939ce5cf92ce79615433d069d8d", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01cdf3f0c813badf7454de520d1248b298502194504afd5904080941cb85a371562848e4756011713593af46145d0652b2edce18d5c9e4a4cd55370bcd6083990628", "result": "valid", "flags": []}, {"tcId": 182, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0400393bd8fe3c49f7885efbcab87962ef12458c18f255f01a24a1ab795cbfe2b4ddf6e8ac253e4fd9484377f59ddc7c087996386ed9a165cca81f853c918be1c9d399007c47aca4aee1795d414c4334ce4bfecafabf3c3391a02e9332d7f17ec1b5c542009f980fbaf3eadcc192f400b9ad2400220c090a758784dba66de40600c626e4a1", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01d10825bb71d094ea2b01855583a8226871ebe7d6bcae064227bdd841788d1c489b94db20c39562450db84735f9285259b34c5d3b43a487f0708632b75de2ebe104", "result": "valid", "flags": []}, {"tcId": 183, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0401b4b19a125bc0cbd0606064a051af4ab17f791880475f00a1fbcd4a797b401bda390ef7826fa01682651d72cb1353704d7a18027c4d609334b2e8c5d4def86ea0e40190e4cd6a4e0ba9ea45d3b7144f74aaf1986462558c8b3a10501882c2669ec265efc152f5510f3e990d67218471cafc66c845cf69e5eff6e0308cc59ac59a407667", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "0031eb146616d7bc61ffb553b7ca8522a5b6088d23d5ca6e30b201b86da2375d1f6cc48e88a24b52b0e22045ca7c506c206930619c289fc4a688432adee4702880d3", "result": "valid", "flags": []}, {"tcId": 184, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0400e7fa4ac2365af9378dd2c81f32f9ef55d4d341ac324eb46923ec7f15448c37ef607c975c352754f472873e469f8d37d38d97d161a7b9b1dae2e965fdcc959747ba01e6c09639729499b607330d7daba2b5c1efae4f121e540bf7e691809a055542284cb5ee87475589cec663b53f21b91231a638d8402e358fd169953491ab3953122a", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "00a8f2dad749ea1e15673586bb67b5c85ff8f45c72ef8447efef3efc626a6a10ca88451643b434a25176cbae77899a3e32a4484781b700e03857bdd158ad3cfbf200", "result": "valid", "flags": []}, {"tcId": 185, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "040104f43ffbb37ce5d0ea956171c070d6147acfa9519b887995832a0eb801e0dbc66896dfaff94409b497f4a416ccd82750da47a520b870dfe6cd3764f0e3c0f1bc3b00a5f3db9939c21147ab4488c8128ba0564f192f2788560145d2b14a852816b4851ef69a462c9854bace720e4629ed55af9d2c7da74cd309fc5566e11630746a3522", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01ee85a3b6d35d0df6a445593c94609932530e25ee71909c5681ae398a38309e8699e94e0f23f56c64a0d04fc10e2896c50f213f12c685151fa40f18ac6d8ed85906", "result": "valid", "flags": []}, {"tcId": 186, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0401250ecad894d3fdffb580099c2c305bd5d903fedd2e532ea58d29d63c3281b39e6f47a11b3be97c1c04e042d3f19bd0cbe0ef4121a257279bbd76c66a05e8d222d001a2b3ce41ca09b5b6287b4c328a315dca243c726e8616629d2b37506b7eb31eb76f513decb0ab20a52c0c260a920e92ae1a629fb817df5ad6d7e62367ac5e8ee5f0", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "011e7d529dc9615209162401ab2bb717378109d87a1ac86b98b7954ea91b960b08fa9374ea72703519c39c69797240c977817e2b34b5322665dd518251bcc1fa970b", "result": "valid", "flags": []}, {"tcId": 187, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "040019f6ffd81d012b70804c308594b47066516bbb339b44abadb4da194399d1b4d5fc98dd9d0dc363a9bcb14396327ffadb39f9b357b7e631a84b23039396f1cc96f101a6a8c0af8e083a9f8c19cb0fbae7b4523c4c54fc9ffefb735113f528f7a3e9662571060861dd6c9c71b33d99b53d716c36a8a902bca64c46c7e34a2e88dbc96b6d", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "006b46bbda9f37961aec9efcd6fcbaf4e25b6674ea37f8d82ca2edcc06353e24d09ea5d86136fedee34e0c391b859072ab91918b51d0800ab20255f438c660882e91", "result": "valid", "flags": []}, {"tcId": 188, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "040077d897cff6a2a3446fb38528ad80cf3cc503ad93da06c7c5a525cf9a7bba33d79d686a0c53290a4594240540a02a85cf14336a23da56efdb562b0656800ff396a40120779ae0df7c1d928ca18d63fcabf0d21bd437c86e40966bae8c9fef2ac72738a75be49938ab9917034a2536c023695e821f70b458339bd8de218a5cf741e55a7d", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01109989b62a379397232c238cf57d81884babb39b041fbf0f0b48bb7d794944f9a302de0eccaa9a5625b222c6692d24077654f19836b8f14d8622ba14eeace274c9", "result": "valid", "flags": []}, {"tcId": 189, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "0401a02719244df807864707fbc6c5285da784301dabffb85853daf946f8d71598fa95f7c90a3d5f06e45051799c16f9af0afe275ff4f51e83e1bfabc18176f2bc4a740154de99c74dd263456034abdf5b2a173e318442d13200f4090aaa2714524c43a85ec5f607d5f355205fef0f4aa3b41b2ff79eca361fc28878fd4e26bcc57ac014d8", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01054b6191cb257e0f18d09cf454fca4c45716ea00f167b2fbd2d028b66c903b5e751a6caaccf4ed18e28635d8454ed3811e1c4b3638eb420ad21ea4fdbdbadae866", "result": "valid", "flags": []}, {"tcId": 190, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04018a82cb60812fd36cccb29e4747d20579ac9ac6b5ef6490326b30c5f077e9d7841ed189ad172140055d7ee6b015e02b1ebb9fc17d6acd8bb4c08d2ddb4541bb88f4007750fe2640e590f15426d64e2334cb2ad597340c445a42379fc194ef74d7e06a220aadb8e69e361b4960dde70f085e924319dea0df1d4c12c41519535702670ea8", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "007113f5f3c547a2bdee4af7625bf161ad9840e83f2c3b871b18be0c9db5686cafd3948c831a251a4649999193852650e6dc8a9f82cc696ad57c7181ac9a9ec220ec", "result": "valid", "flags": []}, {"tcId": 191, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "0400273b9bf3818c129cc272270b5bb0b8b7e557ca8c548a6f3205e37b611962b14d9be317dc44fe82b2d2504984db98a902e7ea2a5afe584dc2b2990e5a790fd55b1900b9211fbfa221e3c24bc3de2f70e8f0e84f4b05c2d2bc5e98da3e379e80becd53d221136ad740b2732ecbc3c7e06c07e064d5de69e6a6d567713f644e8ac25a1d93", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "019188478c62349299ba2f12aec3c7ef65858f50395380ed0e7e70e731d7c47885cfb183ecca22fc71f0afd0621243750786969a0097b36a521ef41df9f08f9a4558", "result": "valid", "flags": []}, {"tcId": 192, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "0400b9d403df5b1dacbef2baece88a0b10fabc5d729753f4e936015afb96b929392a9eeb03460968ed18868714caac2dc16c07245a9ef4832ac418e3290d1f8d0d102e008494f4442d00fdca67d31b3eb656c8a06bd521046c1af075b0ed26c26d5bdd8b800493c68cf493516027ede771aee3ed8b5fb947e9d600cc0a82c3f2076d96aac0", "private": "01781d86cac2c052b7e4f48cef415c5c1319e07db70db92a497c2ac764e9509ac0b07322801f5ae1f28c9d7db71f79e5f51bf646790af988d62339a6d1543192e327", "shared": "01adf724cf47ae319791a3dbe336b2f54a4d74bc28ff8a23c2f333e2a5a36bbe73dd6c9de72616ad5b779e51636d08f7cec58ff18b7127e396856bc13d39094410af", "result": "valid", "flags": []}, {"tcId": 193, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "03", "shared": "00f2246431b597930f2eae61e9aabbd39f8f6ae97c3cf2521a6aeecedda10b5ef5f3b2eb3a8906d02f51d244710aa9e19cc0be21db920132be1c91deb85e466c28df", "result": "valid", "flags": []}, {"tcId": 194, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "00347c51f587c726070bdeb9173d0a547427ead3f2c8de62d9ecc3013285f645d220931520bcef85d08cfb6786045745fbfbfb1924c44a89d06676131a965677272a", "result": "valid", "flags": []}, {"tcId": 195, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "0200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "shared": "01c41dc4437c2f2b94a940711b3a691723397a1f83d6bc0c67ddc7a657160925c7f85bb4eb3842b60b2610ddb7c0b8676267710e58359a8750843c6d8e25d48d1cd9", "result": "valid", "flags": []}, {"tcId": 196, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "006a239cdb7a783840658d5f314bfe5c51e806a4bf1236f8421265bcc503c673eb16c5c2b38b5717fa04ee7dbcdeb15c871711507abb7557a8a8c7b3250141e854d5", "result": "valid", "flags": []}, {"tcId": 197, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "shared": "0112dbf9713aadd478e4f2ebcb058f05b512b1959c7da1994f851f373ce8c341d39c6843373f6fe559905953e1147640159437953c571961c09bad157a8e1a5bf476", "result": "valid", "flags": []}, {"tcId": 198, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47adbb6fb71e91386409", "shared": "003eca2210c8623105085aa284d119f3d716730595c6291aa89bf32a95e8a5fdc64f3d76e92494a43a9dced12d05b6dca4ffe649b32ac12cb0202e702dc83a2cb277", "result": "valid", "flags": []}, {"tcId": 199, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb5fb71e91386409", "shared": "01c4cae9fbfdd45de51d8525e8447a7553c35cf358f1346f1d79666887bb749a3ba0de62e1866b47a447d53b6f1ca5a33ec94507e2cfb65544f5a1195fc6b4dc5810", "result": "valid", "flags": []}, {"tcId": 200, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb67b71e91386409", "shared": "008073b4796e748f3d0de5e85b22aed463f1a6aecdb336bc287b50d139e3591ef5f86b78c3f6051467755f059f295d758075347d657aaae02383838bb96071eacbd4", "result": "valid", "flags": []}, {"tcId": 201, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71d91386409", "shared": "01f11ff8983792d4a790d0de4b56d078b9033ad6318a440e8119342937cc48a39375150ab2cf98273b0fe35d5a3af5d84322a685e89f2cb378a99b9b7bac87e44952", "result": "valid", "flags": []}, {"tcId": 202, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138631b", "shared": "00286cefaaf38ca4c6657eb9b187d8614d51775fd71c1a79b4c0ef1a0d4ce72b6f5b2bc854a4e78283530942a3f4fd2a8586d5ea51513c89d3d29de5de06321e118e", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 203, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138639b", "shared": "014790de14c481f1336fcb7d33a8bf8e23eb594cc48608e9edfe0e326e106b67e7eaa3f04ec9985599178f632a5ee6419e11217060e9fcd5958a43882bf8cd3be6ba", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 204, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913863db", "shared": "01ae775dbc4096a3aea7977b1a0af4b2830ecf9ca927a6247fba4cccb46b3f71d0e7abb8dda72d1c1ee7bb5b875b4773cc8df40f732819c4147da330775d1742ea35", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 205, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913863fb", "shared": "01979fb05e068a12a3f20cfdfb9eaee9f22b356edcc7655383ed38124b86814f86a6f2216a34f3fc2299d403ee42408f95d08c5c6cd11db72cbf299a4a3c2545be25", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 206, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386403", "shared": "0197ebe26798bf67f06ff0282773af75115531f41d94c093d87481b76bef707bc222f2d6672f84a00fa20c5ed27027ab4006b68d93ee2151016c9ddbe014346272e2", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 207, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386406", "shared": "00f2246431b597930f2eae61e9aabbd39f8f6ae97c3cf2521a6aeecedda10b5ef5f3b2eb3a8906d02f51d244710aa9e19cc0be21db920132be1c91deb85e466c28df", "result": "valid", "flags": []}, {"tcId": 208, "comment": "edge case private key", "public": "0401ad5043591dbe81657fe3d1c3d7a516606ad9d320a35fce8aaec8a950fb53f95388f3fc48be998e99334ad9e9234cded14471fe86caccaa07d058ee8771733ac3b900854de36366590b9ee4d0370ea6b00f7ebd8156ccf14e99f1a5344a9b4964fbb8348b081a8840c6b64be77997ad8bebfea5e7d9f7a6a7fa6d7655c50b2b7835f314", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386407", "shared": "01c168314cdc85757ade34a52a9e5379ffa5968f084b7e404939a8033a0fc698e26211754b9b2c04cf8a1420abe6e986ef1a238bbb91dd402b72e0ed50a876f1a83e", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 209, "comment": "CVE-2017-10176: Issue with elliptic curve addition", "public": "0400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650", "private": "01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913863f7", "shared": "01bc33425e72a12779eacb2edcc5b63d1281f7e86dbc7bf99a7abd0cfe367de4666d6edbb8525bffe5222f0702c3096dec0884ce572f5a15c423fdf44d01dd99c61d", "result": "valid", "flags": ["CVE_2017_10176"]}, {"tcId": 210, "comment": "point is not on curve", "public": "04000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 211, "comment": "point is not on curve", "public": "04000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 212, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 213, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 214, "comment": "point is not on curve", "public": "04000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 215, "comment": "point is not on curve", "public": "04000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 216, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000101fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 217, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000101ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 218, "comment": "point is not on curve", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 219, "comment": "point is not on curve", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 220, "comment": "point is not on curve", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 221, "comment": "point is not on curve", "public": "0401fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe01ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 222, "comment": "point is not on curve", "public": "0401ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 223, "comment": "point is not on curve", "public": "0401ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 224, "comment": "point is not on curve", "public": "0401ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "point is not on curve", "public": "0401ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff01ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "", "public": "", "private": "01c6cafb74e2a50c82c7a63d13294bfea113f271e01ae305f79af43203cd32115ecdf2fee5fedba2ad3126783db0c3c4d3029a14369e8f80dbd15d512f13e51c503c", "shared": "", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "invalid public key", "public": "0200429cb431c18f5f4e4e502f74214e6ac5ec2c3f86b830bac24de95feae142ca7d9aa8aa5b34f55af4b2848f2e6ba6df4c3ecd401a1d7b2a8287a332b202196fadbb", "private": "01c1fb2cac9087a3397814b198a80e2ea5b437aac1b41e8a2bd8fef8700e4812aa817320e6e1e3865bd2cf75e43a78be5c27ff1c4b5f5019333cb37d0c9c4ff3ec61", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 228, "comment": "public key is a low order point on twist", "public": "020108cbf3c9bf8e42135d87127556831076d84d5e549e645afda8a099249231b59b6c508dee4e91c9a543e90ebc82613f86cb1290e29102a0f2fdeb57bf4193fb4639", "private": "6619644155c449758f65e2dfe7ba89dee1e090c1d68b6342f43cb1ac000090a7f0408138c1de217990bb015cd1d95f1d884cf659f7324f2fe21eeba63ea988aacd", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 229, "comment": "public key is a low order point on twist", "public": "03011f2dca6b686e2141c11822e2d5439261583ce98cd6c4041c6d1be9e17dee33ea4a65c3e8cca6de50a30a39c788a585f1188bef0680a9c0264b3c8dcf494d0eb948", "private": "00a257d97aa4e5195e2919c147c1639bb0da0cce479a036489006b7b8e7e885096066e5adc8fe7c45940c5a6b94d5065b966a45f099a0cecfe9cce1b3e99dca479f2", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 230, "comment": "public key is a low order point on twist", "public": "02011f2dca6b686e2141c11822e2d5439261583ce98cd6c4041c6d1be9e17dee33ea4a65c3e8cca6de50a30a39c788a585f1188bef0680a9c0264b3c8dcf494d0eb948", "private": "00a257d97aa4e5195e2919c147c1639bb0da0cce479a036489006b7b8e7e885096066e5adc8fe7c45940c5a6b94d5065b966a45f099a0cecfe9cce1b3e99dca479f3", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 231, "comment": "public key is a low order point on twist", "public": "030108cbf3c9bf8e42135d87127556831076d84d5e549e645afda8a099249231b59b6c508dee4e91c9a543e90ebc82613f86cb1290e29102a0f2fdeb57bf4193fb4639", "private": "6619644155c449758f65e2dfe7ba89dee1e090c1d68b6342f43cb1ac000090a7f0408138c1de217990bb015cd1d95f1d884cf659f7324f2fe21eeba63ea988aacc", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 232, "comment": "public key is a low order point on twist", "public": "020009cc73141cf1843d2b2c95dc5cbc4d615c6da4814c1c7208615d8e78c7a8666aba1852faaa45a45d32bd0fde6ea78f262a96bf1e02949cea48c33c695103683048", "private": "2a35258787f91ad0bd3432c3022e4d3ed349c8768a7e7caa1836022fc0c89a9073f6ce14d0990d5b7bb413061c7160e7bd566a5c89f14901b2cc19f1ad531f41e2", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 233, "comment": "public key is a low order point on twist", "public": "030047b9cf28e04b38796858545d60d6133fbdc20ede086e5d95111c982b8c276628235e536c075637a97c0a6c30d02b83b19e578203473eea16dfdeaeccb1dc0d9b19", "private": "01afe5c77a626161fb2c25964c7895b9fff787099db83f077f05a4bfa320fb61f9315bb44d3fb9dd72225d9d993a18df82ac53fb4a5f86b23cb650e5e4778066f677", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 234, "comment": "public key is a low order point on twist", "public": "0300c18410f5727ee0101a52ef95c0ac455cbc65bf9967f0a2c419aa0a291cabad569f2337e102d0a9128f4212dbf9fa9e5a8f14ca7f28e82977281facdd9ca7a92c78", "private": "24ae709e1644e3087b52470c565268becbdbf97de59916763507d109c2e5b7c21727c64e9b560aa248d7bc9fe0ac95720d507263b7b2859b056ea165301cd599d5", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 235, "comment": "public key is a low order point on twist", "public": "0200c18410f5727ee0101a52ef95c0ac455cbc65bf9967f0a2c419aa0a291cabad569f2337e102d0a9128f4212dbf9fa9e5a8f14ca7f28e82977281facdd9ca7a92c78", "private": "24ae709e1644e3087b52470c565268becbdbf97de59916763507d109c2e5b7c21727c64e9b560aa248d7bc9fe0ac95720d507263b7b2859b056ea165301cd599d6", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 236, "comment": "public key is a low order point on twist", "public": "020047b9cf28e04b38796858545d60d6133fbdc20ede086e5d95111c982b8c276628235e536c075637a97c0a6c30d02b83b19e578203473eea16dfdeaeccb1dc0d9b19", "private": "01afe5c77a626161fb2c25964c7895b9fff787099db83f077f05a4bfa320fb61f9315bb44d3fb9dd72225d9d993a18df82ac53fb4a5f86b23cb650e5e4778066f678", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 237, "comment": "public key is a low order point on twist", "public": "030009cc73141cf1843d2b2c95dc5cbc4d615c6da4814c1c7208615d8e78c7a8666aba1852faaa45a45d32bd0fde6ea78f262a96bf1e02949cea48c33c695103683048", "private": "2a35258787f91ad0bd3432c3022e4d3ed349c8768a7e7caa1836022fc0c89a9073f6ce14d0990d5b7bb413061c7160e7bd566a5c89f14901b2cc19f1ad531f41e1", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}]}]}