# RealityTap 核心库文档

欢迎使用 RealityTap 触觉反馈算法库！本目录包含了第三方开发者集成和使用 RealityTap 核心库所需的全部文档。

## 文档结构

### 📚 主要文档

#### [developer_guide.md](developer_guide.md) - 完整开发者指南
**这是唯一需要的文档**，包含了第三方开发者所需的全部内容：
- 快速开始指南
- 完整的 API 参考
- 详细的集成步骤
- 核心概念解释
- 自定义输出处理器开发
- 平台适配指南
- 性能优化建议
- 故障排除和调试
- 最佳实践
- 完整示例代码
- 版本信息和迁移指南

## 快速开始

> **💡 重要提示**：从 v1.0 开始，第三方开发者只需要包含一个头文件：
> ```cpp
> #include "realitytap/realitytap_interfaces.h"
> ```
> 这个文件包含了所有必要的接口定义、数据结构和 API 函数声明。

### 使用指南

**所有开发者**只需要阅读一个文档：

📖 **[developer_guide.md](developer_guide.md)** - 这是完整的开发者指南，包含：
- 新手入门：从"快速开始"章节开始
- API 参考：完整的函数列表和参数说明
- 深入学习：核心概念和最佳实践
- 问题解决：故障排除和调试指南

## 核心概念

### 触觉输出处理器 (IHapticOutputHandler)
RealityTap 使用输出处理器模式，您需要实现 `IHapticOutputHandler` 接口来处理生成的触觉信号。这种设计提供了：
- **硬件抽象**：统一的接口适配不同硬件
- **平台无关**：相同的代码可在不同平台使用
- **灵活性**：支持文件输出、网络传输、硬件驱动等多种用途

### 触觉数据格式
RealityTap 主要使用 HE (Haptic Event) 格式：
- **结构化数据**：包含时间、强度、持续时间等参数
- **高精度**：支持复杂的触觉效果描述
- **可扩展**：支持多种事件类型和参数

### 采样率选择
不同的采样率适用于不同场景：
- **6KHz**：基础应用，低功耗
- **8KHz**：通用选择，平衡性能和质量
- **12KHz**：中等精度应用
- **24KHz**：高精度专业应用

## 示例代码位置

### Android 平台
- **完整集成示例**：`demos/android/app/src/main/cpp/main_activity_jni.cpp`
- **简单输出处理器**：`demos/android/app/src/main/cpp/SimpleSubscriber.h`
- **硬件适配示例**：`demos/android/app/src/main/cpp/MockRichTapSubscriber.h`

### Windows 平台
- **完整集成示例**：`demos/windows/gui/realitytap_wrapper.cpp`
- **GUI 集成**：`demos/windows/gui/` 目录下的相关文件

## 常见使用场景

### 1. 移动应用集成
```cpp
#include "realitytap/realitytap_interfaces.h"

// 创建文件输出处理器
IHapticOutputHandler* handler = new SimpleSubscriber("haptic_output.bin");

// 配置参数
HapticActuatorParams params = {0, 170, "config.bin", SAMPLING_24KHZ, handler};

// 初始化并播放
awa_realitytap_init(&params, 1);
awa_realitytap_append_init();
awa_realitytap_append_start();
```

### 2. 游戏引擎集成
```cpp
#include "realitytap/realitytap_interfaces.h"

// 创建游戏专用处理器
class GameHapticHandler : public IHapticOutputHandler {
    // 实现游戏特定的触觉输出逻辑
};

IHapticOutputHandler* handler = new GameHapticHandler();
// 使用较低采样率以节省性能
HapticActuatorParams params = {0, 150, "game_config.bin", SAMPLING_8KHZ, handler};
```

### 3. 硬件驱动开发
```cpp
#include "realitytap/realitytap_interfaces.h"

// 创建硬件驱动处理器
class HardwareDriverHandler : public IHapticOutputHandler {
    void processWaveformSample(char sample) override {
        // 直接发送到硬件设备
        writeToHardware(sample);
    }
};
```

## 性能考虑

### 关键性能指标
- **延迟要求**：≤10ms 信号生成延迟
- **实时性**：`processWaveformSample()` 必须高效实现
- **内存使用**：避免频繁的动态内存分配

### 优化建议
1. **预分配缓冲区**：避免在 `processWaveformSample()` 中分配内存
2. **批量处理**：使用缓冲区批量处理采样点
3. **选择合适采样率**：根据应用需求选择最低可接受的采样率

## 平台支持

### 当前支持
- ✅ **Android** - 完整支持，包含 JNI 示例
- ✅ **Windows** - 完整支持，包含 GUI 示例

### 计划支持
- 🔄 **macOS** - 开发中
- 🔄 **Linux** - 开发中

## 技术支持

### 获取帮助
1. **查阅文档**：查看 `developer_guide.md` 完整开发者指南
2. **查看示例**：参考 `demos/` 目录下的示例代码
3. **联系团队**：如需进一步支持，请联系 RealityTap 开发团队

### 反馈问题
如果您在使用过程中遇到问题或有改进建议，请：
1. 检查 `developer_guide.md` 中是否有相关说明
2. 查看示例代码是否有类似实现
3. 联系开发团队并提供详细的问题描述

## 版本信息

- **当前版本**：1.0
- **最后更新**：2025年1月
- **兼容性**：C++17 及以上版本

### v1.0 重要特性
- **单一头文件**：第三方开发者只需包含 `realitytap_interfaces.h`
- **完整API访问**：所有公开函数和接口定义都在一个文件中
- **简化集成**：无需处理多个头文件依赖关系
- **跨平台支持**：统一的导入/导出宏定义

---

## 开始使用

🚀 **只需要一个文档**：[developer_guide.md](developer_guide.md)

🎯 **只需要一个头文件**：`#include "realitytap/realitytap_interfaces.h"`

**开始您的 RealityTap 开发之旅！**

**版权所有 © 2025 AWA Technology**
