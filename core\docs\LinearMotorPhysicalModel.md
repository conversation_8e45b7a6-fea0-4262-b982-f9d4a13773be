# LinearMotorPhysicalModel 正确定义与完整分析

## 概述

`LinearMotorPhysicalModel` 结构体是 RealityTap 振动电信号生成算法的核心数据结构，它是一个**混合数字孪生模型**，融合了硬件规格、实验测量、系统辨识、算法设计等多种来源的参数。

## 修正后的结构体定义

```cpp
/**
 * @struct LinearMotorPhysicalModel
 * @brief 线性马达混合数字孪生模型
 * @details 包含硬件规格、测量校准、系统辨识、算法控制、响应校正和感知调节等
 *          六类不同来源的参数，用于实现精确的触觉反馈控制
 */
#pragma pack(1)
struct LinearMotorPhysicalModel {
    
    // ========== 系统设计参数 ==========
    
    /**
     * @brief 驱动电压有效值 (Vrms)
     * @unit 伏特 [V]
     * @source 系统设计参数
     * @calculation 基于功耗、安全性、目标强度综合设计
     * @formula_option1 V_rms = √(P_target × R_coil)
     * @formula_option2 V_rms = (F_target × R_coil) / BL
     * @usage signal_intensity = vrms × √2 × multiplier
     * @lra0809_value 0.900000 V
     * @offset 0
     */
    double vrms;
    
    /**
     * @brief 实际频率
     * @unit 赫兹 [Hz]
     * @source 生产测试/质量检验
     * @measurement_method 阻抗扫描法或振幅响应法
     * @purpose 每个马达的实际谐振频率，补偿制造公差和材料差异
     * @usage 用于频率校准计算，补偿个体差异
     * @manufacturing_variation 由于制造公差，每个马达的实际频率与额定频率略有偏差
     * @lra0809_value 170.000000 Hz (此样品实测值恰好等于设计值)
     * @offset 8
     */
    double real_frequency;  // 生产后实际测量的频率
    
    /**
     * @brief 设计额定频率
     * @unit 赫兹 [Hz]
     * @source 设计规格书
     * @definition 振子运动一个完整正弦周期所需时间的倒数
     * @purpose 设计阶段确定的理论频率，用于电路设计和控制算法设计
     * @usage 作为设计基准和参考值
     * @design_target 电路设计、控制参数计算的目标频率
     * @lra0809_value 170.000000 Hz
     * @offset 16
     */
    double rated_frequency;  // 设计阶段确定的额定频率
    
    /**
     * @brief 理论谐振频率（修正值）
     * @unit 赫兹 [Hz]
     * @source 计算得出（包含修正因子）
     * @formula_basic f_n = √(k/m)/(2π)
     * @calculation_verification √(1535/0.00115)/(2π) ≈ 184.1 Hz
     * @actual_value 176.000000 Hz (包含阻尼修正或非线性因素)
     * @note 实际是有效谐振频率，但保持原有命名
     * @lra0809_value 176.000000 Hz
     * @offset 24
     */
    double resonant_frequency;
    
    /**
     * @brief 最大安全位移
     * @unit 米 [m]
     * @source 机械设计规格
     * @design_constraints 弹簧最大安全变形、磁路线性区间、机械间隙、材料疲劳限制
     * @purpose 硬件物理限制，防止机械损坏和确保线性工作区间
     * @usage 算法中用于振幅上限保护和安全检查
     * @mechanical_design 在线性马达设计阶段确定的固定值
     * @safety_consideration 提供足够的振动强度范围同时保证机械安全性
     * @dynamic_range 相对于标准工作振幅(~0.02mm)提供约25倍的动态范围
     * @lra0809_value 0.000520 m = 0.52 mm
     * @offset 32
     */
    double max_amplitude;  // 机械设计的最大安全位移，硬件物理限制
    
    /**
     * @brief 线圈电阻
     * @unit 欧姆 [Ω]
     * @source 规格书直接给出
     * @measurement 万用表直接测量验证
     * @physics 影响电流、功耗和电磁阻尼
     * @lra0809_value 8.270000 Ω
     * @offset 40
     */
    double coil_resistance;
    
    /**
     * @brief 等效阻尼系数（算法稳定性参数）
     * @unit 牛顿·秒/米 [N·s/m]
     * @source 算法设计参数
     * @purpose 防止除零错误，数值正则化
     * @characteristic 远小于物理阻尼值，确认为算法参数
     * @note 实际是算法稳定性因子，但保持原有命名
     * @lra0809_value 0.000068 N·s/m
     * @offset 48
     */
    double equivalent_damping_coeff;
    
    /**
     * @brief 电机常数（BL值）
     * @unit 牛顿/安培 [N/A]
     * @source 实验测量
     * @measurement_method1 静态力测试法：BL = F_measured / I_applied
     * @measurement_method2 反电动势法：BL = ε_back_emf / v_velocity
     * @physics 电流-力转换系数，磁通密度(B)与线圈有效长度(L)的乘积
     * @lra0809_value 0.266000 N/A
     * @offset 56
     */
    double BL;  // 保持原有行业标准命名
    
    /**
     * @brief 振子质量
     * @unit 千克 [kg]
     * @source 机械设计规格
     * @description 振子本体质量，包含振子、磁铁、部分弹簧质量等
     * @design_method 机械设计计算、CAD分析、称重测量
     * @usage 用于计算自然频率：ω_n = √(k/m)
     * @algorithm_application 在DispEqualiza.cpp中直接使用motor->mass
     * @lra0809_value 0.001150 kg
     * @offset 64
     */
    double mass;  // 线性马达设计时确定的振子质量
    
    /**
     * @brief 弹簧常数
     * @unit 牛顿/米 [N/m]
     * @source 机械设计规格
     * @description 弹簧设计时确定的刚度值，包含机械弹簧和磁弹簧效应
     * @design_method 弹簧设计计算、材料力学分析
     * @formula k = (G × d⁴) / (8 × D³ × n) (螺旋弹簧)
     * @usage 算法中使用校准后的值：k_calibrated = k × (f_real/f_theoretical)²
     * @algorithm_application 在DispEqualiza.cpp中进行频率校准后使用
     * @lra0809_value 1535.000000 N/m
     * @offset 72
     */
    double spring_constant;  // 线性马达设计时确定的弹簧常数
    
    /**
     * @brief 机械阻尼系数
     * @unit 牛顿·秒/米 [N·s/m]
     * @source 实验测量
     * @measurement_method1 自由衰减测试
     * @measurement_method2 Q值测试
     * @physics 纯机械阻尼，包括摩擦、空气阻力等
     * @note 实际是等效机械阻尼，但保持原有简洁命名
     * @lra0809_value 0.055000 N·s/m
     * @offset 80
     */
    double mechanical_damping;
    
    // ========== 保留字段和扩展数据 ==========

    /**
     * @brief 保留字段
     * @size 168字节
     * @purpose 内存对齐和未来扩展
     * @offset 88
     */
    char reserved[168];

    /**
     * @brief 频率相关振幅限制表
     * @size 512个双精度浮点数
     * @range 索引0-511对应频率50-561Hz (1Hz步进)
     * @source 大量实验测试数据
     * @measurement 在每个频率下测试最大安全振幅
     * @factors 考虑温升、机械应力、音频噪声、谐波失真
     * @formula A_limit(f) = min(A_thermal(f), A_mechanical(f), A_acoustic(f))
     * @usage 频率相关的振幅限制和安全保护
     * @algorithm_usage if (amplitude > frequency_amplitude_limits[freq_index]) amplitude *= limit_ratio
     * @offset 256
     */
    double frequency_amplitude_limits[512];

    /**
     * @brief 频率响应映射表
     * @size 256个双精度浮点数
     * @source 实验响应校正测试
     * @purpose 输入频率索引到输出频率的非线性映射
     * @usage output_freq = frequency_response_mapping[input_freq_index]
     * @application 支持频率校准和个性化调节
     * @algorithm_usage 在realitytap_common.cpp中用于频率查找表
     * @offset 4352
     */
    double frequency_response_mapping[256];
    
    // ========== 系统状态与配置参数 ==========

    /**
     * @brief 配置读取成功标志
     * @type 布尔值 (0=失败, 1=成功)
     * @usage 用于验证配置文件是否正确加载
     * @offset 6400
     */
    int32_t read_success;

    /**
     * @brief 频率下限阈值
     * @unit 赫兹 [Hz]
     * @source 算法计算得出
     * @calculation 固定设置为0
     * @usage 用于强度转换的频率分段判断
     * @algorithm_application 在convert_he_intensity_to_hed_intensity中判断低频处理
     * @offset 6404
     */
    int32_t frequency_lower_threshold;

    /**
     * @brief 频率上限阈值
     * @unit 赫兹 [Hz]
     * @source 算法计算得出
     * @calculation 基于frequency_response_mapping动态计算
     * @formula freq_upper_threshold = freq_idx + 150 (其中freq_idx通过映射表查找)
     * @usage 用于强度转换的频率分段判断和连续事件处理
     * @algorithm_application 在convert_he_intensity_to_hed_intensity中判断高频处理
     * @offset 6408
     */
    int32_t frequency_upper_threshold;

    /**
     * @brief 连续事件强度缩放因子
     * @unit 无量纲
     * @source 算法计算得出
     * @calculation 基于频率上限阈值的多项式计算
     * @formula continuous_scale = f²×0.040324 + f³×(-0.00009465) + f×(-5.749379) + 275.061239
     * @where f = frequency_upper_threshold
     * @usage 在高频连续振动事件的强度转换中使用
     * @psychophysics 用于连续振动的感知强度校正
     * @offset 6412
     */
    double continuous_event_intensity_scale;

    /**
     * @brief 瞬态事件强度缩放因子
     * @unit 无量纲
     * @source 算法固定值
     * @value 8.713339000000019 (固定常数)
     * @usage 在高频瞬态振动事件的强度转换中使用
     * @psychophysics 用于瞬态振动的感知强度校正
     * @algorithm_application 在convert_he_intensity_to_hed_intensity中作为分母使用
     * @offset 6420
     */
    double transient_event_intensity_scale;
};
#pragma pack()
```

## 参数来源分类总结

| 参数类别 | 字段名 | 来源方式 | 获取方法 | LRA0809值 | 实际含义说明 |
|---------|--------|----------|----------|-----------|----------|
| **硬件规格** | coil_resistance | 规格书 | 万用表测量 | 8.27 Ω | 线圈直流电阻 |
| | rated_frequency | 设计规格 | 设计计算 | 170 Hz | 设计额定频率 |
| | max_amplitude | 机械设计 | 设计计算/实验验证 | 0.52 mm | 最大安全位移 |
| | mass | 机械设计 | CAD分析/称重测量 | 0.00115 kg | 振子质量 |
| | spring_constant | 机械设计 | 弹簧设计计算 | 1535 N/m | 弹簧常数 |
| **生产测试** | real_frequency | 生产测试 | 阻抗/振幅扫描 | 170 Hz | 实际频率 |
| | BL | 生产测试 | 力测试/反电动势 | 0.266 N/A | 电机常数 |
| | mechanical_damping | 生产测试 | 衰减/Q值测试 | 0.055 N·s/m | 等效机械阻尼 |
| **算法设计** | vrms | 设计 | 系统需求计算 | 0.9 V | 系统驱动电压 |
| | equivalent_damping_coeff | 算法 | 数值稳定性 | 0.000068 N·s/m | 算法稳定性因子 |
| **计算得出** | theoretical_resonant_frequency | 计算 | 修正的谐振频率 | 176 Hz | 有效谐振频率 |
| | frequency_lower_threshold | 算法计算 | 固定设置 | 0 Hz | 频率下限阈值 |
| | frequency_upper_threshold | 算法计算 | 映射表动态计算 | 动态值 | 频率上限阈值 |
| | continuous_event_intensity_scale | 算法计算 | 多项式计算 | 动态值 | 连续事件强度因子 |
| | transient_event_intensity_scale | 算法固定 | 固定常数 | 8.713339 | 瞬态事件强度因子 |
| **实验数据表** | frequency_amplitude_limits | 实验测试 | 频率扫描测试 | 512个数值 | 频率振幅限制表 |
| | frequency_response_mapping | 实验测试 | 响应校正测试 | 256个数值 | 频率响应映射表 |

## 关键计算公式

### 1. 基础物理公式

```cpp
// 谐振频率计算
f_natural = sqrt(spring_constant / mass) / (2 * M_PI);
// 验证：√(1535/0.00115)/(2π) ≈ 184.1 Hz vs 实际176 Hz

// 电磁阻尼计算
c_electromagnetic = (BL * BL) / coil_resistance;
// 计算：(0.266)²/8.27 ≈ 0.0086 N·s/m

// 总阻尼计算
c_total = mechanical_damping + c_electromagnetic;
// 计算：0.055 + 0.0086 = 0.0636 N·s/m

// 阻尼比计算
damping_ratio = c_total / (2 * sqrt(spring_constant * mass));
// 计算：0.0636/(2√(1535×0.00115)) ≈ 0.0275
```

### 2. 算法校准公式

```cpp
// 频率校准的刚度调整（DispEqualiza.cpp中的关键计算）
// 目的：补偿每个马达的制造公差和个体差异
frequency_calibrated_stiffness = spring_constant *
    (real_frequency * real_frequency) /
    (theoretical_resonant_frequency * theoretical_resonant_frequency);

// 具体计算：1535 × (170/176)² ≈ 1432 N/m
// 说明：使用实际测量频率校准理论模型参数

// 频率校准的物理意义：
// - rated_frequency: 设计目标频率 (170 Hz)
// - real_frequency: 生产后实测频率 (170 Hz，此样品恰好等于设计值)
// - theoretical_resonant_frequency: 理论计算频率 (176 Hz)
// - 校准补偿了制造差异和模型误差

// 信号幅度计算
signal_intensity = vrms * sqrt(2) * multiplier;
// 最大安全位移：max_amplitude = 0.52 mm (硬件设计限制，算法中直接使用)
```

## 重要数据表详细说明

### frequency_amplitude_limits[512] - 频率振幅限制表

**数据结构**：
```
索引范围：0-511
频率范围：50-561Hz (1Hz步进)
数据类型：double (8字节浮点数)
总大小：4096字节
```

**物理意义**：
- 每个频率点的最大安全振幅限制
- 考虑多种物理约束的综合限制值
- 防止硬件损坏和性能劣化

**约束因素**：
```cpp
A_limit(f) = min(
    A_thermal(f),      // 热约束：防止过热
    A_mechanical(f),   // 机械约束：防止机械损坏
    A_acoustic(f),     // 声学约束：控制可听噪声
    A_harmonic(f)      // 谐波约束：减少谐波失真
)
```

**索引计算方法**：
```cpp
// 频率到索引的转换公式
int32_t freq_index = int32_t(frequency + 0.5) - 50;
// 例如：170Hz → 索引120，50Hz → 索引0，561Hz → 索引511
```

**算法应用场景**：
1. **信号生成过程中的实时限制**：
```cpp
// 在DispEqualiza.cpp的多个函数中使用
int32_t freq_response = int32_t(current_frequency + 0.5) - 50;
double amplitude_limit = motor->frequency_amplitude_limits[freq_response];
if (current_amplitude > amplitude_limit) {
    *signal_buffer *= amplitude_limit / current_amplitude;
}
```

2. **多阶段信号处理**：
   - 第一阶段：渐变上升阶段的振幅限制
   - 第二阶段：稳态阶段的振幅限制
   - 第三阶段：渐变下降阶段的振幅限制
   - 频率扫描：动态频率变化时的实时振幅调整

3. **保护机制**：
   - 防止在特定频率下产生过大振幅
   - 避免机械共振导致的损坏
   - 控制可听噪声和谐波失真

### frequency_response_mapping[256] - 频率响应映射表

**数据结构**：
```
索引范围：0-255
映射功能：输入频率索引 → 输出频率值
数据类型：double (8字节浮点数)
总大小：2048字节
```

**索引计算方法**：
```cpp
// 输入频率索引到输出频率的映射
// 索引范围：0-255，通常对应不同的频率区间或强度级别
int32_t output_frequency = (int32_t)frequency_response_mapping[input_index + 50];
```

**算法应用场景**：

1. **触觉事件频率映射**：
```cpp
// 在realitytap_common.cpp的convert_he_to_hed函数中
auto frequency_table = actuator->frequency_response_mapping;
point->frequency = (int32_t)frequency_table[point_freq + 50];
```

2. **频率阈值设置**：
```cpp
// 在DispEqualiza.cpp的init_equaliza函数中
double target_freq = motor->frequency_response_mapping[200];
// 循环查找与target_freq不同的频率值
while (freq_idx >= -200) {
    double curr_freq = motor->frequency_response_mapping[199 + freq_idx];
    if (fabs(target_freq - curr_freq) > 0.000001) break;
    --freq_idx;
}
```

3. **个性化频率校准**：
   - 补偿不同用户的感知差异
   - 适配不同设备的频率响应特性
   - 支持用户偏好设置和个性化调节

4. **非线性频率映射**：
   - 将线性输入频率映射到非线性输出频率
   - 实现更自然的触觉感知曲线
   - 优化特定频率范围的响应效果

### 两个数据表的协同工作

**工作流程**：
```
输入频率 → frequency_response_mapping → 校准后频率 → frequency_amplitude_limits → 限制后振幅
```

**具体过程**：
1. **频率映射阶段**：
   - 输入原始频率或频率索引
   - 通过frequency_response_mapping进行非线性映射
   - 得到校准后的目标频率

2. **振幅限制阶段**：
   - 使用校准后的频率计算索引：`freq_index = int(frequency + 0.5) - 50`
   - 从frequency_amplitude_limits获取该频率的振幅限制
   - 应用限制：`if (amplitude > limit) amplitude *= limit / original_amplitude`

**设计优势**：
- **分离关注点**：频率校准和振幅限制独立处理
- **灵活性**：可以独立调整频率映射和振幅限制策略
- **性能优化**：查表法实现O(1)时间复杂度的实时处理
- **个性化支持**：支持不同用户和设备的定制化配置

## 强度转换算法详解

### 频率阈值的计算和应用

**阈值计算过程**：
```cpp
// 在DispEqualiza::init_equaliza()中计算
// 1. 设置下限阈值
motor->frequency_lower_threshold = 0;

// 2. 计算上限阈值
int32_t freq_idx = -1;
double target_freq = motor->frequency_response_mapping[200];
while (freq_idx >= -200) {
    double curr_freq = motor->frequency_response_mapping[199 + freq_idx];
    if (fabs(target_freq - curr_freq) > 0.000001) break;
    --freq_idx;
}
motor->frequency_upper_threshold = (freq_idx == -201) ? -51 : freq_idx + 150;
```

**强度缩放因子计算**：
```cpp
// 连续事件强度缩放因子（基于多项式）
motor->continuous_event_intensity_scale =
    motor->frequency_upper_threshold * motor->frequency_upper_threshold * 0.040324 +
    pow(motor->frequency_upper_threshold, 3.0) * -0.00009465 +
    motor->frequency_upper_threshold * -5.749379 + 275.061239;

// 瞬态事件强度缩放因子（固定常数）
motor->transient_event_intensity_scale = 8.713339000000019;
```

### 强度转换的三种模式

**模式1：低频处理**
```cpp
// 条件：(连续事件 && frequency < frequency_lower_threshold) || (瞬态事件 && frequency < 0)
intensity_factor = frequency * 0.01381322 +
                   pow(frequency, 3.0) * 0.00000145 +
                   frequency * frequency * 0.00020902 +
                   0.9987492;
```

**模式2：高频处理**
```cpp
// 条件：(连续事件 && frequency > frequency_upper_threshold) || (瞬态事件 && frequency > 100)
frequency_component = frequency * frequency * 0.040324 +
                      pow(frequency, 3.0) * -0.00009465;

// 选择缩放因子
frequency_coefficient = (连续事件) ? continuous_event_intensity_scale : transient_event_intensity_scale;

intensity_factor = (frequency_component + frequency * -5.749379 + 275.061239) / frequency_coefficient;
```

**模式3：中频处理**
```cpp
// 条件：其他情况
intensity_factor = 1.0;  // 不进行频率相关的强度调整
```

### 最终强度计算

所有模式都使用相同的心理物理学公式：
```cpp
final_amplitude = intensity_factor * intensity *
                  pow(weber_ratio_constant + 1.0,
                      (intensity_factor * intensity - 100.0) / stevens_exponent_factor);
```

这个公式结合了：
- **Weber-Fechner定律**：感知强度与刺激强度的对数关系
- **Stevens幂定律**：感知强度与刺激强度的幂函数关系
- **频率相关校正**：不同频率下的感知差异补偿

## 频率参数的设计意图和应用

### 三个频率参数的关系
```
rated_frequency (170 Hz)        ← 设计阶段确定的额定频率
    ↓ 制造公差和材料差异
real_frequency (170 Hz)         ← 生产后实际测量频率
    ↓ 算法校准计算
theoretical_resonant_frequency (176 Hz)  ← 理论计算的谐振频率
```

### 频率校准的重要性
1. **制造差异补偿**：
   - 每个马达的 real_frequency 都可能与 rated_frequency 略有不同
   - 算法使用 real_frequency 进行个体校准
   - 确保每个马达都能获得一致的性能

2. **模型参数调整**：
   - 使用 real_frequency 校准 spring_constant
   - 公式：k_calibrated = k × (real_frequency / theoretical_resonant_frequency)²
   - 补偿理论模型与实际硬件的差异

3. **LRA0809样品分析**：
   - rated_frequency = real_frequency = 170 Hz (理想情况)
   - 说明此样品的制造精度很高
   - 但算法仍需要校准功能以适应其他样品的差异

## 物理一致性验证

### 1. 谐振频率验证
```
理论计算：f_theory = √(1535/0.00115)/(2π) ≈ 184.1 Hz
结构体值：resonant_frequency = 176.0 Hz
差异：(184.1-176.0)/184.1 ≈ 4.4%
```

**差异原因**：包含阻尼修正、非线性因素或实验校准结果

### 2. 阻尼系统验证
```
电磁阻尼：c_em = (0.266)²/8.27 ≈ 0.0086 N·s/m
机械阻尼：c_mech = 0.055 N·s/m
总阻尼：c_total = 0.055 + 0.0086 ≈ 0.064 N·s/m
阻尼比：ζ = 0.064/(2√(1535×0.00115)) ≈ 0.0275
Q值：Q = 1/(2ζ) ≈ 18.2
```

### 3. 振幅参数分析
```
最大安全位移：max_amplitude = 0.52 mm (机械设计限制)
算法应用：直接用作振幅上限保护和参考值
```

**分析说明**：
- max_amplitude 是机械设计确定的最大安全位移
- 算法中直接使用此值作为振幅参考，无需额外的理论计算
- 在DispEqualiza.cpp中多处直接使用 motor->max_amplitude
- 确保算法输出不会超出硬件的机械安全限制

## 参数获取建议流程

### 第一阶段：设计和规格参数
1. **设计规格参数**
   - coil_resistance = 8.27 Ω (规格书给出，万用表验证)
   - rated_frequency = 170 Hz (设计阶段确定的额定频率)

2. **机械设计参数**
   - max_amplitude = 0.52 mm (机械设计规格或设计验证)
   - mass = 0.00115 kg (CAD分析、称重测量)
   - spring_constant = 1535 N/m (弹簧设计计算)

### 第二阶段：生产测试和质量检验
3. **生产后测试参数**
   - real_frequency = 170 Hz (生产后实际测量，补偿制造公差)
   - BL = 0.266 N/A (力测试或反电动势测试)
   - mechanical_damping = 0.055 N·s/m (衰减测试或Q值测试)

4. **实验数据表获取**
   - frequency_amplitude_limits[512] (频率扫描测试，每个频率的振幅限制)
   - frequency_response_mapping[256] (响应校正测试，频率映射表)

### 第三阶段：算法校准
5. **参数校验和校准**
   - 验证设计参数的物理一致性：f_n = √(k/m)/(2π)
   - 计算校准后的刚度：k_calibrated = k × (f_real/f_theoretical)²
   - 验证阻尼系统：c_total = c_mechanical + BL²/R_coil

### 第四阶段：算法参数设计
6. **系统设计参数**
   - vrms = 0.9 V (基于功耗和安全性)
   - equivalent_damping_coeff = 0.000068 (数值稳定性)

7. **最终参数修正**
   - theoretical_resonant_frequency = 176 Hz (修正值)
   - 确保所有参数的物理一致性
   - 验证 real_frequency 与 rated_frequency 的偏差是否在合理范围内

## 算法中的实际应用

### 1. 均衡器参数计算
```cpp
// 在 DispEqualiza::calc_acc_equalizer_param() 中
double mass = motor->mass;                               // 0.00115 kg
double spring_constant = springConstant;                 // 校准后的刚度值
double damping_coeff = dampingCoefficient;              // 总阻尼系数

double damping_factor = damping_coeff / sqrt(spring_constant * mass) * 0.5;
double omega_n = sqrt(spring_constant / mass);
```

### 2. 频率校准应用
```cpp
// 在 DispEqualiza::init_equaliza() 中
double frequency_calibrated_stiffness = motor->spring_constant *
    (motor->real_frequency * motor->real_frequency /
     (motor->resonant_frequency * motor->resonant_frequency));
springConstant = frequency_calibrated_stiffness;

double damping_coeff = motor->mechanical_damping +
    motor->BL * motor->BL / motor->coil_resistance;
dampingCoefficient = damping_coeff;
```

## 快速参考表

### 参数速查
| 字段名 | 值 | 单位 | 来源 | 物理意义 |
|--------|----|----|------|----------|
| `vrms` | 0.900000 | V | 设计 | 系统驱动电压 |
| `real_frequency` | 170.000000 | Hz | 生产测试 | 实际工作频率 |
| `BL` | 0.266000 | N/A | 生产测试 | 电机常数 |
| `mass` | 0.001150 | kg | 机械设计 | 振子质量 |
| `spring_constant` | 1535.000000 | N/m | 机械设计 | 弹簧常数 |
| `mechanical_damping` | 0.055000 | N·s/m | 生产测试 | 机械阻尼 |
| `max_amplitude` | 0.000520 | m | 机械设计 | 最大安全位移 |

### 常见问题
**Q: max_amplitude在算法中如何使用？**
A: max_amplitude直接用作振幅上限保护和参考值。在DispEqualiza.cpp中，算法根据频率范围判断使用max_amplitude或计算动态振幅缩放因子，确保输出不超出机械安全限制。

**Q: equivalent_damping_coeff为什么这么小？**
A: 这是算法数值稳定性参数，不是物理阻尼值。

**Q: theoretical_resonant_frequency为什么与计算值不同？**
A: 可能包含阻尼修正、非线性因素或实验校准结果。



**Q: rated_frequency和real_frequency有什么区别？**
A: rated_frequency是设计阶段确定的额定频率，表示理论目标值；real_frequency是生产后实际测量的频率，用于补偿制造公差。算法使用real_frequency进行个体校准，确保每个马达都能获得一致的性能。

**Q: 为什么需要频率校准？**
A: 由于制造公差和材料差异，每个马达的实际频率与设计频率可能略有偏差。频率校准使用real_frequency调整模型参数，补偿这些个体差异，确保算法在所有马达上都能正常工作。

**Q: 参数的典型容差范围是多少？**
A:
- real_frequency: ±5% (制造公差)
- BL: ±10% (磁路和线圈差异)
- mechanical_damping: ±15% (材料和装配差异)
- coil_resistance: ±5% (线圈绕制精度)

**Q: 如何验证参数的有效性？**
A:
- 物理一致性检查：f_n = √(k/m)/(2π) 应接近实测频率
- 阻尼合理性：总阻尼 = 机械阻尼 + 电磁阻尼
- 数值范围检查：各参数应在合理的物理范围内
- 交叉验证：多种测量方法的结果应一致

**Q: frequency_amplitude_limits数组如何获取和使用？**
A:
- 获取：通过频率扫描测试，在50-561Hz范围内每1Hz测试一个振幅限制值
- 索引：freq_index = int(frequency + 0.5) - 50
- 使用：实时检查当前振幅是否超过限制，超过则按比例缩放
- 作用：防止特定频率下的过驱动，保护硬件和控制噪声

**Q: frequency_response_mapping的256个值代表什么？**
A:
- 这是输入频率索引到输出频率的非线性映射表
- 支持个性化频率校准和用户偏好设置
- 在触觉事件转换中用于频率映射：output_freq = mapping[input_index + 50]
- 在均衡器初始化中用于设置频率阈值和工作范围

**Q: 两个数据表如何协同工作？**
A:
- frequency_response_mapping先进行频率校准映射
- 然后使用校准后的频率在frequency_amplitude_limits中查找振幅限制
- 这种设计分离了频率校准和振幅限制的关注点，提高了灵活性

**Q: 强度转换算法的三种模式是什么？**
A:
- 低频模式：frequency < frequency_lower_threshold，使用三次多项式校正
- 高频模式：frequency > frequency_upper_threshold，使用缩放因子校正
- 中频模式：其他情况，intensity_factor = 1.0，不进行频率校正

**Q: frequency_upper_threshold是如何计算的？**
A:
- 从frequency_response_mapping[200]开始向前查找
- 找到与目标频率不同的第一个值的索引
- 计算公式：frequency_upper_threshold = freq_idx + 150
- 如果未找到则设为-51

**Q: continuous_event_intensity_scale的计算公式是什么？**
A:
- 基于frequency_upper_threshold的三次多项式
- 公式：f²×0.040324 + f³×(-0.00009465) + f×(-5.749379) + 275.061239
- 用于连续振动事件的感知强度校正

**Q: 为什么需要不同的强度转换模式？**
A:
- 人耳对不同频率的敏感度不同（等响度曲线）
- 触觉感知也有类似的频率依赖性
- 低频、中频、高频需要不同的感知补偿策略
- 连续事件和瞬态事件的感知机制不同

## 结论

修正后的 `LinearMotorPhysicalModel` 结构体定义更准确地反映了各参数的真实含义和来源：

1. **参数分类准确**：
   - **硬件规格参数**：coil_resistance, rated_frequency, max_amplitude, mass, spring_constant
   - **生产测试参数**：real_frequency, BL, mechanical_damping
   - **算法设计参数**：vrms, equivalent_damping_coeff
   - **实验数据表**：frequency_amplitude_limits, frequency_response_mapping

2. **重要数据表的完整说明**：
   - frequency_amplitude_limits[512]：频率相关的振幅限制表
   - frequency_response_mapping[256]：频率响应映射表
   - 详细说明了数据结构、物理意义和算法应用

3. **max_amplitude的正确理解**：
   - 这是机械设计的最大安全位移，不是算法计算值
   - 在线性马达设计阶段就确定的固定值
   - 用于算法中的振幅上限保护和安全检查

4. **设计参数与算法校准的关系**：
   - mass和spring_constant是设计时确定的固定值
   - 算法使用校准后的spring_constant：k_calibrated = k × (f_real/f_theoretical)²
   - 这种设计既保持了物理参数的准确性，又实现了算法的自适应性

5. **频率参数的设计意图**：
   - 明确区分rated_frequency（设计值）和real_frequency（实测值）
   - 解释频率校准的重要性和实现原理

6. **物理一致性和质量控制**：
   - 提供了完整的验证公式和计算方法
   - 包含参数容差范围和有效性验证方法

7. **实用性强**：包含参数获取流程、算法应用示例和常见问题解答

这种设计体现了现代触觉算法的复杂性：既要保持物理准确性，又要确保硬件安全，同时适应算法优化需求，是硬件物理特性与软件算法优化的完美结合。
