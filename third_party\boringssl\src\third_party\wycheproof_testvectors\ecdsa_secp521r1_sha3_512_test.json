{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 449, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04005c6457ec088d532f482093965ae53ccd07e556ed59e2af945cd8c7a95c1c644f8a56a8a8a3cd77392ddd861e8a924dac99c69069093bd52a52fa6c56004a074508007878d6d42e4b4dd1e9c0696cb3e19f63033c3db4e60d473259b3ebe079aaf0a986ee6177f8217a78c68b813f7e149a4e56fd9562c07fed3d895942d7d101cb83f6", "wx": "5c6457ec088d532f482093965ae53ccd07e556ed59e2af945cd8c7a95c1c644f8a56a8a8a3cd77392ddd861e8a924dac99c69069093bd52a52fa6c56004a074508", "wy": "7878d6d42e4b4dd1e9c0696cb3e19f63033c3db4e60d473259b3ebe079aaf0a986ee6177f8217a78c68b813f7e149a4e56fd9562c07fed3d895942d7d101cb83f6"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004005c6457ec088d532f482093965ae53ccd07e556ed59e2af945cd8c7a95c1c644f8a56a8a8a3cd77392ddd861e8a924dac99c69069093bd52a52fa6c56004a074508007878d6d42e4b4dd1e9c0696cb3e19f63033c3db4e60d473259b3ebe079aaf0a986ee6177f8217a78c68b813f7e149a4e56fd9562c07fed3d895942d7d101cb83f6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAXGRX7AiNUy9IIJOWWuU8zQflVu1Z\n4q+UXNjHqVwcZE+KVqioo813OS3dhh6Kkk2smcaQaQk71SpS+mxWAEoHRQgAeHjW\n1C5LTdHpwGlss+GfYwM8PbTmDUcyWbPr4Hmq8KmG7mF3+CF6eMaLgT9+FJpOVv2V\nYsB/7T2JWULX0QHLg/Y=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024201abcd9bbc11d77ae8aacb4dc113aa0d5a53ee51b5e4b189befeed4649f35c97fe595e3ee86ba4c3358e80dd91c4e7db45cfd0fa027f18458c30602d7038515558b8", "result": "valid", "flags": []}, {"tcId": 2, "comment": "valid", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "valid", "flags": []}, {"tcId": 3, "comment": "length of sequence contains leading 0", "msg": "************", "sig": "30820087024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 4, "comment": "wrong length of sequence", "msg": "************", "sig": "3088024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "************", "sig": "3086024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "uint32 overflow in length of sequence", "msg": "************", "sig": "30850100000087024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint64 overflow in length of sequence", "msg": "************", "sig": "3089010000000000000087024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "length of sequence = 2**31 - 1", "msg": "************", "sig": "30847fffffff024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**32 - 1", "msg": "************", "sig": "3084ffffffff024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**40 - 1", "msg": "************", "sig": "3085ffffffffff024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**64 - 1", "msg": "************", "sig": "3088ffffffffffffffff024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "incorrect length of sequence", "msg": "************", "sig": "30ff024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "indefinite length without termination", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "************", "sig": "308187028001ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2028054326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "removing sequence", "msg": "************", "sig": "", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "lonely sequence tag", "msg": "************", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "appending 0's to sequence", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "prepending 0's to sequence", "msg": "************", "sig": "3081890000024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending unused 0's to sequence", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending null value to sequence", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510500", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "including garbage", "msg": "************", "sig": "30818d498177308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "************", "sig": "30818c2500308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "************", "sig": "30818a308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510004deadbeef", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "************", "sig": "30818c2247498177024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "************", "sig": "30818b22462500024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "************", "sig": "30818f2244024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20004deadbeef024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "************", "sig": "30818c024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b22246498177024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "************", "sig": "30818b024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b222452500024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "************", "sig": "30818f024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b22243024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510004deadbeef", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including undefined tags", "msg": "************", "sig": "308190aa00bb00cd00308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "************", "sig": "30818eaa02aabb308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "************", "sig": "30818f224aaa00bb00cd00024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "************", "sig": "30818d2248aa02aabb024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "************", "sig": "30818f024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b22249aa00bb00cd00024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "************", "sig": "30818d024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b22247aa02aabb024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "truncated length of sequence", "msg": "************", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "using composition with indefinite length", "msg": "************", "sig": "3080308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "************", "sig": "30818b2280024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20000024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "************", "sig": "30818b024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b22280024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with wrong tag", "msg": "************", "sig": "3080318187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "************", "sig": "30818b2280034201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20000024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "************", "sig": "30818b024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b22280034154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "Replacing sequence with NULL", "msg": "************", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "changing tag value of sequence", "msg": "************", "sig": "2e8187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "************", "sig": "2f8187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "************", "sig": "318187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "************", "sig": "328187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "************", "sig": "ff8187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "dropping value of sequence", "msg": "************", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "using composition for sequence", "msg": "************", "sig": "30818c3001023081864201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "truncated sequence", "msg": "************", "sig": "308186024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "************", "sig": "3081864201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "indefinite length", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": ["BER"]}, {"tcId": 55, "comment": "indefinite length with truncated delimiter", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b5100", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length with additional element", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b5105000000", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with truncated element", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51060811220000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with garbage", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000fe02beef", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with nonempty EOC", "msg": "************", "sig": "3080024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510002beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "prepend empty sequence", "msg": "************", "sig": "3081893000024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "append empty sequence", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b513000", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append garbage with high tag number", "msg": "************", "sig": "30818a024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51bf7f00", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "sequence of sequence", "msg": "************", "sig": "30818a308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "truncated sequence: removed last 1 elements", "msg": "************", "sig": "3044024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "repeating element in sequence", "msg": "************", "sig": "3081ca024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "long form encoding of length of integer", "msg": "************", "sig": "30818802814201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b202814154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 68, "comment": "length of integer contains leading 0", "msg": "************", "sig": "3081890282004201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20282004154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "wrong length of integer", "msg": "************", "sig": "308187024301ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 71, "comment": "wrong length of integer", "msg": "************", "sig": "308187024101ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024254326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024054326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "uint32 overflow in length of integer", "msg": "************", "sig": "30818c0285010000004201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "************", "sig": "30818c024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20285010000004154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint64 overflow in length of integer", "msg": "************", "sig": "308190028901000000000000004201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "************", "sig": "308190024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2028901000000000000004154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "length of integer = 2**31 - 1", "msg": "************", "sig": "30818b02847fffffff01ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "************", "sig": "30818b024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b202847fffffff54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**32 - 1", "msg": "************", "sig": "30818b0284ffffffff01ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "************", "sig": "30818b024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20284ffffffff54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**40 - 1", "msg": "************", "sig": "30818c0285ffffffffff01ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "************", "sig": "30818c024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20285ffffffffff54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**64 - 1", "msg": "************", "sig": "30818f0288ffffffffffffffff01ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "************", "sig": "30818f024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20288ffffffffffffffff54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "incorrect length of integer", "msg": "************", "sig": "30818702ff01ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b202ff54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "removing integer", "msg": "************", "sig": "3043024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "lonely integer tag", "msg": "************", "sig": "304402024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "************", "sig": "3045024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b202", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "appending 0's to integer", "msg": "************", "sig": "308189024401ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20000024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024354326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510000", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "prepending 0's to integer", "msg": "************", "sig": "3081890244000001ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20243000054326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": ["BER"]}, {"tcId": 95, "comment": "appending unused 0's to integer", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20000024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "appending null value to integer", "msg": "************", "sig": "308189024401ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20500024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "************", "sig": "308189024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024354326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b510500", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "truncated length of integer", "msg": "************", "sig": "30450281024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "************", "sig": "3046024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20281", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "Replacing integer with NULL", "msg": "************", "sig": "30450500024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "************", "sig": "3046024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20500", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "changing tag value of integer", "msg": "************", "sig": "308187004201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "************", "sig": "308187014201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "************", "sig": "308187034201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "************", "sig": "308187044201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "************", "sig": "308187ff4201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2004154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2014154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2034154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2044154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2ff4154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "dropping value of integer", "msg": "************", "sig": "30450200024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "************", "sig": "3046024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20200", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "using composition for integer", "msg": "************", "sig": "30818b22460201010241ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "************", "sig": "30818b024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b222450201540240326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "modify first byte of integer", "msg": "************", "sig": "308187024203ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024156326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify last byte of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a632024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30bd1", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "truncated integer", "msg": "************", "sig": "308186024101ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "************", "sig": "3081860241ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "************", "sig": "308186024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024054326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "************", "sig": "308186024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20240326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "leading ff in integer", "msg": "************", "sig": "3081880243ff01ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20242ff54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "replaced integer by infinity", "msg": "************", "sig": "3046090180024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "************", "sig": "3047024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2090180", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replacing integer with zero", "msg": "************", "sig": "3046020100024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "************", "sig": "3047024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2020100", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308187024203ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ecdf778ea1da4001729f30bea5e3cf64b9f4421887e4aa3c3b8ae86129c45cf0abb024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3081860241ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed9546bdb1625a0ea52f373e7cc4ee2fffeccb5f50d376b345b37a6a45f235e42a9024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3081870242fe31e7e8fee8be05ded42c3f10ff14cfc83fc776dca7bf8032c1dd98403fd5c8912c5a0d9d661b2f7f418cc016eaba135a30f794413a3ef883f60ce9a4824b69594e024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308186024131e7e8fee8be05ded42c3f10ff14cfc83fc776dca7bf8032c1dd98403fd5c89126ab9424e9da5f15ad0c8c1833b11d0001334a0af2c894cba4c8595ba0dca1bd57024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3081870242fc31e7e8fee8be05ded42c3f10ff14cfc83fc776dca7bf8032c1dd98403fd5c89132088715e25bffe8d60cf415a1c309b460bbde7781b55c3c475179ed63ba30f545024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308186024131e7e8fee8be05ded42c3f10ff14cfc83fc776dca7bf8032c1dd98403fd5c8912c5a0d9d661b2f7f418cc016eaba135a30f794413a3ef883f60ce9a4824b69594e024154326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b202420254326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca368019b44ce269bd99bf7487eba70cd063805d0a67190f1faf3032d16b1fe04d11b6f5a", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20242fe54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a6a1c117945b3cca717f226e3b1824ba302f05fd80e7ba73cf9fd28fc7aeaaa748", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308187024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20241abcd9bbc11d77ae8aacb4dc113aa0d5a53ee51b5e4b189befeed4649f35c97fe5f0cb860e7e5939f230111907bf0d19fff954438c68ea94481a4bdb919c01cf4af", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20242fdabcd9bbc11d77ae8aacb4dc113aa0d5a53ee51b5e4b189befeed4649f35c97fe64bb31d964266408b781458f32f9c7fa2f598e6f0e050cfcd2e94e01fb2ee490a6", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b202420254326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b20242fe54326443ee2885175534b23eec55f2a5ac11ae4a1b4e76410112b9b60ca36801a0f3479f181a6c60dcfeee6f840f2e60006abbc7397156bb7e5b4246e63fe30b51", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "308188024201ce1817011741fa212bd3c0ef00eb3037c038892358407fcd3e2267bfc02a376ed3a5f26299e4d080be733fe91545eca5cf086bbec5c1077c09f3165b7db496a6b2024201abcd9bbc11d77ae8aacb4dc113aa0d5a53ee51b5e4b189befeed4649f35c97fe5f0cb860e7e5939f230111907bf0d19fff954438c68ea94481a4bdb919c01cf4af", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020100024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020100024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020100024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020100024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201000242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020101024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020101024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020101024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047020101024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201010242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201ff024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201ff024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201ff024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201ff024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470201ff0242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913864090201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913864090242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3049024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913864080201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e913864080242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3049024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a0242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3049024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "308188024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3049024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3047024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "304702420200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3081880242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3081880242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3081880242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3081880242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024201ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30818802420200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30490242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30470242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3132313930", "sig": "308188024200b4b10646a668c385e1c4da613eb6592c0976fc4df843fc446f20673be5ac18c7d8608a943f019d96216254b09de5f20f3159402ced88ef805a4154f780e093e0440242013aed2bb1d92ef16a821bf47203a3a7df2e9d3efdb040f7d6c7b36bac07bf2c1fa1c44ce630e40f38ef1b838b5252cd41d03f974ff2eb6e731cc52a96d789ee1dce", "result": "valid", "flags": []}, {"tcId": 230, "comment": "special case hash", "msg": "32373239373236343137", "sig": "308187024161a4212b4a97c71fe13d44d85881cdb999566cb3e0ab3b8dbe19eb493e3ddc93ca482dbf2a2be8b2593405840e8d18b32bd29e6c3227758632abad768f08cece000242014e853cf725c53676c4a807e389036302ab4d9a37a3a565d65cc44e51fc10ef8a9358fdcd02f193dd8b6053d1e5a436d79ef89ad7764270da133222f0ccddec0a4e", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "343331343737363137", "sig": "30818802420108a2d18a9052bf94b3ad92c0d1dd4e044793d154562394de5d2b87abb96553e27c13551c1c02c96b55654c61067dd38c646a5a22edb74a8b2a9918061f50d046d50242010d71d4946272199678f331ad5a1d6be422b5289e05b313a1312380f2b633d03f2f871ec3800c531a95b2c8aed55a18031d229c74a4bf673be46dab46f7db6ea322", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "36363033343338303333", "sig": "308188024201d1850d561d65708444e3ecd468d8857cbebac709b128f358257959ea1df83cf0b6511532247ccf4342dd0e45c0e9c41ab0ff00ace007722c223ee973cc60e2d54d02420180e7674a0f0120d9922ac3dfe23661fb69eb0b328b307a0b6245ac881c8a198db56f8f6ad0d63c0fa4681cce10ad4421675afed97ea57bb7837f716d9af939904a", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "32383239363231343535", "sig": "308188024201b46717f9a79cfca7966a09a027288efc065709d7ff2e3e330ea79988ceb766648cdbc824642d95a4bde04d85cd7fbff7150a60369a66e8ca38056dd0a31d7a30740242018035d894c0a0f9fe1db8afcdcca4f0b3fc8f36117708f0176b805d276867e339807e408808817d91f99d9c29880c4c162ae8b67d8bdc24d2c5acc3d29e4ed0967e", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "34333131383231373336", "sig": "3081880242016121c9e9c79f6bff0cfcd8dcd22f9be3d87729d9015123b0f497cddb98772790744081bc692f2e99f27ee10bc56239b2783d754b5020999f31a70c76129f73092402420143ff0fa6411ed447156cd44e1746d390a59373943c01fbd9457c23df7701022e69b06804165c16ebb9a536d5c28c00e98f7b1cd4ff294f878bc00c034d9f382515", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "3131373730373734313735", "sig": "308187024200e2d1f70f08770c220fdca23d2301850a332ca31d57edbc65231f4920e9b6e6fbad37a3266e0a01032906d064163e8c549038e9d5e52d32cea30f3e22e4f1c49ae1024145216ed90546b8a99ab98be980bf9a1f7f7fd7bd4112f0561a85cf0c13933efbdccb7528f217babec7eb702bb26570feee0885306b3f8c3f86c9665a90a4dabdd6", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "31353938353135353635", "sig": "308187024200dbc9fc7d1b18ae9d5c42c4043fb468660a0bb74e0fd40a8279d0f5b4a37377eed84b07fc1e9b6d5db1ac1c88cb25eb23e3188754e4c7e38aa5f153076e6b1b8e1002413424f4b74d7b19e910fd9e6641e8172c03f759aceba300af49f9f7ffa98abd658d512d79f843d1f4421e46532de99496d11640ad1ce376ef31455c8bf4417fe8f0", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "32383831313031363138", "sig": "30818802420134fe8e5e3625aafc2db789eec8eb1591ac7f5e400664a04aa5e9bb643c795c1506931a8b976dd00c4ec86855db40e24c45c724c57b54494e4fa4261ff77e16e211024200ff3280b9409c4cbe6ed29ed9bd1221fff69da388531f62952ab05e97cb7b8af69a32bd31043efae743cd00382dcdc13626f0b2c6cef1041ac550a6c1f8d93c27bc", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32303034373833333332", "sig": "3081880242017c711b844e59b2699d69c1dc27a3ed505fe686a0e6ff5c1ee674e36964902def9f92f453d10c5b185242ac98c1f65c0433fdc96de39943b31be16314fc9c73db9202420170767fd3b8a2bd2d8cb9c8c1a69073a437e3bd07061ef930e47bef09e4033f467e220cedbc7fbfe193c48dee3fd15f51686fb237d8b00874cbeb05c237c5880b97", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "39353030323437373837", "sig": "308188024200bf986f02e077a60e9be04a9d7700e3d4ed4b04a9a9d1bef5f04c7eee01c320ccd2fabee38d44d71fa65b4ae15330f989adcab23757a70632f54ce12c37ccb195cf024200869f9e343033ffbc0f1f05f90fbdf14b7ba525ffaab1d69cf4ca219f6f191db230b1c223125832894b546222cd22ea7b4bf7ec0fa36f1dc2ce51207f1bf38be51d", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "32323039353030303630", "sig": "3081860242014c2f8a18655c6d4e6a54e1c51c9d61edce24ca37e38459621549e653c3fa9b9a0120cad50295d6f885427c2a18b604e6f6c57f7a9cecabea68b9eebc49aaeb623a0240436e05bab531350aa441e6e7a1042ea4219de3db75752dc263bb14592b5fdb15827b2a4c2f6ae94aedc89eb0e664989fcd573bcc3014274cbec6791982b8f539", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "38313933373839323237", "sig": "30818702410c59126e6eb5932c6a05ee36738100c24141bde74e3675e112d1a9c0134add3e78b3697ea48fc26394761503dfc1cbd444dafd8d0831b4f517b329e37600898c800242017c611d7bce811524911c2741f2ead675b9cb7ccc7738fdb7aa47ab01d4fb6690e938a179159b3a2f83393d006c04f0948ceaef9797f6630ee0df7a35559a34b16b", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "33363530363033323938", "sig": "30818602417cc7fe18cca28a8c231bd3c7aa2936d02e3906a49dcdc2a30aac9926d8c89cf25bc644f584ab6ef026dc551af6ba32ceb8e3ce2af46178c4de8c7add701314e774024134ede164d5d60a69ca905f0c83314a63700024dbefc51ce1cf2bc69447cad76918580d45b93447093f5429170c996f2f7818ba0c51ce96b7526ea969d7c060d225", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "3136333833393533353233", "sig": "30818802420154a680570056cfcae672d5af7e19556298b9ddd3fa162dc2066ceb8910008f304bcfec4faa917c7c8060f2f5c46fcf9b136ee1ea845b93f1e71613c45f22f9d0b3024200ecdd6293480007e51bb4ab2eb863eed6742e693d160418522c182aa02851593b02a818eb757200f0cfcc1045fd1239c648b9968f2d38c3b98f42f9d21fe8943ae2", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "32303931373638323035", "sig": "3081870241593c8392ac4e7e1b966ae851ccc07940a04c5cd09dcc977b6223a936e849297dcfb01e65b2ee850b8fbaf9e62fd958c6a73a767020d2207cc405f795e77c808078024200ce6358773088e069648e44976b0f9633e0d7a3fc4f21f568f196a5f4d0ae193b2ace16d765ac8396ec631acb5bdda0ca5fe10dcc6c2e685021e7442b45baa6d343", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "39333634373032383235", "sig": "30818702416226a7101e0fd946778d45dadac793c04dbb4e0aca011eaad718f505ede077115c5f0e8074f30d3fcd04bfa3ffd69607e17dc1dde1047e1ada38d6daae8b45716c024200bf2a2a249e0873b6894d6f8bd2f4ac64671525762e463a29c0afc86bdbc73223985feece210d360c7631134acc319de6975b3ba98a1cbeb8e37800064b4792c1a8", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "393236383638373931", "sig": "308188024200d70238ba91fb3261a3baae40852190517d4d75e2e5ee528651399d615990b0d08dc687d7d060ffc91271a37f66a2d89d9d22a80ce4cd1f8c4a4e02def1a070681e024201e82c779a915d496a101829012ee2b2cd6b58b9ad902cbcbdba49876b671f9bb9bdd223ca04997b9f946dc441f98068c6a8dc573c69a3db9b6a563a4b7f5a712177", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "35313738313334383231", "sig": "308188024201904b72705dbc8c01a422a34c3b72213ff9385eb56a6cb271c57ae5ee3338a735977d92666f474b670123f589a8b5d2682a685ebc24afce49039fc57b23480b723c02420161185a8e41a08b44663d001824c2483a5086e6da36d7140aa1612724de553894662f9eee58735af46c1a80147665a46048393ead5a5478f0ac05a62a02a697fd2d", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "34373335303130373531", "sig": "308188024200efe4e821d4b26d7114502a72384a16d070e1a87f166bbc4caec10229ee423a0a6edb4d734a641f5411db4c0d218f979bde59b40b516835cb422981af6fe4685674024201afdbf5e549255948a5c8188d26af9b6cefad74d5d431d7f7c49f7c2fb27329cd359520e14a9c3d3c6e935d95f12c48d034e387dabd7cec0b7caac64b0551817d53", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "3134333533393131363839", "sig": "308188024200c69ff834fd8c2bfd562322fe6c65c25dac7cc08cce12d2a841afe601575b54610cd10c682afc20b782616b38d1e63a24cc9a3a0ff7a861f93cff1d9d9701f98e44024200acb471793c0367a786366cf6ff4436bc140fa83572b3ad304d0a1073e7d3aa93da8a4952b6b5985e9e6b331dca2b687bf2fdb2e4bfb781cb0fcb44da56f3b5918b", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "31333834353439323034", "sig": "308188024201f6c06a2f7a6e10338018fc6960f7617ffd3d64302e1f26bfbfa6cc70f472614dda2ea5dbe4dd693ba4679fb23c926050d2b92761c1e84c5f2c222a21b3745eec7f024201ed0aa68af7c551ca17d9b0bb99124f996bc663c52f8e5d904717f14a7e7229dc31116769a9185ace5b3563d46edc24e4dccafbeefa7a8e06a08df63cd808da91a6", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "32373632313932373839", "sig": "3081870242018b88509839c9025991bfd154d529860131b66755273f0986aedbd4a9246e91e725d290c3193552ab4544ec8e57520a7c4f23e55d8d52291a8b40a51599a02b4f0b0241477c1e75aea8be30f2bd7e9551fc5b4105378d5220d094ac09f1ab20a36bbcd3ca7ed27e103527d4f6c564c7604465092c16758faa524d37a04888b90b8ae9b656", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "31383331363534333331", "sig": "30818802420191a669dfe8d305edf8e4b81dc72bae04032b0d93076f7ed54d46d249704cae99ea33cbd960ee22015245f310a56b0c450c8fbeb785e3aa87b968270d21e8de2658024201e5455c1bd53acb7d5f4e9c639eeb92ce6615762181d86cf335af8a64a858102ca8de95808f20a408a6cbb7f878b261bae448e0ac4cc17834697d50aedae3b447ff", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "32343336383939303330", "sig": "308187024201f19027cb434dbcd1b3a9220854ca1c200b1ed39eda51d2d6643edfb93f7465674644aee2c1c66cbd5cd587841a1c2eaee1178029822c94acab6ee634c0b633dd3a024108f6ecc30bab74b51a26d71bacaae18e59a659f9878a58af16bb35cf0fb5855d49f8582cd1be0a3d7dcfba99edaea18cc2be37b109f7c2eb10ec4556f75ba66acf", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "323034303431323232", "sig": "30818702414d3103f0c7747f5f7155f5c28b5a7757b10fc648784116438888d6402eb7d35049b240840c15445416d52da0c7bb59f0174d47d666d5ab219d4d821192618f40f4024200c22413a33604e0bad3459b89cb7a2ad0c023feec18bff9cd6942fa91c78a92610b90d9f494500a3b6552963b07ce6a640b44ebc1dcbec365d715120addc1b4687a", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "33333337313139393735", "sig": "308187024200b2081e6830ebcc22d981fe9cbec81bfe9a4dab9c1ac1bfbd199226e0ffe0c5b9b02a9c710fb219616d741200551c202f538e8ff20b6afef7acca305308a12445c602410aeebc6603a567271ee33c405219cdaf33c6b809d888a0f0c1c22cfbfb6cde33bc6fc5b5af12c952be0b4ca5766a0f9fe7aff69c97507010e2b29cad41f2751905", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "36363935363230363738", "sig": "30818702420105a9c72463f7031c7303ebdafaae233e8a0092cf488595124c00d8bd19e6b851c6040186ffd0f3a803b616c9d8fd46000bfb6f62ea86d566cc75725db6790f43ad024104a06ca5033dde4b771f78ca3bd74469669124b99bed53359b1ba394640b4459008bfe368ea2ea5c45b7cbfbb6a14d9dc02bf85515ca118ca8302a3b6faa33f548", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "32303933303137373437", "sig": "308186024166357c2698c51b89824fafdd9c7df6f75191102e4b59a92f3a91939a236ae1f656c523c2f4c5d25f4d26fef34b73e43505f777bf808e86d29d45b5dc65b2bebac10241153498ffdbbf4335cff891ece1cac19d11b5b5b83775976167ae43d52ad905f8dbea9369e8f30b9764ca4bf88e0a67aab97d2db687b22c2c7b313d90c77d641fb2", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "313233343137393136", "sig": "30818802420080fedb219a9123098db3e48f31b8c850b0ac0c88e47c328e081857f1fa8dc558a30aaa04ccaa2f788cd0b3a5ec6c441ae9e816c7207011d789c334ba916ed81453024201c29e1f56aebc705b611a643b839688bdbf6f684faa7efdd2f86cf5a2afcadffc62a35505b421bb5f55f688eebe49b70eb0bf4f40e366a1039cdecfddc58934e576", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "31373634333530363837", "sig": "308188024200db5b252f41b8139da572de931dc1ef569e49fa9021f66234693d08292922def384acf8a04d044d07fe7c548f1f0bf6388906bfa83b154014f7db2bae29444df37d0242017e9b0e27ec4c00a7e64467a203e21254dadc0a3dc381be924215af50c80ced936296c0aae57e8ace74c534ea749d061a8b7e1b5c85aa71c6cd36b30e2aaa44a5c9", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "3131343137323431343431", "sig": "308188024200de0b76360a26b366286d0a95fde0ff2ff24adcc63a2d2d45ef8c1bc8030d673cdbc2e274e343f2d6befb603cb8d6a7c739a785dbe4d974c4fd25a226e89bb08abe0242011a248975f286d128f1bc916829c7c59378459f61ebe7a46acbe3394c71b00e60779a87ee210ca042696b2c6476d1ed83002bc8cb4a72a7c06e62dae167dc8b16ea", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "32323638323436343933", "sig": "308188024201250f78140faac9b131e0a85eabdbacf6b999460c918f1eb8ed0154bd64dcd4609d5856f4495b4f428b93c2d58c162c3ed78263d83440b9444197daee103e2e3565024200be4536433ef008015ddf0badd9464ee873f90a8132ea906e95a5e8d0c0ed9540f6e27490adaab451218d1771dc2745a9ba01ce8d9b008737cda0e4a08c1e69293c", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32373234373936373737", "sig": "308188024201e667880aba13b0dfa45e81a8f534c691893150ce1d6fa543f2fb2a14a9dc50eead2f8f45336208637765c61e22732b8375619faac8eccdea37fdf0efa9497162f402420120ed36a00299f68318fa56072cb39a1e9a6fbffeeab047bc315ad6ff5f82dc433126faebef1adc6d2f64339239b248898c963ee42f356fb25bf11fead790d28f0d", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "393733333935313139", "sig": "308188024201d7113e1691c415b3c8aa1d662ce75ac9e54c4d5a9af698b338a2f5cdc77a88735ddbdc155192ad0ac8a8f4b46a861d0fd9526f696bfc5c27823aed612d6c80799d024201f4a09f3a975ce4d237c0e00fa60ca34e9be2ab2fc7fcde811e49bcbef086654f97a67ddd075f68137ba467560e5f72a4dfd23d123e08004fbafa9dfcb5319f09a5", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "31353037303032373036", "sig": "308187024201e948d76ca9fca0a7a99cb56c8c24066dfd0577d8cf0f154b7520ea2f97fa94fc791d21f5ad0f56d9adce2bf4c259698a642b03d1115b7f1ecd9804d30124e8c25202416d4ed5c27e6ddc80b69802e81f4da4108bf93ca082a61df296f15557dcf12b5d9466fbda02bf5399b175464a7b23866bfba079b1b112bf06b36723fd611135c47f", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "33373433353638373832", "sig": "30818702415ef361042a5dfb8057e99968fa8dc5c32e1e6fee6a923e8ca711c4851b1bab8c6dce47631c69a22b6d5744380f92318a38738df7f74d3ba06be2df0fae32d3b9e40242019988d998bfc270b68e7bad761e99f9affdd96478fa9d02cf04269ede6d604b7680a9dcd8a3f589b989c06fbe251c490b19f3c297a41367f1f6e2e39e2c4ca19251", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "393437363731323438", "sig": "308188024201b80a6592fa733ce77b3f48febe5b178c287288a976b857a4a020f07516577184a296fe37c3b88d3cf20e00b5c53d5881f2a21980e3ad3e8c0e358e363c4927e3230242008009c2401db711677867b898225217997005bb458d6c5109c56fd2ae72d3df8b8e4c4d5f5038969230cef0134ce17ba19aaaeaadbb3da7f16ce4700119c8d91ccb", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "373235343434333234", "sig": "308187024200bc3785bff1e5c9bca6d12d303849487f2d8159ff5df782433f3a616b9fb49280978aa88d940d0428097f82a26ec22246e64f675a5118c7ba67bd69f46fa6852c59024152469800e0c50d7a580a67008f07894376c3783395b7f41ef468a29fafcfc4a68b2899669603ad858fed05f232cf39844f2d8606355993dde9a6dac14a6eb70bbd", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "35353334303231323139", "sig": "308187024201289d4e5db332d13be1f44462020f3882e0a626814e5a7cf388ec8453ed1f921bdc3dd2bbc614e4f1be52bc0baedc8f662f7769a41aac95d7a1599390fd900d7ef9024132d876eae37c73d79073c9ab8e756d2bff616464a733acbc26d474d00caedf091dd4b2e99c0bc060f0d3d393a3272d76949f091e278bf371234832e23df8b15ce3", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "3132333031383133373933", "sig": "308188024201300817e6783f704b2edddbd4d016bf45a80ff06845058d3fd9a1dc1349ef902bbfb69f55b0141c1f925fea8bc9e35f44d72b6935cc7d49eb2da1f1b3097483a9ca024200e53573707d4533f65231a6d0539dfe5b885c0126021022de32f3a4199bb1d470cff7fefafa95cf9b31fc43f3326f3e6e64c31498bd71dd8306592cc1d987cc0afa", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "39313135333137363130", "sig": "3081880242018571d2e78c0fd3526c44ccf5d13bff203d1d60f8985e977395bdf8a842691b82d035809a071ea8556ae1b71acc415f208f4555165806b13758d8c03a692084c7da0242015ac3a64b46df043658dce21c333df7acca6d9ce3161469d005b3017c3eb42159333c71161e8576cb0c83184e5c513b13f43ee665d7fa8363d809060246526eea94", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "32383934333937393632", "sig": "30818702414c186d723b97360ad18329e4effe6a4ca80c17ed4fa8bc1137ca0d261bdb845c24a018a960c13dcfdfa94c739fa5390b0943335703b7adc0e5c6d97fe0f32313340242017a446f9f54ff41cb9bdbd02d236b4d2597689f99528a3a57cd10e5ee38fc9584efa5d80998c29d47796f02d20a9e7b493ce87a60448943bc55134af171c9162a1e", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "31323333353833303333", "sig": "3081870242016d8f256b29c501aab1f2442c367ee5f64f7e9d8391ad399ce7ad3a84f0656698474e3c7219e35641a9f67efe6d85a7d5e4502011cd61eb6b501007204aa1aaf0eb02413ae01795cead8f70af2b225b8b346c6ae0a9ae048b7768dc86c9cec3cf0709f54b29f99fd2eae87f0178dc266b554c779b0e2726753232b185a1efdd9b24dd2319", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "323131323639323536", "sig": "308187024201fd7c9737ab2cfb8044ecf512617fa199a04f4900a0ed8e6113c5d2fd19c7544279a29a1edba9ee6c0aaba2257fe19deb4042935754c78ee17aca930056c18458db0241400658f9d136e0a994a4219485e2b6a3bf36aadfd6927c14aeada8ec88ca0ce7a169b79860f7dc49b59d9cf629f5fe1d07a4eea0ea61beebb65615736fa3f1ba94", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "33303035343833383634", "sig": "308188024201109ce5f94541c60c9748fcb55f83ac9b351576bc92bf21e6a989302b076c869b2300fbd717d6c08b1ed619be256156248ddc33258a7ea58ccdcb223072278ae58d0242016f9eff4d78ad496290e35710754437ec0a9b30a3be091727dd256802fccfaac8c6f53aab0c39adbb4b34433fcc1f221e8222f3389c0d646eddbc07ddf0450631a5", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "38333536373632373235", "sig": "30818802420154a404a6cc2a53aca70381b3223396947dd17eed27d6f3c0b9b6da00d82f6ea4f584a522f8ae4c11cdf86f61ad12a431c6c38855402f87b6c37ca278e6cbbc2f83024200cb11bfefffe6559f084f3ad3fcc9c1513c2657f231847da40d2e5f8b2596a57955360075bc9bb82effa71aa1af9485be669ac4b3fd107c4f81071ad106d8b068e8", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38333239353030373630", "sig": "30818802420093cbe3fb6d4d3d42428997ab1ee236228878bd91f809b1a00626ce3806480c298ef4d43d2623c2a58d3daeed0a4090d0513ebaee412e852b2479f693da458a94e302420120c5822abe92fcd34c6624d71e98b8712173ac17f3d663eedd1c1e905c300610778abc54ac6f790602df4e2f4b84ca35cf1b5cce08de341b08db82dfdf87ae6650", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "31393237303834313635", "sig": "30818602412bff1eb4d4a3ad9676ea4e171ac832b4d1174b46cb4b9c7eb3cc4f8860795d49a1fe3676f085a44232f4f24c1368fa0538d0ef8e64be8cdcb835e2c23d0064a16202414b73fd94b41beca21416ce3e451585516713751235e20c04f90e5d6458ae382cd50ccf9523db05c7179d9aea6b7400ae83d28ae920f6701ea0dad7bc364ed59d29", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "37333032333538323838", "sig": "3081880242016dbbab105266a78d1df8e195d854a4fffd3d66bf17ec3f96e3c2c5fd26b2c59a156078aeacc5832d81cc6c86396f471cf8d4dc4f2891b43b0b4980469782a4f09a024201a9d5d04b820d5be931fde08801d46a27d3e7fe9995aa0970b7acc739c56e94c524e57e5de386fb1002e76d17cc84584e677f92655a0fce71b03703d4474ed477f4", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "3133343733373337333833", "sig": "3081870242012b83531b8b74c699a3a340cc133b559b0bc4abc0eda717962b7f68eb05f382307279a571760a88b6c11cd0c060a8603eda021038784fe1fbfc199e038a3d8eb26f024113228ae6c8e5682cd4ebd1125892dd95bd31bbf50aaef061614c4715a90f1db89d38bc35e4e2529c71acaa26ed5ab1f49c892c6f987015320d29359d2794b86f43", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "32353031353533363839", "sig": "308188024201bc7ce250d5a075177b749d919ca1b08d27d0c34d900f36c7f2ed0bd21adfd0be63c87463d547ed630a351d46615940b3f2acbd4cb8c3fc3a533ae2d0d5bcbe94af024200b6306e0c0a3bcd7f96c00eed9eb0576bd9c649caf9055607ccc0febdfceabb6a33fe805cb0193fbf8a36dcfd76b4260b280dbac821cbeba5cf594e565f5fddb896", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "33353335303430303239", "sig": "30818702413f3a8f128b0c66454badb41115199c6f2a899d51fc8706c57dcb6a0937015e7e2c404534c0ab9ad4d5ba135f559dbe8ba2b95fa6159ca8e6a11f4470b92d3c7ead024201ee116f82c612b2bef7b8882492a8b0779da07768f72b372635af930c18362a2a4aadb19d1297cc8051968588e1fe273970c298ab63ff1a51f4ef458c6642eb5f4d", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31343230333537323930", "sig": "308188024200bb6fb4f81fe5715bf8219a486dc94f89f685f89b69c026eca73a653910a3ac2401efbbd4c5e5931e03bc5ca3e34cb6b1f9f7e93fcef688c6ffedd0ae8a5372dc04024200d8bdba9909f7f8199c85e65692fafd9e52091be6dcb98affd42b17c6eca1f60f9fe68ce5e5a3699cf968a1b1c094d7e83b0f9266a04aa3319b459c259ee3bda13c", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "393035313735303638", "sig": "30818802420172ff9d99d166998b9d3c752c72435a3c88a6ec6ccb88cf3cc0b67b2c90a635a81ec08bfb9e5213822b23f2a933ce1dcd10b74f962492773046c9a2caa0398a10e3024200e014f3fd3b0802902c85f5b3ba39453d44b123c4efc3fd3244bdc5a135aedaa758e661d09ebe7af1b725cf3bc656f9a86646512fa6bd5f239574036d285affec4d", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "31363133373734323935", "sig": "30818702416f96a2eabd62032864758c284f5e691a274aeb5474a96aa183c41d43b6321c985207fb5480c7a6de8d316ff82fd97c9fd710e014c94d6586125e2416a60e710f85024201facee5bf81d66206d08349646020ac237970f7f695309cb6b2a16ddb464c21da01317c9074cebfed530250f818e3619740783d007981324d4f3761c72e2ccc2a60", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "383733363733343931", "sig": "3081880242018ce4b4738dc0d0206ebf7e804ee800467cd2d174aed6dcf556bcdb2c138cef9bcdd321a94979eaad8b7ff359c8910e7addf06671779aecca0f779f0de148fe4817024201b495412395cb659112efc8acd9fd94fb5ae2732a4dc923d641ff96d67d72486e9f963a9cfa680fb55791b7005b7dfc8d7a30072ce5afcbd4213b0a184e8eb05e06", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "34333535313036343035", "sig": "3081860241605e9fe2651fab3bc3cd356aa1bd6c866114eb14fd0bfe40fb75071247a8e7e5cc8c375a1b32ab99754b200018f964ed34f509612777cc90129a030c670168cc2902413c546cc8750c5675b150f4f49303ee22acf38539d6d3ec4acdd30fe57a1f9baeb62a8d8a42f56e5b95ea4908069aa5bb35da7af074c31c450047f8181c181c1454", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34353339353735383736", "sig": "3081880242011023b80c9ce34888acd9f5265b9fbdd18a7c87302d22b64231cbba16578edf53ef4cd7b4ac29f47e13c31444eeff405c89891b462c9ae3de7d1076e1137c613e210242010097adf0b6db68a90390247e0b761044d271f9a10fa17b7d1ffb433bda77b0ca4e1fb7cd5b2bbd9a786a82b4b5d2049b520f8b8fc1c5ad6c9bc99aaa4b4ffc37ad", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "383933363633323031", "sig": "308188024200b6ae707a764b79c5e532a138c72d2bf6dcadf17d045f722a2b5401e099ad67249cbbd0b2c7103080b0a3c1ccff47b58b526b1cda1a125a62650638cc71ad5424c502420193fbbf26a4d3a37565cbea7096cc57d6c93670eaddbd06956dda8ff8ec5fba338c449176e45baeae515e9bfce4b7323b4e3162a008819c23314a15757831533c7a", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "33383036303638303338", "sig": "3081870242016c0113463959f1aec914b7694d6228ce5c8208515253f52232b416108bda0b3de7d678175d455694fd66f49ba9ccc9f9a32cc368d38b606dc454dab9fc411ecf20024127009c48d3402075e8bb2869065ad83d662594cc9ce4fdee42aba7639080d7e7fc507c04c6dc45e39840a321b74d695b1e6d95411f4909aa98383d5b102831243d", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "32323332383137343937", "sig": "308188024200b6ca0c0e3aa951fe443180120e5e86e958d12d2d0ba40d047b16eaa9cb2c164acecd0cc20e7a6196bc68771b3ac5f82909bff29de560eb9f39e8faf6d7047f8173024201c50102f66d6b0255aba2ef758e5d1f148a9a579b2f2bca3ba3d5639f64412c2cc444937c84c69b8dc47885e9967f024e8cbfa7560c730cdaab72eccf93c3035271", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "34303734333232353538", "sig": "30818702411adc5909ccb73ced388d8cbed6aa7535cca6086f52b0dac552e6191eed513a19b0ff5d57d306d0c8b756d700dc96f189cb552d707766eda4cbb855e542a91eb147024201a6821d57714bf3805e8a28a65197463c09597b6c2fde00b64521f7cb6cd567b33ee344e9677ba423045ae5f5b47a39260548dabd6f961482dd4fe45fbfdfcb128a", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "363130363735383237", "sig": "308187024200f6cc5a98e84faa07fd46dcb0d057383d01ea7eac9e3fbd9f7d462bee76da988a0eed44fd59da6cf992c086bcf1092a3f3d0c9d373297bccbc0a1a25d1ef1736fb10241684733926ff39e177af57a0a3b4531cdd19b0c5f717f741d82ea493a09c310026a19386cb95c06ea7b346a2d90c01df02c6997c25dc0b89cc578ed4b0bc7cf3d48", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "3137343138373339323133", "sig": "308188024201ea2fdd9e045483942a46017a75cfbcf3e22905b56de335375b6a72443949c3d8a0af587b7dec02f1c61f679205d5c1f76adb3aaffb93a20533caea83284f3032c70242017e31471262766142ffaaf2cd9939d96fdb81fab2629b24f394c58966b9df0d464967e8b73cf2030de62d4415ad4c3cb5568d8c4d80ea8fda6a3196d6782338001f", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "35313237383432323837", "sig": "308188024201556547667d7bb076541f9ccc96f5d4948bf1a49472e78c949b3cc1024515c2f4e8fb93410dc75f961fc89df82cf7781b58f7803cf8ccd01d3733050c0fd57de1f1024201c395c774afcf7cd9138c2f97bc8157ad5eea934ee3e976fc0ae497096c6f7424903a64cae5babb13f25d53ddaf762fa3bf35649b748f86a12992383d9ded2e21dc", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "35303338363930383739", "sig": "308187024201db229a7e00aba5780017a0ee2e579ffd069be31d8358736db31d2881dae942e12d3fa0d2768d5bab095cdc03826211b48bbc2acada67e463da26b685a5e6d8a71c024133679983c9b13c3235eca932448d95945b3a16048c1f75708c054c3f36c87babce0e5db864ac5b63061011d2dbd84b2f239e9b3810ec869de2c663164688b0a7b9", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "33383737303432333937", "sig": "3081880242011cc7f10ba1f52a7d74a20955c7f327b3951acdc4af205d0ae66eaeb1b9f2bc86f98af26c5efc2240951194d4ea77a5d605afed96712556005948b4666d5746c4a302420086c3bfd2ec110d4aa0f89f78b9cf93161db88e3777ae0e986ff0248f7eff483bdf4c58ddb5a70863bb0355c3ded63856559cebe87ee71112014bb44f905c699e1c", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "333231373038313738", "sig": "30818702415cb5e2b95fa6447a73e57455326571cbc604e9e54b593af792ac4f3add9cab628d8087d2da79c217c808ce000c45b86555beb6f2caa3ee7c86d7bf3c5a4f8f99a20242013c057b5a8e2d5c66ed0fe4b059fc9a2c218333835188dec24cd52c1e691a745c087d199f5e08911d798741aa0d716c20ed24baab3cdfe1f5a66403152b0ff31776", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "37363637303434323730", "sig": "308187024200a4d6b83cd0b2ec187c7543b5493731a99f5ae83303190f122f893ab7c6d8c6a25b46aae1cc1b4e84a13bf52f3b3a88a6b0d17df6f1e2a83ef68456556a80b4699502413ed11506c4638677d48b4ac940504083b4cac13bf53319bb312c24cc9dd85d3d13ee63022cbb8dc123da8fd926e6f0e23181308d00f81eb10c584e9b3f4eab9bb5", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "31313034373435303432", "sig": "308187024201a09b6cfb57008a004d719ed63c45e4b0e97622a36ed2d2b22dde5c633fb714f0279108bc44e94614c9956ae9ff22c51cd847e8ce4314e767d33a01fe62e5b1fa1c02410acdba06f7227ca1ed8d0365a9ac68962026e33a593ac08df7bca7e596a5d6717667172ce494fcfe3a9dde26f545c86bf11f8d91ca8a410efa342d79217ec7f094", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "313533383730313534", "sig": "3081880242009a544548cc696921a3b0e98201400bbeaf37876f45240b508bd193211826a997fc66dca369de89ffc72ad9c1a67cd9b15651fc34807e7dec3043230e65b7cc98820242015bf35d1ab035a1fcae2e05b13677ca2dddd95a553d319e71c59a823421ec6b05f659446a026072f2dcb9c3cb62b1fa302d43754af38dc681a6809db16e4c32865c", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "32323631333835303439", "sig": "308188024200db87573f7cbfa341b96ceb4bbc79b959593b55cd8f638ef4b5e589bc1664ea91f7473c9d913a0901330784341a366314b30495a45a88c8774d985b4e5ffa915108024200c039fabd667c40289f8a065b0b43e807a0f7f51f7767501588031588fc57317a712cb4217dfb45fa905c8c7b5dc9a838ebb974b5a5b456d08ad6b1d79707c54257", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "37353538373437363632", "sig": "308187024129cc57f41ee48f6d222cba402727e0eee262d67f922e4c0488c276960409dad40d7f719175eb009cc7088cb35f3aca9114c71915b1158c78fb5101649497121e5f024200b1c22f956b33fb228f7dc12e48baa527762ae6f4c229a4162c4753cac39827b959c4b0b7994b877bf0901dff5a6d4e9757cfad14b0e5b7717e1d0f54c335d08a7a", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "33343939333634313832", "sig": "3081870241598f0072da616419220ac9408daf79cf4feb4d0d09fc48b3defae2e34c66b88aecff56f95b422131c1de20205df2a8d5ba554db78eac388a267a36335964c7776f0242016f2735e142d4280ce39254a4d0f8fa8f4f7542f88fdb1a08bd7975011950a9166b291de602193ffbc56adfa4e5212e8e8bb24ddf80a94d762d8a671851dc6621b8", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "32333639323733393835", "sig": "308188024201025c2317f1e414e14a5b41c7645bc1c2d057dcb23b3b6ec5bba84adb50171cca89f806091d1402b61ead88bf1df50af1b97953244e2cb4278639e4bbb74a040d7a024200d3ec9a1e19ce0fcfd09c65016e96500f95a8a7255191b95e11e2649e2efeecd928d29a1f540935e0528828a49867af990ff941d1765cf868f08504ac12e3b4f6a4", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "343933383339353635", "sig": "3081880242010d14ed8dea7292f11c33755145fbccaa0d99c79813ca0373e18d4fd11fb653b456392ab09db6087035dc3907617fccc5b040d21a675d0f63f4af74af8c7211655b024200c76242ece39dd3aee07cf60210a6575a0670b2821a0856ae2d8505b58ea6570a69ec5ee3efcbd38573acaeed2c6ca3d9e69c08e204093cf8f647089632ecaef7e7", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "32353334333739393337", "sig": "30818702416196bbfa16cf1d5ac3aec198d097d113453d7df5ea535b6eb61ac387858d179f6981f64d9bcb60a921d6764d3324eb9692542bb18b0b5400d985bdcd940426b4c6024201ffb4a363ff951fea35487f32dcbefaf04e89b73f585598de4a4101c7c39804bc787d92d74210acf86f5a6d03e878ba5f5f5d2724abca0c659f343c3d3ea5764577", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "383334383031353938", "sig": "308188024200ab2cae0eb0f847e8b17e986f21be7572985e9f037a27f82b7361a7771af68ad2c94e70bc70c051202b00465f8be707957914562c0a350d7b8448867f39b8b7f2c4024201d5ab664b4f3b0552bb84a520c9ec94ee8d6be4b8b3369e10bac7cdf4ea7c969310fce183ee855e460053d30c5e2da82d13a06e03c2f961b79d6ec777444b5647b0", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "32343131303537343836", "sig": "308188024201ffa079d25bbdfc6ea73a3601d834de5c5854f97c3571f10c7727f3142681aacbf14c862206cc02362ab629e3b3e34994a68333d8f6382985b84b5da5984fc9f46d024200c4b5a3b09d990f51cd33e103e7f77b524319000d2ef08e9a10e532ebaa36d9562b36afffe35c3baba49643f04966d31a9989b25e2cdda65c89c238f083ea4f8c26", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "373836343638363335", "sig": "3081880242018706d81d28ee89ff9bee8ae0a13aac7d3922943fab2a8ff8d3df91c396f03197a0ddc002e825b229e39a2ff10529f935d85085fb0a6a8af6eb6f9e10c9d260687e024200bb9aad24e5a92d7dc3ee6e29ab441efaaa4a6db4e31502ba8e9c9190eae1ee8040198fe8db5fe8b5a0228933fcb309d7dff4ac00dda2152bf3b1f8e3874c9bdc2f", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "33303534373733373638", "sig": "308187024177b04bdb6f839f14de5ce476af1f26e1e39c8653141e4499149a8b267786917096cbcb7a72b0d21a3894abf165b653bcaf6075974e073214678feacc10557e0f05024200eda59d6d49bf299d3eeaf5a2b9a232f7de5b08e144bd359d663d49730985584dcc091e86977b77845f4a3e02a01f485f7cdb891968012dbdf4352339abca4966dd", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "31393237303137373338", "sig": "3081880242015de07d01e25e26ed3069809031e96cf318860495c15a244c42bc0c1151c0a2ef7cb2a2841f0841a890c5c7211585fbb63850c09f5b4a8c28f60e764207a00ab0bb024200ac1abfb9f8f9824a1ef744ba1299d295848cbdc98691984ecce0473899ef3951a91dae6aecca52a8ab9e04d57f77bbc178e26a31abbd8dc3103742ac0414a9db40", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "31303531333235363334", "sig": "308187024200dfb9098e386dbe6c49b45d834c06bb331912a4015dd2ac81036d8547fbd69a98d55601c97901c417e33b6eed5e499168cc14b34fb120180cb0b6e11f638182ce93024150214812721b288d270bdbce361d4c5acbad454521111d2adb1ea4b371df5c380e4cbe19d0e780bb2e268a46957fb496d2719ae9516d41e70dedd8f9a1f7e7478c", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "34303139383636363832", "sig": "30818602413a717996fb34063e77e05a8755eb90dc39f637ba6f18e702e8d049e18790338469cb916907cddc404f73abf047175086de9cc512cd46b9510077ddef8ed60456f902413d4067f4dd3941fb46c2e3d1e13998ee7e42d3265255f4a680fec372e57fd269eb843e6441c74e552ca9690b1007c16f339137c48d8e4b321808558907736a589e", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "3130343530323537333530", "sig": "308188024200b915a03331a4ef1bc8129d56865bb5158167666c5539442d96e417884d2a41b330ef63fb13f66fa77cc6a300e29e8535cb09305ab3f49d7c464d7403c54a428ffe024201a3b5ff1bf1c0a19fb268a016bc19f8747d2a0ec06acfcb7564e8b673551300dc0c6988a8d57d2e69b90f45caf42f2769ce54691c24f7d4ccb322dcb01525606052", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "333236393538353830", "sig": "308186024177572850ef3a79cd9df6946e1ffd372269f3a50515b9f8f61b82ee6d29af25c5ab17e05394c14620f15c7d047870102a991322baad29b30465d6d1271b3e85efed02414e070c594fdb84f678beb2be1de5aa45ec36ea3c9ebb37a25769ce01bf90db3f2458737138afedc21dd51515241e8724e9a49af12154a278d89db898f66aa10c36", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "33303734363533323431", "sig": "308187024200c71690b845e4ee1e91a8452c65a245860d5c13c13f359896af8b22466901a999b033ba49c4f3d9b2b496bb03d1f44a523d64815f3db29b262cb31889df5680613d0241561b5957008a469c998197956ae855d77c2c3bebac91d307e9a9d98bcbb25a97bd914165e08b9651fd70e0a8215c50ce941059dd39cdea01b3520337b43e5c8f2e", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "37373134363833343830", "sig": "308188024201b8c5100a6021116c8e988c66611acc9da7cc5ec621f95df537d8082ee3f5c07f7171a8150ef45ce3f3dc802a8911205a5106b963a52ea6287588cb0dc1d998ead8024201ccb6df7546f4061f14c6a0950e0fd38372e0c42588c6fcb3354247398d19a5c91d86617595c385e93a087b84c7ff1fd6c4ee4fca2fb111662788dbdc1bc5217bcd", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "31373933333831333230", "sig": "308188024201de9edf9e10b8c65452f8ea07c57f01c2f2ee6828d89bd4bceaec4046645a970e85b41cf7f04f3fcb357e532e0814d0b7c8fbe1e073d555e3fc13a18bdeb8088858024201b091f6bf09385dd2d7a2209637332be35e1c7efba0b80a97b268b5e6d50ad2bff662ec74fab0434890a48e39340a494f45a7de4b886513829854de83ad9abbdc9a", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "34383830363235353636", "sig": "3081880242010ea1fae566aba575e06f2e676777f0f4b832ac65474c00cb4363a7e96762ac557912cf22f737414fc386410d2140a07f2cc604759a4e50ca6d34eff5a9babd18680242008711b35d2a5a6dd36630d0bd0b0c1e85a11057583cc87f59bdeab74fcdaf407235d68d24bda26e6b86645ba31301539effe8e51818e6c915b333cb868ec35a62c2", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "3439343337363438383537", "sig": "3081870241224f2a6797d31f669a725a0be5c48e569f313bf09780aa49c8430b2ed3e28bd82aea9a46fddfdf9ff6d8be9d50dc6edd9021a1aa8a8707d19fbbb164d3f2f2c967024200aecdd04885de9bddc60d84ebbf9e868d28542cdfccf7b1a306cb188e18986891cb2537a804ca99b8da8b4a8862bcb842161b33477afea8f224f4277db93a4f66d7", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "34373038363839373836", "sig": "3081880242011e71fbb3bb894f71425475862000567c6f58170fc2b121e1783da56346a46113e1779203fd55f5be57fda0e7a48d46d1908b8033f87b577eba64e604e29ef8693a024201ef2a00cc4f978bcf8a0cca6bafa93a3204ca0c9bb4ed684ee89a569638e99d84cb470f51fdc3786e1ad383c5a90ae49bc1e7e7c14f6bedbabf1f09a42567e302a2", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "33303239383732393531", "sig": "3081870242014867d547dfcce3333d412a7e177451c8fc67295ea0a3fe1bb99c1f517e950c4637464a8d746f217e5ea2d2e87676a76b8df889364a993af971410b2a652bbbb18d02410429194561d54794f49d752bd42660856de51f473086380ffd33a856cb8cedf6d2af61e9084e1fc7f6b0232ca5b8343d2aeb82ba32c7810af1f1332d06aadae8eb", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "33303137313930333835", "sig": "308188024201df9b3881756775ec1f0ae5de26f7d6f8d563c1afaf7942f3059ce75ccf75a34a0120955a1e0ebdd89de9b08b143dcb62e40a711a37e6408721f80e560a17f2cbc50242012d7eabe9560bc23bd62266ce1bee7ffecda8c39edf7eaf5608338e2b546b689d2102e29bd3064ef670489c8599ff902b098211107edfda5920eed9deeeacb8023c", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "393536333633393339", "sig": "3081880242019fa4898ac932313efdfca7e720a52748d11df0d00a2a36bd6c12ae037c8921598368141dfc14d9b4d5736b0899774f51142e109c94bfab196eb7b96c2dcb621c98024200959ff6ea8673daeb5383357d0be542f64a0385bd7ac3a13cb3ab5c30f08f58c16c8b17d25f73298245ae2a6e060af590b4e2ad870d13a3ed14d61376e3c378a32a", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "35333030373634333530", "sig": "30818702414953aeccd2b35bcdf7990ca391d394d198ed627de0b4b5783fc12e20967ecc9729fe6c9ae51c54c566d52356ef4fea2d8da4423cbe5fb82d1d2984a2b6f20ef42e024201092dfa2b04a5e1b574c1d907f66e2484aa772c936f2ad3c03b61f7d01290ee849d3e3d528593a304bc2f645dbc3ca83cf2119588adc29860e72a30b1eccf895dd1", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "31393334363634383434", "sig": "308188024201d9a16289b154bc7b341ab5f376df9d09acd1068b67bab090307042962bceae0199696af36bab0f647906ed7e0a5627c961217e53317ca0896bc650df96e9461f5d024201ce2b975ecbd5eaa28883ce90560b27b4c66a284c501e57ed4ce589ecda801def1d15106cd8c830ac1c00b529e4f46e188ec5f90ef650f520b1cfdc33c6ea1710a8", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "3335353435303535393632", "sig": "30818702420136f562aa082b535fd5657052a62e794eb6e1b81881ee2bfc41832dd7284be075f5e7e369de8f7bf2660c81e690ab9ceff89750032579f2f19715f6faae78b595090241784aa162e2ea1ec60811b5e4fa29907210f3af5148f14b35e641e91d09cf580cf0436662118c9edea88a08b7d88d3562294a0c326a44631b4210e6422d01a0ff82", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "31333031373232313038", "sig": "308188024200e076bb4455463845b37d0ea66560171847d6601df05b00d3ac292df7e3f348b6fed892ca07cb9c9a8a66fb1e33001906e68e73fb4ed223f169447e763183e8e8b90242018e69c337db6f330128dfa27e413fa46681741581452989e530b686406581852a62f0281a10cfdcf10287a58617555e54216be5911f2eca50856b416987c112d808", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "35363137363931363932", "sig": "308188024201b5f54a668122d72e4ca9db26c4246dfe4bd9929f196d76daaccf0658e75cf360ffba84be8ec6a8b4454917c921b68f1db202da9152fae152feed4c795172f6e3dc02420144c3580c1ac9a80223b2b3f4b67bf224ee7cd2e2f9cc89857bcdfbafd05694208b87a1fa040e33211ec9b8f59dd6e8313d8d9c67292f3f845cfefb197973b1f5c8", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "33353831393332353334", "sig": "30818802420115239e7d4efe196c90165cd12f4992c03ac4796adaa6efe282b9dc269a80a27dfc09efa3f3247dd8239de2444c76786bb36967bc6c862778e0088685c6ccdd05f9024201c560b07ee45e916dd9d0d7cd03be4ac14183f1725a4757291af193a9cfa44f0b979ef27047915a3c2da505c411491d243de19b604b1d7f610dbfcf50ea79099093", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "3135373136363738373434", "sig": "3081880242018ef019e7f54e6475232d253a6af62a0a1079fd74fa0f1c0cab921f09ce07d22a80ef7235255bc8af01d6f487c2f48aaa59052555c66fca89a3d3fd65a5c215d953024201fe467d44a3b55133286673417d4d4f9f42b6e5e49fea2550ce98d1b441f57711d8fe4ebe1f6664a023bdff61f8abd7b2eac163dd1e668875055b9a8dccb8537682", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "33313939373833333630", "sig": "308188024200aa9c6b87c3de041deb72244fd3b2628f310da41493090a7c1bd9af654b0ca793175e2e32ce623dd7f8671085066cd1cf4e7fb60a573779b142a20704d33831ac520242016b84d9f21150dcd439859e714fb23142ef8a16259b520621156fd04052cfd5022bd31fd0dcb4a1b0b790cee745de38f74f3dabf4d63c71ed2147f43851fa747232", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "373430343735303832", "sig": "308188024201bf9a7c27c58f163d27ff91a10093ac5dafa525c39aed530eeca981c578dcc1ee962732e77093aecebddf8d81daa923477941396938f211811dfc34e4a6eff2ed6c024200eb3f6b2f40f9ce33dc679f1787ff6c00143f946bfd2b6e10f358b88ca9146a4133ad4356efe0bc1863f9f9475d58f99f6aa87f88d0ad2395699c09615c112c12ed", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "343137343336353339", "sig": "30818602413702d9eb114d84ce30b036b068a829e16882e4e73460b1884fd734c0a238239fe2dabf1f623e420c94973d499987d497d38f115767a5205119735c7198f5cd0aa902414e72a80ce8a9fcf312f1a58c6d4bc43a32d30869c5e4c5d8630d547cc22c02eb90ef17d8e9a588355b6666341ff70a24189f9b6d042ff44699f18e7794f61ae590", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "31323335363538383839", "sig": "308188024200b981f4cfbc9f8f7123c5a81098b1a2b2b5ebf4cb3ec332a78217d049669f5153802647e3b888199405e84c571c149e5c88eb20e855d2d4511a737baf968d498a60024201c75e5da7adc47363e0c10d0097679ac8135624844a54bf838348da350688d775a171c4dfd760c13dd606fc98fc71c9721ae2ba76658f9dae84ee32512d474d4a91", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "32343239323535343034", "sig": "3081880242015ba3c5c8a7d86775f6445bfd6ac06e4c40323862a8754b1ba092bf3cec10d247b6a2edc599d914bd2a5aa00ab5a1020d4897c068e569dc9f412dcce1a758cab208024200baba7f93ce5cfba1a28e5e05509d95371cf1466e34e8b8c6befa4bce7e59e6a2253b63c0a7b472a61bd376fdb09700ed0f7348e2187c8fe4ca87644fecc6e3dde4", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "3531383033303235343636", "sig": "30818702414ad0d674b3a91adc8324994b3bcf1071a9b39ff1f493f272518623cd09025be1dc0d6c987412e37942c0ad2d0012c51b8dec7b7001681723b8a92daf919f3afb2a024201039fe951958b044b84db980853068f645ee68834fdd1ca904b5181bea15e57c18a6859d56a4d12d89a441b47a839b680a0e4a089fbacd1ce6d7d0b3ca7edfd0510", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "34343736333938323030", "sig": "308187024201115c5bd71bd8a15cdae52b23da52080175279e49193c62980e2d2559847a173fc382e50fc63e1fd80a8fc9272319be5f97a2f8567bebb65ae5f5fdbc539071a653024157ba29f8107a3e6f737e983abbdeae230341b06d6d865d1c0603c23f319e3f1c6b02b59ca1e263fb3158da29e762846a02cda628e53d41258fae403683863b0684", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "39303630303335323132", "sig": "308188024201dd8801b1ed5f0960ecc399bc9e30d0fd83033d7128ffa3826675625dbaf327e294eaeb7089b5e8a8c307e637c79f749d93f9d8f834d4913e31f27d625711283f5d0242011e6377d6b68e848ff2a71552d8347859468566cb4896cf79ee5bcf91f75e93637dee313b47e4f9e7c94f8869a81238441b5739f031c9c4d4de5c79340717e369b1", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "31373536303533303938", "sig": "30818702420197edd550c58d5b3331febe15b9ec78f4f4d2ccf9cba462f341f0f8e73e8372aa6a3d8d2db17f2ff33115754160277701298db543575560d8b11de14836ab043f6a02417a5e1749aa35b07ab0ce97d9098f3c89b1a69919f8ac7513991169c07ece47b7e383cda503a5b004f1c6ca85b0a2c134d52c964988cd6b19821faefa35f1fcc3bf", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "34393830383630303338", "sig": "308188024201ada24626706c0dd8962db4d745494068506d39a4ae6413e980f02d3c0aa1d66dc8345a767afa1b9cbc0bc6d9f6efa215d4a22f7b1b8094bf835262828a39c226d2024201bcb2cc554765df59067a24081f6a2982c37b89025b80f20255421b5bd68b0e1da5b60446640a58e270f7349a9a48681d490307cedf659dcb09cb8962830187fde0", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "33363231383735333335", "sig": "308188024200a5b4ffa61d97713f9512813ffb68901d216eb649167780ef6fc7b0dab626b8a6beda7b0af3ace07c4281ebc6dc9db4e14a1ce2ef0616d599f8a3e226ba81b7adee024200b2d7ceb21ce8c72b4a4951104c93df65ea8fe5176f8b3e6dc6063be1fc005f59098b0fbc790e34398b2a5289a5c5e1487d8434e8af0ac9be5477d7f571f5b0afc6", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "36363433333334373231", "sig": "30818802420107d319831da7f253b044e229a58ef5eb2d7029382bea1a4db8095673e072466e4d69693123ffc06bac6f626612cfe760e53dffce3f643fff7cd85eb3aa2d6d17050242012b0acdc6b84489e6832ffb25d3c980b40922959e83e81b7f3998015a6884a7d26fe211108ab050affdfc689f4e82bf98ae777d242030619fbbc38344336ceff3a4", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "34343534393432373832", "sig": "308188024200d296056a992da1d912dfaf564e49a4ff33946adee320926f606730dd4255dfa941c1bc38059099320525a87e283859d0ba99472a8f59dc4f3d8d4307e567c3e914024200cf38b5c76aa99b8818f7728be518b5159ba1b8e1037b359fbfd33b2a2da3fc86e4b9600427f1a622bcd8ef9dc3fc916aaa8d81b288ae42496e0155d52347d8ad7a", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "3230313834343032", "sig": "308188024200ce546b84fd4ed2ca5cc412da1b22ab12a9a6f0cd26119e9400a71437a2551d948627f253e8d805a43c1b53f4e49d4ac88767aa20cae16e4824233ff8f8dfbfb5a70242015fc68020d9c71f960d6253eff0a188b4f01cc075883e8a529839b77ac89a6698140537278b0373292c300ebd48a276d5d40a7ec572a397e856bad0b8b6bc3fc4b8", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "3538313332313733", "sig": "308187024179c8514c8cdbd8ad3af8d89b46694745282b8d7b26ea4deddd778ea9ce8079e0cbbe2ce6891a138f023053cad1f2d59a8fe5dc0111c0a5091b239d00f02bb97fd6024200df02700edca68bb6917ec75b0ec106ff89a4c0c74989b058e147cd23aef9e541b951cf4fccf70c110458e7c1c04adb7f082d3170698fc1962668ff6e277f3f3636", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "31313833383631383131", "sig": "3081880242019e552fc9bdc4dd34f3a8c902f84668be067137fb013a4911a4ee72dde4291850fa3d82cd841e3cabddcf9a114fcf3eac5b3dbce74a1a23474b360fd0f65e7c28ff024200f8fb2289cc3b66129ab6cec2decf9431ee781b920e2c3c58897ea1797827b1dc4e0141bdb223ebd5dba00bf603aaf6fb5b7ea8cb36798e97c4bf748cde9ec709c0", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31393232363032393036", "sig": "3081880242011e97a758b809597aaba432324a361d823c6e0769903db9029c996104ecbc56dc5944d604dcb9f99c24edfa042a49ce8f5cfb163ed177c574a6ca289880174fa9b60242013ea7dbb8040c05f761e00abaefd2ea10ae0e998653f77779418c41b3bf58d6d3326d963e14797c002a2651c44c8e578aba9b220af427a033a3b47281653fa953f4", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "393735313433323037", "sig": "30818802420167be329c69775e5b9953eb6b1fcfb5c7fe266718692e2319c81bdc03699d49771d4407023496070dfc19d9b766972429be1421b701b2579a773e0fbfaca595886502420122b728309996f36b2300d97ee1214bfaeeebd954e25aca3aca2b985faae6a79f0b5ded50866468160954663c9a7afb4a8a8c327458491554d511b81b8c9e113d32", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "38333135313136333833", "sig": "308188024201ba351c69ead837ffb48d958d5f44c4c054f67cf79a4dd55de33fcf81c61b4aebf2f9a0aa3ef3c51b5db42b25ea221372cbd2a95638274fa377de06cdafd7ba86c10242013ba85a1343e41677f97b2020233ca419660163188345cd8baffbb39629868237b7c7cb297d4ca6150fe652139af34dd733ca20276d26895af1f1476de2e504c691", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "333236333136383132", "sig": "308188024200d444150b1bdb12f9b40a9b4b7368d3f083bba98dd5d580e4df8767159b144f1e3b0825e80b8c2bcac4cd35b8692c31dc001f1bd63c763e4791d95d8bb641003b5d024200fc06f26a7357b675b39c566a9cb30a99053721fbbd48bcbcebd463e857327b8132842bf55a22b76d4848afc2535d473c8993e49aa2bfdac2344554c4eb0df0c9ec", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "34303239363837313336", "sig": "308188024200e06e110f7db2367e9927568838ebc5982e90d5aa09c442e2c1073027fe6237230625bbca2495a765fac5b35ed0f1da59b70ab9af366697618c112ec38cd8546130024200b1afea64f3612903bc3dcdcba4a2137745b168e8db230fb4a5027c99daca6dfb8b6e40104f82bfad536a5f85c9b43f6b9fc8f4dd7f1119a5eb7b2834bb24cfd80b", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "36333230383831313931", "sig": "308187024177bed86ba5729d867876dd8f2de6e6c9bee584505b5d471ccd0a9a54658a7dbd88e6afa938da50cf7e7afac1e06758eb0f999efa46d439bea76dabe0b08fd04a5b024200d67c25f1ece420b60eb9a60fd9590f84d87af37db4a39a20a6dcc96aabc92aa8197d836e2ccd20e2739674f5f792258593c6009fb02d3e5e348ba27300b5d372b5", "result": "valid", "flags": []}, {"tcId": 354, "comment": "special case hash", "msg": "35323235333930373830", "sig": "308187024201a7796c913ae8f43ba70a5a405d3ed87aae144b62ad261f7074a228b440b07375ce41d5a746cd296e1232f185c3be9dc8f275cc3867eae1e80008a880e5581b8b3b02414c6d7bb94edff76cfa8a11599a86783ab63304151ffe49985b1c3c39b36d03451da0726ff28be2567ed4cbdcab9b5697d4e6851a624790b3ac6410b0f895339c8a", "result": "valid", "flags": []}, {"tcId": 355, "comment": "special case hash", "msg": "31333439333933363934", "sig": "308187024200adbb3974f657ca2e8e099d898817c62959733b8808b7125f872dc1942bf63e50cffed2d598d70a9b59131da79c55b073d49786724701e6810dc540e2f6760610c002414ed5139909989c18608a2dc05a984b328ae8f92d0baef270ae15b7f97e9e2df780584d08ca285df39352b9631cf8e1866c42f1b4c4c0a67cf4ce7d101a423a21ce", "result": "valid", "flags": []}, {"tcId": 356, "comment": "special case hash", "msg": "3130333937393630373631", "sig": "30818602412e537b14ae1f1dbeecb0cae036b2cfd921d78174a020be40d07f29478194f832b8af3915a5be2e67fe54404066bc434e0b4636be86ced86db2315f2ea5f64f15b5024142bde34b1f608f3fcd9da30481f8edc863fde24fa1f679833e9fd35281cf78ac42c7f13339a9b798ece954a349b84206fdf811098290dee904c1bd704215c0b1b4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401c22f5cf1065e9501aa40c802a58b944e8747d0dc332cd040fc27f1ef18a1af20caef9d53d6771ac8ab9b9d46799471feec8d651f1bcd120740e7a6218ba2634a72018281452c5b0f2aec6eb143891ef7775748c889c03e7712ab2d2c5c614234122ecdf2ed25526e5189fd3a90ebd17825b9ea5edfd7214358e6cd99814a40504e4472", "wx": "01c22f5cf1065e9501aa40c802a58b944e8747d0dc332cd040fc27f1ef18a1af20caef9d53d6771ac8ab9b9d46799471feec8d651f1bcd120740e7a6218ba2634a72", "wy": "018281452c5b0f2aec6eb143891ef7775748c889c03e7712ab2d2c5c614234122ecdf2ed25526e5189fd3a90ebd17825b9ea5edfd7214358e6cd99814a40504e4472"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401c22f5cf1065e9501aa40c802a58b944e8747d0dc332cd040fc27f1ef18a1af20caef9d53d6771ac8ab9b9d46799471feec8d651f1bcd120740e7a6218ba2634a72018281452c5b0f2aec6eb143891ef7775748c889c03e7712ab2d2c5c614234122ecdf2ed25526e5189fd3a90ebd17825b9ea5edfd7214358e6cd99814a40504e4472", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBwi9c8QZelQGqQMgCpYuUTodH0Nwz\nLNBA/Cfx7xihryDK751T1ncayKubnUZ5lHH+7I1lHxvNEgdA56Yhi6JjSnIBgoFF\nLFsPKuxusUOJHvd3V0jIicA+dxKrLSxcYUI0Ei7N8u0lUm5Rif06kOvReCW56l7f\n1yFDWObNmYFKQFBORHI=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "k*G has a large x-coordinate", "msg": "************", "sig": "3067022105ae79787c40d069948033feb708f65a2fc44a36477663b851449048e16ec79bf5024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386406", "result": "valid", "flags": []}, {"tcId": 358, "comment": "r too large", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386406", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401f974fbc98b55c4d39797fe6ff8891eab2aa541e8767a1b9e9eaef1f94895cdf6373c90ccb3643d1b2ef3154b126de937e4343f2409b191c262e3ac1e2577606e58006ed880d925e876beba3102432752ce237b8682c65ceb59902fd6dc7b6f8c728e5078e8676912ae822fda39cb62023fa4fd85bab6d32f3857914aae2d0b7e04e958", "wx": "01f974fbc98b55c4d39797fe6ff8891eab2aa541e8767a1b9e9eaef1f94895cdf6373c90ccb3643d1b2ef3154b126de937e4343f2409b191c262e3ac1e2577606e58", "wy": "6ed880d925e876beba3102432752ce237b8682c65ceb59902fd6dc7b6f8c728e5078e8676912ae822fda39cb62023fa4fd85bab6d32f3857914aae2d0b7e04e958"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401f974fbc98b55c4d39797fe6ff8891eab2aa541e8767a1b9e9eaef1f94895cdf6373c90ccb3643d1b2ef3154b126de937e4343f2409b191c262e3ac1e2577606e58006ed880d925e876beba3102432752ce237b8682c65ceb59902fd6dc7b6f8c728e5078e8676912ae822fda39cb62023fa4fd85bab6d32f3857914aae2d0b7e04e958", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQB+XT7yYtVxNOXl/5v+IkeqyqlQeh2\nehuenq7x+UiVzfY3PJDMs2Q9Gy7zFUsSbek35DQ/JAmxkcJi46weJXdgblgAbtiA\n2SXodr66MQJDJ1LOI3uGgsZc61mQL9bce2+Mco5QeOhnaRKugi/aOctiAj+k/YW6\nttMvOFeRSq4tC34E6Vg=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "r,s are large", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386407024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386406", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040029de1ba495032fe2f4b03173aa8edede277d064324ff416bd652a123ac40a6ab91e189fb42c5c67ddeba359873ed559652ba2b8378508c69b3eb13d395f11add84002ab8b1c49bc1c079d19fab2dcf30f77e1d8e7ed786669149b254d7feb89fad748ce8c9937992fa64ee025f7aeb6aae8c86ca9221a5531c232a70e6bd0c11644e6a", "wx": "29de1ba495032fe2f4b03173aa8edede277d064324ff416bd652a123ac40a6ab91e189fb42c5c67ddeba359873ed559652ba2b8378508c69b3eb13d395f11add84", "wy": "2ab8b1c49bc1c079d19fab2dcf30f77e1d8e7ed786669149b254d7feb89fad748ce8c9937992fa64ee025f7aeb6aae8c86ca9221a5531c232a70e6bd0c11644e6a"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040029de1ba495032fe2f4b03173aa8edede277d064324ff416bd652a123ac40a6ab91e189fb42c5c67ddeba359873ed559652ba2b8378508c69b3eb13d395f11add84002ab8b1c49bc1c079d19fab2dcf30f77e1d8e7ed786669149b254d7feb89fad748ce8c9937992fa64ee025f7aeb6aae8c86ca9221a5531c232a70e6bd0c11644e6a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAKd4bpJUDL+L0sDFzqo7e3id9BkMk\n/0Fr1lKhI6xApquR4Yn7QsXGfd66NZhz7VWWUrorg3hQjGmz6xPTlfEa3YQAKrix\nxJvBwHnRn6stzzD3fh2OfteGZpFJslTX/rifrXSM6MmTeZL6ZO4CX3rraq6MhsqS\nIaVTHCMqcOa9DBFkTmo=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe02420095e19fd2b755d603bf994562d9a11f63cf4eadecbdc0ecb5a394e54529e8da58a527bc6d85725043786362ab4de6cbc7d80e625ae0a98861aea1c7bf7109c91f66", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04016f831fbe722a5b11ea60d4b63c1bb88616bbb2249f3cffaccaf849791e470447a582090e7add34b5031a7d9aaaf64b96e1da83aec7980b2acbbadaf9a145a8f4d1010126cce70be1a8204e418788993cfd9da4bd26a799d42184386cdb0bfecccbdbd30f93a1e2ac19c95bbd34347e0d2e2b95c30f31ad5958a3926485f61d6bff2903", "wx": "016f831fbe722a5b11ea60d4b63c1bb88616bbb2249f3cffaccaf849791e470447a582090e7add34b5031a7d9aaaf64b96e1da83aec7980b2acbbadaf9a145a8f4d1", "wy": "010126cce70be1a8204e418788993cfd9da4bd26a799d42184386cdb0bfecccbdbd30f93a1e2ac19c95bbd34347e0d2e2b95c30f31ad5958a3926485f61d6bff2903"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004016f831fbe722a5b11ea60d4b63c1bb88616bbb2249f3cffaccaf849791e470447a582090e7add34b5031a7d9aaaf64b96e1da83aec7980b2acbbadaf9a145a8f4d1010126cce70be1a8204e418788993cfd9da4bd26a799d42184386cdb0bfecccbdbd30f93a1e2ac19c95bbd34347e0d2e2b95c30f31ad5958a3926485f61d6bff2903", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBb4MfvnIqWxHqYNS2PBu4hha7siSf\nPP+syvhJeR5HBEelggkOet00tQMafZqq9kuW4dqDrseYCyrLutr5oUWo9NEBASbM\n5wvhqCBOQYeImTz9naS9JqeZ1CGEOGzbC/7My9vTD5Oh4qwZyVu9NDR+DS4rlcMP\nMa1ZWKOSZIX2HWv/KQM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe024115837645583a37a7a665f983c5e347f65dca47647aa80fd2498a791d44d9b2850a151a6e86fce7d7bb814e724ff11b9ef726bf36c6e7548c37f82a24902876ee19", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401d8c20c1248ac77878a328190f9524a770f0c99f0d689adac92da66e097fce8f55d2311c9eca17a01a1140ac4237caa7e99c4ef12b16ed3945d40479a2d74afc5d80015d0590ac9c19d4a1ebd458990ce7601b6fed26cc031ea5b39f770a7111044db2ebc3d6fa90aa171155ce9376e215a932be0897e09876af544467de7d5f03124c6", "wx": "01d8c20c1248ac77878a328190f9524a770f0c99f0d689adac92da66e097fce8f55d2311c9eca17a01a1140ac4237caa7e99c4ef12b16ed3945d40479a2d74afc5d8", "wy": "15d0590ac9c19d4a1ebd458990ce7601b6fed26cc031ea5b39f770a7111044db2ebc3d6fa90aa171155ce9376e215a932be0897e09876af544467de7d5f03124c6"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401d8c20c1248ac77878a328190f9524a770f0c99f0d689adac92da66e097fce8f55d2311c9eca17a01a1140ac4237caa7e99c4ef12b16ed3945d40479a2d74afc5d80015d0590ac9c19d4a1ebd458990ce7601b6fed26cc031ea5b39f770a7111044db2ebc3d6fa90aa171155ce9376e215a932be0897e09876af544467de7d5f03124c6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQB2MIMEkisd4eKMoGQ+VJKdw8MmfDW\nia2sktpm4Jf86PVdIxHJ7KF6AaEUCsQjfKp+mcTvErFu05RdQEeaLXSvxdgAFdBZ\nCsnBnUoevUWJkM52Abb+0mzAMepbOfdwpxEQRNsuvD1vqQqhcRVc6TduIVqTK+CJ\nfgmHavVERn3n1fAxJMY=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "small r and s", "msg": "************", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04011f1481b54fd859c3d4ba500d2d5d222301c4907479d12bcdf1363007c5632ec51fdd6569ad174f4f9648555e24cc2a47c0753214ffe41fddf874a9a2141c4134b3012d283c35b7127fd1d5c5d0a73127f2c1ab9056205cca574ba075189a2da2eb4750f19a97b96f87a58aef96c38f8dd54104a33988b5b988747112596279fc7a612c", "wx": "011f1481b54fd859c3d4ba500d2d5d222301c4907479d12bcdf1363007c5632ec51fdd6569ad174f4f9648555e24cc2a47c0753214ffe41fddf874a9a2141c4134b3", "wy": "012d283c35b7127fd1d5c5d0a73127f2c1ab9056205cca574ba075189a2da2eb4750f19a97b96f87a58aef96c38f8dd54104a33988b5b988747112596279fc7a612c"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004011f1481b54fd859c3d4ba500d2d5d222301c4907479d12bcdf1363007c5632ec51fdd6569ad174f4f9648555e24cc2a47c0753214ffe41fddf874a9a2141c4134b3012d283c35b7127fd1d5c5d0a73127f2c1ab9056205cca574ba075189a2da2eb4750f19a97b96f87a58aef96c38f8dd54104a33988b5b988747112596279fc7a612c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBHxSBtU/YWcPUulANLV0iIwHEkHR5\n0SvN8TYwB8VjLsUf3WVprRdPT5ZIVV4kzCpHwHUyFP/kH934dKmiFBxBNLMBLSg8\nNbcSf9HVxdCnMSfywauQViBcyldLoHUYmi2i60dQ8ZqXuW+HpYrvlsOPjdVBBKM5\niLW5iHRxElliefx6YSw=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "small r and s", "msg": "************", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04007130346b01cc8256b06c469e974c5d1a73218c7fc76abc73fe49f3c7141d9b3528c856975468cb43ee45461a2ace7ffeec29624d1580be7e75a10dc8fbbded6e9801619ff433bcc4c6379306591c3f8fede365e7ad9d4c0cb566aacdf82c92a504f7fbf77a54ce4e7ba6803b9d421230d4f3ae4f2508508e86b182a009cd4d4b861339", "wx": "7130346b01cc8256b06c469e974c5d1a73218c7fc76abc73fe49f3c7141d9b3528c856975468cb43ee45461a2ace7ffeec29624d1580be7e75a10dc8fbbded6e98", "wy": "01619ff433bcc4c6379306591c3f8fede365e7ad9d4c0cb566aacdf82c92a504f7fbf77a54ce4e7ba6803b9d421230d4f3ae4f2508508e86b182a009cd4d4b861339"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004007130346b01cc8256b06c469e974c5d1a73218c7fc76abc73fe49f3c7141d9b3528c856975468cb43ee45461a2ace7ffeec29624d1580be7e75a10dc8fbbded6e9801619ff433bcc4c6379306591c3f8fede365e7ad9d4c0cb566aacdf82c92a504f7fbf77a54ce4e7ba6803b9d421230d4f3ae4f2508508e86b182a009cd4d4b861339", "keyPem": "-----BEGIN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAcTA0awHMglawbEael0xdGnMhjH/H\narxz/knzxxQdmzUoyFaXVGjLQ+5FRhoqzn/+7CliTRWAvn51oQ3I+73tbpgBYZ/0\nM7zExjeTBlkcP4/t42XnrZ1MDLVmqs34LJKlBPf793pUzk57poA7nUISMNTzrk8l\nCFCOhrGCoAnNTUuGEzk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "small r and s", "msg": "************", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 365, "comment": "r is larger than n", "msg": "************", "sig": "3047024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640a020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04018f8b623f8dbe41e9e93e2c1ae7b0ca2c3cae6995a95f8fabe2f3d7b7324e754d5be9cda985a17a41b7b26b757d5694afeb46e60863365ca8d81fe007dc405898e9003c3237004541ddaaaaefad0adbf3517e7ba8a975fc9b6f124f07559d36b042c844540884485052fea349a69cdedbd93c99c72f4d6cb03b59cb594add19dca4b263", "wx": "018f8b623f8dbe41e9e93e2c1ae7b0ca2c3cae6995a95f8fabe2f3d7b7324e754d5be9cda985a17a41b7b26b757d5694afeb46e60863365ca8d81fe007dc405898e9", "wy": "3c3237004541ddaaaaefad0adbf3517e7ba8a975fc9b6f124f07559d36b042c844540884485052fea349a69cdedbd93c99c72f4d6cb03b59cb594add19dca4b263"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004018f8b623f8dbe41e9e93e2c1ae7b0ca2c3cae6995a95f8fabe2f3d7b7324e754d5be9cda985a17a41b7b26b757d5694afeb46e60863365ca8d81fe007dc405898e9003c3237004541ddaaaaefad0adbf3517e7ba8a975fc9b6f124f07559d36b042c844540884485052fea349a69cdedbd93c99c72f4d6cb03b59cb594add19dca4b263", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBj4tiP42+QenpPiwa57DKLDyuaZWp\nX4+r4vPXtzJOdU1b6c2phaF6Qbeya3V9VpSv60bmCGM2XKjYH+AH3EBYmOkAPDI3\nAEVB3aqq760K2/NRfnuoqXX8m28STwdVnTawQshEVAiESFBS/qNJppze29k8mccv\nTWywO1nLWUrdGdyksmM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "s is larger than n", "msg": "************", "sig": "3047020101024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e914b3a90", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400a72460b47647cf18215020a05ddd57b6b7a1fdb59589ba54572596abafd9413a3645622b78de90f02ed27e6eaa66b88e22d1edf86dffc00945c453bf55d9561be8005655d43e58d4dddb71176716492177bb31c673e9ad1d6a68c00f7d7267002d00e3f203cd4e5d882fb4f6a4da4cf1653934940d8adb162fdc6d3863cfbd7da1bea1", "wx": "00a72460b47647cf18215020a05ddd57b6b7a1fdb59589ba54572596abafd9413a3645622b78de90f02ed27e6eaa66b88e22d1edf86dffc00945c453bf55d9561be8", "wy": "5655d43e58d4dddb71176716492177bb31c673e9ad1d6a68c00f7d7267002d00e3f203cd4e5d882fb4f6a4da4cf1653934940d8adb162fdc6d3863cfbd7da1bea1"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400a72460b47647cf18215020a05ddd57b6b7a1fdb59589ba54572596abafd9413a3645622b78de90f02ed27e6eaa66b88e22d1edf86dffc00945c453bf55d9561be8005655d43e58d4dddb71176716492177bb31c673e9ad1d6a68c00f7d7267002d00e3f203cd4e5d882fb4f6a4da4cf1653934940d8adb162fdc6d3863cfbd7da1bea1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQApyRgtHZHzxghUCCgXd1Xtreh/bWV\nibpUVyWWq6/ZQTo2RWIreN6Q8C7Sfm6qZriOItHt+G3/wAlFxFO/VdlWG+gAVlXU\nPljU3dtxF2cWSSF3uzHGc+mtHWpowA99cmcALQDj8gPNTl2IL7T2pNpM8WU5NJQN\nitsWL9xtOGPPvX2hvqE=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "small r and s^-1", "msg": "************", "sig": "304802020100024201efdfbf7efdfbf7efdfbf7efdfbf7efdfbf7efdfbf7efdfbf7efdfbf7efdfbf7ef87b4de1fc92dd757639408a50bee10764e326fdd2fa308dfde3e5243fdf4ac5ac", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04015894e001912487ce160912d6e8ac66a8a7449fe56d06c02e1e52a04029e74cecf0655d7257cc73f68f5929ef5a6cb3b0e689cbb12df439d0932dc990696957a0cd007f2be8ac8168dd28c429e45f4f3127f17e1b1f081be900e1ca8c72e354d7e11198cc73d6d6f9542170441be58e4eb0ff34356896d8dedda4bd7596b6b2af2040a3", "wx": "015894e001912487ce160912d6e8ac66a8a7449fe56d06c02e1e52a04029e74cecf0655d7257cc73f68f5929ef5a6cb3b0e689cbb12df439d0932dc990696957a0cd", "wy": "7f2be8ac8168dd28c429e45f4f3127f17e1b1f081be900e1ca8c72e354d7e11198cc73d6d6f9542170441be58e4eb0ff34356896d8dedda4bd7596b6b2af2040a3"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004015894e001912487ce160912d6e8ac66a8a7449fe56d06c02e1e52a04029e74cecf0655d7257cc73f68f5929ef5a6cb3b0e689cbb12df439d0932dc990696957a0cd007f2be8ac8168dd28c429e45f4f3127f17e1b1f081be900e1ca8c72e354d7e11198cc73d6d6f9542170441be58e4eb0ff34356896d8dedda4bd7596b6b2af2040a3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBWJTgAZEkh84WCRLW6KxmqKdEn+Vt\nBsAuHlKgQCnnTOzwZV1yV8xz9o9ZKe9abLOw5onLsS30OdCTLcmQaWlXoM0Afyvo\nrIFo3SjEKeRfTzEn8X4bHwgb6QDhyoxy41TX4RGYzHPW1vlUIXBEG+WOTrD/NDVo\nltje3aS9dZa2sq8gQKM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "smallish r and s^-1", "msg": "************", "sig": "304d02072d9b4d347952cd02420100508d073413de829275e76509fd81cff49adf4c80ed2ddd4a7937d1d918796878fec24cc46570982c3fb8f5e92ccdcb3e677f07e9bd0db0b84814be1c7949b0de", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401a6518eb0c8bc48d8c6dd8deafca4164a6c45cff282fb321365e3082eb49275447f0efde82cd05125524f31a3a3e0b844a489365f33c46b3f86833f4e61ae15fb6c002486c01f530c53fba83b76f0d74c6afe83acfd567b17fb13e13d205b28f2a562300e03cc170525eb05b3d9d02f0f69eb9b8551096b67bc2b0a202ef1366d31bb28", "wx": "01a6518eb0c8bc48d8c6dd8deafca4164a6c45cff282fb321365e3082eb49275447f0efde82cd05125524f31a3a3e0b844a489365f33c46b3f86833f4e61ae15fb6c", "wy": "2486c01f530c53fba83b76f0d74c6afe83acfd567b17fb13e13d205b28f2a562300e03cc170525eb05b3d9d02f0f69eb9b8551096b67bc2b0a202ef1366d31bb28"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401a6518eb0c8bc48d8c6dd8deafca4164a6c45cff282fb321365e3082eb49275447f0efde82cd05125524f31a3a3e0b844a489365f33c46b3f86833f4e61ae15fb6c002486c01f530c53fba83b76f0d74c6afe83acfd567b17fb13e13d205b28f2a562300e03cc170525eb05b3d9d02f0f69eb9b8551096b67bc2b0a202ef1366d31bb28", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBplGOsMi8SNjG3Y3q/KQWSmxFz/KC\n+zITZeMILrSSdUR/Dv3oLNBRJVJPMaOj4LhEpIk2XzPEaz+Ggz9OYa4V+2wAJIbA\nH1MMU/uoO3bw10xq/oOs/VZ7F/sT4T0gWyjypWIwDgPMFwUl6wWz2dAvD2nrm4VR\nCWtnvCsKIC7xNm0xuyg=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "100-bit r and small s^-1", "msg": "************", "sig": "3053020d1033e67e37b32b445580bf4eff0242013cc33cc33cc33cc33cc33cc33cc33cc33cc33cc33cc33cc33cc33cc33cc33cc3393f632affd3eaa3c8fb64507bd5996497bd588fb9e3947c097ced7546b57c8998", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400e75c5dc6fb8f88c1090a0c626ad8cd3800ebbf84a3a7a9ca11f3329bb4ba341890310f2c05c25a604a956abb325aa5053eb3af7886e83c6836e96a02660810b6a40050c49bb8c84b748f6aa80d7f69c12315bd77ac4b2b998a5d63af5ac1b1a6b62400017ce6c02cb8e66704d86739bec5c64bbb5e5df782df6ca1ed3c53f13c5b7096", "wx": "00e75c5dc6fb8f88c1090a0c626ad8cd3800ebbf84a3a7a9ca11f3329bb4ba341890310f2c05c25a604a956abb325aa5053eb3af7886e83c6836e96a02660810b6a4", "wy": "50c49bb8c84b748f6aa80d7f69c12315bd77ac4b2b998a5d63af5ac1b1a6b62400017ce6c02cb8e66704d86739bec5c64bbb5e5df782df6ca1ed3c53f13c5b7096"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400e75c5dc6fb8f88c1090a0c626ad8cd3800ebbf84a3a7a9ca11f3329bb4ba341890310f2c05c25a604a956abb325aa5053eb3af7886e83c6836e96a02660810b6a40050c49bb8c84b748f6aa80d7f69c12315bd77ac4b2b998a5d63af5ac1b1a6b62400017ce6c02cb8e66704d86739bec5c64bbb5e5df782df6ca1ed3c53f13c5b7096", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA51xdxvuPiMEJCgxiatjNOADrv4Sj\np6nKEfMym7S6NBiQMQ8sBcJaYEqVarsyWqUFPrOveIboPGg26WoCZggQtqQAUMSb\nuMhLdI9qqA1/acEjFb13rEsrmYpdY69awbGmtiQAAXzmwCy45mcE2Gc5vsXGS7te\nXfeC32yh7TxT8TxbcJY=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "small r and 100 bit s^-1", "msg": "************", "sig": "30480202010002420086ecbf54ab59a4e195f0be1402edd8657bb94618fab50f2fe20fe5ebbc9ff0e491397ed313cc918d438eedb9b5ecb4d9dfa305303505baf25400ed8c20fc3fc47b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400fe436033507264c0f6a8e0bd76035c56dfcbea4660aeb8865e0845a4e9895e8394b3fff53428bb6e047ad4d53edd47fb10a0d5ffa10a09b31ee45f2e5f370b0da90072fdb3001eccec7ed1d32b560f53f00dc7d1d06111e79f98793872e7b15e5ed8ba9e3cf35e9a5521b893485f6b508d6b3524b9f11516240bd2d23efd37de28d720", "wx": "00fe436033507264c0f6a8e0bd76035c56dfcbea4660aeb8865e0845a4e9895e8394b3fff53428bb6e047ad4d53edd47fb10a0d5ffa10a09b31ee45f2e5f370b0da9", "wy": "72fdb3001eccec7ed1d32b560f53f00dc7d1d06111e79f98793872e7b15e5ed8ba9e3cf35e9a5521b893485f6b508d6b3524b9f11516240bd2d23efd37de28d720"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400fe436033507264c0f6a8e0bd76035c56dfcbea4660aeb8865e0845a4e9895e8394b3fff53428bb6e047ad4d53edd47fb10a0d5ffa10a09b31ee45f2e5f370b0da90072fdb3001eccec7ed1d32b560f53f00dc7d1d06111e79f98793872e7b15e5ed8ba9e3cf35e9a5521b893485f6b508d6b3524b9f11516240bd2d23efd37de28d720", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA/kNgM1ByZMD2qOC9dgNcVt/L6kZg\nrriGXghFpOmJXoOUs//1NCi7bgR61NU+3Uf7EKDV/6EKCbMe5F8uXzcLDakAcv2z\nAB7M7H7R0ytWD1PwDcfR0GER55+YeThy57FeXti6njzzXppVIbiTSF9rUI1rNSS5\n8RUWJAvS0j79N94o1yA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "100-bit r and s^-1", "msg": "************", "sig": "3053020d062522bbd3ecbe7c39e93e7c2402420086ecbf54ab59a4e195f0be1402edd8657bb94618fab50f2fe20fe5ebbc9ff0e491397ed313cc918d438eedb9b5ecb4d9dfa305303505baf25400ed8c20fc3fc47b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04000e117d46e922faec0482c89f289c8fca8445e77f456261704c567aa012d655301e4ad9975dea5549cfccc69dbf162a56bad4519cd2c32c169aa41b8df366f3fae800e2d8a21fbb54f84fd5395b648996306c8ba7c2d434179ac999f1d46098867975ca2ed83026b96a7a56caaa0dae8e81901eb0db352abc4a34c4ee6a41b26f33fe3f", "wx": "0e117d46e922faec0482c89f289c8fca8445e77f456261704c567aa012d655301e4ad9975dea5549cfccc69dbf162a56bad4519cd2c32c169aa41b8df366f3fae8", "wy": "00e2d8a21fbb54f84fd5395b648996306c8ba7c2d434179ac999f1d46098867975ca2ed83026b96a7a56caaa0dae8e81901eb0db352abc4a34c4ee6a41b26f33fe3f"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004000e117d46e922faec0482c89f289c8fca8445e77f456261704c567aa012d655301e4ad9975dea5549cfccc69dbf162a56bad4519cd2c32c169aa41b8df366f3fae800e2d8a21fbb54f84fd5395b648996306c8ba7c2d434179ac999f1d46098867975ca2ed83026b96a7a56caaa0dae8e81901eb0db352abc4a34c4ee6a41b26f33fe3f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQADhF9Ruki+uwEgsifKJyPyoRF539F\nYmFwTFZ6oBLWVTAeStmXXepVSc/Mxp2/FipWutRRnNLDLBaapBuN82bz+ugA4tii\nH7tU+E/VOVtkiZYwbIunwtQ0F5rJmfHUYJiGeXXKLtgwJrlqelbKqg2ujoGQHrDb\nNSq8SjTE7mpBsm8z/j8=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "r and s^-1 are close to n", "msg": "************", "sig": "308188024201fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138638a0242015555555555555555555555555555555555555555555555555555555555555555518baf05027f750ef25532ab85fa066e8ad2793125b112da747cf524bf0b7aed5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040128bdac640b4f6fee289691b4947a598690bb9e6ccf3bfd0782f61def532e290a45d7202bd8424119b5774c4ebceb7a746f884a88f096bb322743e94b28e8aa5b10018716996903048af17d152bcdb9a1ff67aa8b148e025b5d4edbd75faf9595712fdcc7cfa483e01f3a6576727bcb35649d482e18b7e4c4b4e41dff4be3de92488f94", "wx": "0128bdac640b4f6fee289691b4947a598690bb9e6ccf3bfd0782f61def532e290a45d7202bd8424119b5774c4ebceb7a746f884a88f096bb322743e94b28e8aa5b10", "wy": "018716996903048af17d152bcdb9a1ff67aa8b148e025b5d4edbd75faf9595712fdcc7cfa483e01f3a6576727bcb35649d482e18b7e4c4b4e41dff4be3de92488f94"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040128bdac640b4f6fee289691b4947a598690bb9e6ccf3bfd0782f61def532e290a45d7202bd8424119b5774c4ebceb7a746f884a88f096bb322743e94b28e8aa5b10018716996903048af17d152bcdb9a1ff67aa8b148e025b5d4edbd75faf9595712fdcc7cfa483e01f3a6576727bcb35649d482e18b7e4c4b4e41dff4be3de92488f94", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBKL2sZAtPb+4olpG0lHpZhpC7nmzP\nO/0HgvYd71MuKQpF1yAr2EJBGbV3TE6863p0b4hKiPCWuzInQ+lLKOiqWxABhxaZ\naQMEivF9FSvNuaH/Z6qLFI4CW11O29dfr5WVcS/cx8+kg+AfOmV2cnvLNWSdSC4Y\nt+TEtOQd/0vj3pJIj5Q=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "s == 1", "msg": "************", "sig": "3047024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad020101", "result": "valid", "flags": []}, {"tcId": 374, "comment": "s == 0", "msg": "************", "sig": "3047024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400438f4f62297e21524d237da20c89842c608e319f1ec5260a79f5ced18519bbfac425a4e72fc19fcdb8225ac86f81e29f30d6f5f3ff32afbf0d44191731179095ed01e9581d305a505fe6b6142b386aa8fa0eb13f87eded898a173f1a09337487f843e7daa7dc24132ee35942645284239bdfc7feca911e76d10ebc50fc71f4c5cc23e6", "wx": "438f4f62297e21524d237da20c89842c608e319f1ec5260a79f5ced18519bbfac425a4e72fc19fcdb8225ac86f81e29f30d6f5f3ff32afbf0d44191731179095ed", "wy": "01e9581d305a505fe6b6142b386aa8fa0eb13f87eded898a173f1a09337487f843e7daa7dc24132ee35942645284239bdfc7feca911e76d10ebc50fc71f4c5cc23e6"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400438f4f62297e21524d237da20c89842c608e319f1ec5260a79f5ced18519bbfac425a4e72fc19fcdb8225ac86f81e29f30d6f5f3ff32afbf0d44191731179095ed01e9581d305a505fe6b6142b386aa8fa0eb13f87eded898a173f1a09337487f843e7daa7dc24132ee35942645284239bdfc7feca911e76d10ebc50fc71f4c5cc23e6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAQ49PYil+IVJNI32iDImELGCOMZ8e\nxSYKefXO0YUZu/rEJaTnL8GfzbgiWshvgeKfMNb18/8yr78NRBkXMReQle0B6Vgd\nMFpQX+a2FCs4aqj6DrE/h+3tiYoXPxoJM3SH+EPn2qfcJBMu41lCZFKEI5vfx/7K\nkR520Q68UPxx9MXMI+Y=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "point at infinity during verify", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd28c343c1df97cb35bfe600a47b84d2e81ddae4dc44ce23d75db7db8f489c3204024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400eef2fd1df18de7e98b0c5b8ad244704c1cc4ab7cb8fa91617dc564e96fffdd91ac15cf0d3b1ee3aa97ef4e1b34f9e33d12ebd96a3b98825244494d5b2d5aaab24101e064096439d4e090db8a9facf5c17eb2ec888a8ddc89b72eb16f059c6e2fb05c1d3cdba1bc0dd06ad322c2597fdcd696acd7f09417d76088d82a439babffb9c7d2", "wx": "00eef2fd1df18de7e98b0c5b8ad244704c1cc4ab7cb8fa91617dc564e96fffdd91ac15cf0d3b1ee3aa97ef4e1b34f9e33d12ebd96a3b98825244494d5b2d5aaab241", "wy": "01e064096439d4e090db8a9facf5c17eb2ec888a8ddc89b72eb16f059c6e2fb05c1d3cdba1bc0dd06ad322c2597fdcd696acd7f09417d76088d82a439babffb9c7d2"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400eef2fd1df18de7e98b0c5b8ad244704c1cc4ab7cb8fa91617dc564e96fffdd91ac15cf0d3b1ee3aa97ef4e1b34f9e33d12ebd96a3b98825244494d5b2d5aaab24101e064096439d4e090db8a9facf5c17eb2ec888a8ddc89b72eb16f059c6e2fb05c1d3cdba1bc0dd06ad322c2597fdcd696acd7f09417d76088d82a439babffb9c7d2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA7vL9HfGN5+mLDFuK0kRwTBzEq3y4\n+pFhfcVk6W//3ZGsFc8NOx7jqpfvThs0+eM9EuvZajuYglJESU1bLVqqskEB4GQJ\nZDnU4JDbip+s9cF+suyIio3cibcusW8FnG4vsFwdPNuhvA3QatMiwll/3NaWrNfw\nlBfXYIjYKkObq/+5x9I=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "edge case for signature malleability", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd28c343c1df97cb35bfe600a47b84d2e81ddae4dc44ce23d75db7db8f489c3206024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd28c343c1df97cb35bfe600a47b84d2e81ddae4dc44ce23d75db7db8f489c3204", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040072aa33a0c180e18b1b0f86579b0109d629a865ff67c68e37c81bcfe3d14401c58b995a90adec3133de53eca49ffca323f1e067546c27b9d7e559fa4c605f20b2b901c2a21f93a82d61c279a0f27875b735a7b702150478c044d4b4c1f6121a4c0b37dd9c75031080798653c1dfc9a66aaeb94b4fcab22113222050a6fe6121bfdf1420", "wx": "72aa33a0c180e18b1b0f86579b0109d629a865ff67c68e37c81bcfe3d14401c58b995a90adec3133de53eca49ffca323f1e067546c27b9d7e559fa4c605f20b2b9", "wy": "01c2a21f93a82d61c279a0f27875b735a7b702150478c044d4b4c1f6121a4c0b37dd9c75031080798653c1dfc9a66aaeb94b4fcab22113222050a6fe6121bfdf1420"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040072aa33a0c180e18b1b0f86579b0109d629a865ff67c68e37c81bcfe3d14401c58b995a90adec3133de53eca49ffca323f1e067546c27b9d7e559fa4c605f20b2b901c2a21f93a82d61c279a0f27875b735a7b702150478c044d4b4c1f6121a4c0b37dd9c75031080798653c1dfc9a66aaeb94b4fcab22113222050a6fe6121bfdf1420", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAcqozoMGA4YsbD4ZXmwEJ1imoZf9n\nxo43yBvP49FEAcWLmVqQrewxM95T7KSf/KMj8eBnVGwnudflWfpMYF8gsrkBwqIf\nk6gtYcJ5oPJ4dbc1p7cCFQR4wETUtMH2EhpMCzfdnHUDEIB5hlPB38mmaq65S0/K\nsiETIiBQpv5hIb/fFCA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "edge case for signature malleability", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd28c343c1df97cb35bfe600a47b84d2e81ddae4dc44ce23d75db7db8f489c3206024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd28c343c1df97cb35bfe600a47b84d2e81ddae4dc44ce23d75db7db8f489c3205", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400cf1dff74307192b773d12b275076ef04b1807e3b774bf2e28dffc1aa5eca395ca525a9dc6cd2d4ae7e93cbf981e074040ffd2d86fc6b82038b850537143847e4ce015a9af9c2f60552ad141acc2ea27ab73a1772078616c3017a441f16e417d93d88030d5351fbf1e6ae9b0c243636d3fb74de920b292ec550f232eb90cdbcca174d20", "wx": "00cf1dff74307192b773d12b275076ef04b1807e3b774bf2e28dffc1aa5eca395ca525a9dc6cd2d4ae7e93cbf981e074040ffd2d86fc6b82038b850537143847e4ce", "wy": "015a9af9c2f60552ad141acc2ea27ab73a1772078616c3017a441f16e417d93d88030d5351fbf1e6ae9b0c243636d3fb74de920b292ec550f232eb90cdbcca174d20"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400cf1dff74307192b773d12b275076ef04b1807e3b774bf2e28dffc1aa5eca395ca525a9dc6cd2d4ae7e93cbf981e074040ffd2d86fc6b82038b850537143847e4ce015a9af9c2f60552ad141acc2ea27ab73a1772078616c3017a441f16e417d93d88030d5351fbf1e6ae9b0c243636d3fb74de920b292ec550f232eb90cdbcca174d20", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAzx3/dDBxkrdz0SsnUHbvBLGAfjt3\nS/Lijf/Bql7KOVylJancbNLUrn6Ty/mB4HQED/0thvxrggOLhQU3FDhH5M4BWpr5\nwvYFUq0UGswuonq3OhdyB4YWwwF6RB8W5BfZPYgDDVNR+/HmrpsMJDY20/t03pIL\nKS7FUPIy65DNvMoXTSA=\n-----<PERSON><PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "u1 == 1", "msg": "************", "sig": "308186024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad0240342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3a56f87db98089d208c89e902bb50ed289995ee7ccf6d6e6b1cec4aaf832d3734", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401d0435963afc96eac099639a3e23383462e505c83955f29743ed244e6b76bb6fb080118558e63a6c632d3be9ba173dfe46fa7d9c761ce619055ad1c4321e142e8a6018065482a1e845183854103d7c97a37f31aa73703aa1eafc5a774d52962041c35b25ef4e98a9c5b48bf0c91bf16df0c2f2d1685aa4b414383e0654a9e43c0c60458", "wx": "01d0435963afc96eac099639a3e23383462e505c83955f29743ed244e6b76bb6fb080118558e63a6c632d3be9ba173dfe46fa7d9c761ce619055ad1c4321e142e8a6", "wy": "018065482a1e845183854103d7c97a37f31aa73703aa1eafc5a774d52962041c35b25ef4e98a9c5b48bf0c91bf16df0c2f2d1685aa4b414383e0654a9e43c0c60458"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401d0435963afc96eac099639a3e23383462e505c83955f29743ed244e6b76bb6fb080118558e63a6c632d3be9ba173dfe46fa7d9c761ce619055ad1c4321e142e8a6018065482a1e845183854103d7c97a37f31aa73703aa1eafc5a774d52962041c35b25ef4e98a9c5b48bf0c91bf16df0c2f2d1685aa4b414383e0654a9e43c0c60458", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQB0ENZY6/JbqwJljmj4jODRi5QXIOV\nXyl0PtJE5rdrtvsIARhVjmOmxjLTvpuhc9/kb6fZx2HOYZBVrRxDIeFC6KYBgGVI\nKh6EUYOFQQPXyXo38xqnNwOqHq/Fp3TVKWIEHDWyXvTpipxbSL8Mkb8W3wwvLRaF\nqktBQ4PgZUqeQ8DGBFg=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "u1 == n - 1", "msg": "************", "sig": "308188024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad024201ffcbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede3e402abcabb80e36ac16ffa82726f94af34218463bb8b8a7a21fdb3bba2ed9439e836c6f0e0b2cd5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04001af6a36318d047a1ed36849109c3a3661235e693dc0a3bbd76ddfeb7cdb13e79e7f36eaf6e412bdd44c4d0f9914b4696137212127913b9c0b3556f808f547108130030246a78fc9d73335ce9befc2be236c32e947efde654481bcb7763656bd9c250c2e1f73511811efdf2c01332cffe2498b8b71cf50126fa0f8b34c8ed2d9631894e", "wx": "1af6a36318d047a1ed36849109c3a3661235e693dc0a3bbd76ddfeb7cdb13e79e7f36eaf6e412bdd44c4d0f9914b4696137212127913b9c0b3556f808f54710813", "wy": "30246a78fc9d73335ce9befc2be236c32e947efde654481bcb7763656bd9c250c2e1f73511811efdf2c01332cffe2498b8b71cf50126fa0f8b34c8ed2d9631894e"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004001af6a36318d047a1ed36849109c3a3661235e693dc0a3bbd76ddfeb7cdb13e79e7f36eaf6e412bdd44c4d0f9914b4696137212127913b9c0b3556f808f547108130030246a78fc9d73335ce9befc2be236c32e947efde654481bcb7763656bd9c250c2e1f73511811efdf2c01332cffe2498b8b71cf50126fa0f8b34c8ed2d9631894e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAGvajYxjQR6HtNoSRCcOjZhI15pPc\nCju9dt3+t82xPnnn826vbkEr3UTE0PmRS0aWE3ISEnkTucCzVW+Aj1RxCBMAMCRq\nePydczNc6b78K+I2wy6Ufv3mVEgby3djZWvZwlDC4fc1EYEe/fLAEzLP/iSYuLcc\n9QEm+g+LNMjtLZYxiU4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "u2 == 1", "msg": "************", "sig": "308188024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401cfb15a18fa029408a4dc7e1894df30e263fc428a872d8b1cdc222c4bbb31ebfe7a24897e514c1c51ef4d7a339f7638c2859a00d7d0390ca002a6ed0ba7424fb6bb01ed010d62df4069c1020e0be488768879f1e6c67a0136889080c190d0d6930f2c4097d75f89dea3659785c374a91588dd6cfa2f9e296e7084ad60c0b4041a5476b4", "wx": "01cfb15a18fa029408a4dc7e1894df30e263fc428a872d8b1cdc222c4bbb31ebfe7a24897e514c1c51ef4d7a339f7638c2859a00d7d0390ca002a6ed0ba7424fb6bb", "wy": "01ed010d62df4069c1020e0be488768879f1e6c67a0136889080c190d0d6930f2c4097d75f89dea3659785c374a91588dd6cfa2f9e296e7084ad60c0b4041a5476b4"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401cfb15a18fa029408a4dc7e1894df30e263fc428a872d8b1cdc222c4bbb31ebfe7a24897e514c1c51ef4d7a339f7638c2859a00d7d0390ca002a6ed0ba7424fb6bb01ed010d62df4069c1020e0be488768879f1e6c67a0136889080c190d0d6930f2c4097d75f89dea3659785c374a91588dd6cfa2f9e296e7084ad60c0b4041a5476b4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBz7FaGPoClAik3H4YlN8w4mP8QoqH\nLYsc3CIsS7sx6/56JIl+UUwcUe9NejOfdjjChZoA19A5DKACpu0Lp0JPtrsB7QEN\nYt9AacECDgvkiHaIefHmxnoBNoiQgMGQ0NaTDyxAl9dfid6jZZeFw3SpFYjdbPov\nnilucIStYMC0BBpUdrQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "u2 == n - 1", "msg": "************", "sig": "308188024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad0242015555555555555555555555555555555555555555555555555555555555555555518baf05027f750ef25532ab85fa066e8ad2793125b112da747cf524bf0b7aed5c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400496260a48fd656a3acbd4af6876d5864ea2c8b317f0448c5ae7c74160d64f303aba83cca1160860028c2bf1508b5af1109de5f740dd9e287af8b7b32ac1f2ea90500b08409969c1e066f880742b0606469dce29c07ab5c84bd480f57fa701d868fa399a49a4ba1fe4ecee198a648746273129ff7d02cff2abee672f1775d3ae0ec75ba", "wx": "496260a48fd656a3acbd4af6876d5864ea2c8b317f0448c5ae7c74160d64f303aba83cca1160860028c2bf1508b5af1109de5f740dd9e287af8b7b32ac1f2ea905", "wy": "00b08409969c1e066f880742b0606469dce29c07ab5c84bd480f57fa701d868fa399a49a4ba1fe4ecee198a648746273129ff7d02cff2abee672f1775d3ae0ec75ba"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400496260a48fd656a3acbd4af6876d5864ea2c8b317f0448c5ae7c74160d64f303aba83cca1160860028c2bf1508b5af1109de5f740dd9e287af8b7b32ac1f2ea90500b08409969c1e066f880742b0606469dce29c07ab5c84bd480f57fa701d868fa399a49a4ba1fe4ecee198a648746273129ff7d02cff2abee672f1775d3ae0ec75ba", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQASWJgpI/WVqOsvUr2h21YZOosizF/\nBEjFrnx0Fg1k8wOrqDzKEWCGACjCvxUIta8RCd5fdA3Z4oevi3syrB8uqQUAsIQJ\nlpweBm+IB0KwYGRp3OKcB6tchL1ID1f6cB2Gj6OZpJpLof5OzuGYpkh0YnMSn/fQ\nLP8qvuZy8XddOuDsdbo=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "edge case for u1", "msg": "************", "sig": "308186024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024011648f7c5e213698d5d89a66b913bc4e38b721f642a0cb0b40954716716d50968c7a829e8802df0ad9834dab93c5a462dddca4d445247a23b44ec38fd66467bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400d522117fe95568bf988ae0fbaf5be3d46ef750361f16189becc63283e6939b36869556c7ebd90272dc4f0bd054f8505c3d01dcdccc3f6538830d9c5fca9fc23b0701c7a8c0beba04b4d62b01095a220fbb6878c91cd2a56eb7bba569a5fc0b42964117ebd4faad3e52086d505aad8b9f0096e9d67f51d505aea87cf61ed33a5efab4ef", "wx": "00d522117fe95568bf988ae0fbaf5be3d46ef750361f16189becc63283e6939b36869556c7ebd90272dc4f0bd054f8505c3d01dcdccc3f6538830d9c5fca9fc23b07", "wy": "01c7a8c0beba04b4d62b01095a220fbb6878c91cd2a56eb7bba569a5fc0b42964117ebd4faad3e52086d505aad8b9f0096e9d67f51d505aea87cf61ed33a5efab4ef"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400d522117fe95568bf988ae0fbaf5be3d46ef750361f16189becc63283e6939b36869556c7ebd90272dc4f0bd054f8505c3d01dcdccc3f6538830d9c5fca9fc23b0701c7a8c0beba04b4d62b01095a220fbb6878c91cd2a56eb7bba569a5fc0b42964117ebd4faad3e52086d505aad8b9f0096e9d67f51d505aea87cf61ed33a5efab4ef", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA1SIRf+lVaL+YiuD7r1vj1G73UDYf\nFhib7MYyg+aTmzaGlVbH69kCctxPC9BU+FBcPQHc3Mw/ZTiDDZxfyp/COwcBx6jA\nvroEtNYrAQlaIg+7aHjJHNKlbre7pWml/AtClkEX69T6rT5SCG1QWq2LnwCW6dZ/\nUdUFrqh89h7TOl76tO8=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "edge case for u1", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0241426fb6fd3c671a0f7c7d2c3cac25b966e6ac0ea9eab99d07706245e9992d1d12698fe266c59cebc214d545cf57aca1d3bb80cf3946602712411941191134201a9a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04003293db60a2a4b219a531caaf860169f52d5ef06d2f3c185fa4d2f6e3e34bd67926948f8397d2b4a98c06ef904f184599dea82da1bb383b9536ba4717849696a3fc00116bbc57ae85d57a0675eb7fae131afabbca5fd0abfc246bb0373d006c797ed4fcb27ead00574619c3c5622ce123d4cf670fbde3809b8fb20392a03a20c614894f", "wx": "3293db60a2a4b219a531caaf860169f52d5ef06d2f3c185fa4d2f6e3e34bd67926948f8397d2b4a98c06ef904f184599dea82da1bb383b9536ba4717849696a3fc", "wy": "116bbc57ae85d57a0675eb7fae131afabbca5fd0abfc246bb0373d006c797ed4fcb27ead00574619c3c5622ce123d4cf670fbde3809b8fb20392a03a20c614894f"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004003293db60a2a4b219a531caaf860169f52d5ef06d2f3c185fa4d2f6e3e34bd67926948f8397d2b4a98c06ef904f184599dea82da1bb383b9536ba4717849696a3fc00116bbc57ae85d57a0675eb7fae131afabbca5fd0abfc246bb0373d006c797ed4fcb27ead00574619c3c5622ce123d4cf670fbde3809b8fb20392a03a20c614894f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAMpPbYKKkshmlMcqvhgFp9S1e8G0v\nPBhfpNL24+NL1nkmlI+Dl9K0qYwG75BPGEWZ3qgtobs4O5U2ukcXhJaWo/wAEWu8\nV66F1XoGdet/rhMa+rvKX9Cr/CRrsDc9AGx5ftT8sn6tAFdGGcPFYizhI9TPZw+9\n44Cbj7IDkqA6IMYUiU8=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0242012f3be6555b95d6a3787dca53f1687f9f23cb121b235cf654c7e1631f52dce04eb8e134c875b7bcd817309bbb455457b9a8c96ac395f3828cce52a0d73baa292d7f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400140cf342e8957593fa7ecfd1cf9530574361364e2bd39df5fac1073e938fcd8b87535f42b230515189204f80fc2c16b40c9bbf252dcb8d77fafa81242e929015c301cc5e15c329aedefbd8accb680d12d76ceb74fdaa70a61aeb79cd2211f2baaf85aef7f4099af081aca2a18b5126df36a02728a50d6bb6904ebd3a0591b56206ce9c", "wx": "140cf342e8957593fa7ecfd1cf9530574361364e2bd39df5fac1073e938fcd8b87535f42b230515189204f80fc2c16b40c9bbf252dcb8d77fafa81242e929015c3", "wy": "01cc5e15c329aedefbd8accb680d12d76ceb74fdaa70a61aeb79cd2211f2baaf85aef7f4099af081aca2a18b5126df36a02728a50d6bb6904ebd3a0591b56206ce9c"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400140cf342e8957593fa7ecfd1cf9530574361364e2bd39df5fac1073e938fcd8b87535f42b230515189204f80fc2c16b40c9bbf252dcb8d77fafa81242e929015c301cc5e15c329aedefbd8accb680d12d76ceb74fdaa70a61aeb79cd2211f2baaf85aef7f4099af081aca2a18b5126df36a02728a50d6bb6904ebd3a0591b56206ce9c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAFAzzQuiVdZP6fs/Rz5UwV0NhNk4r\n0531+sEHPpOPzYuHU19CsjBRUYkgT4D8LBa0DJu/JS3LjXf6+oEkLpKQFcMBzF4V\nwymu3vvYrMtoDRLXbOt0/apwphrrec0iEfK6r4Wu9/QJmvCBrKKhi1Em3zagJyil\nDWu2kE69OgWRtWIGzpw=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024200d0c7db4562c9a527ffcbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9edbed2712adcc0e3bf3c0037a7bee1551669c288188cdf5f66be630de62069d0cd6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400f332fb45734adce50c95527c0ad83fe0965addb1a188a4578737037982b435d175c9e520793e7e8d832b1a2eb3aea271111a4b2b87ce9d1d0cf0eeb7e45a51241b01aabda82f880782f9d76480834b12d60c8e5e1c90ef75242d815eac13425cf07ee77354ff707098bb7830444beff8edfb4812c2961279b412e5078ef147db4d490d", "wx": "00f332fb45734adce50c95527c0ad83fe0965addb1a188a4578737037982b435d175c9e520793e7e8d832b1a2eb3aea271111a4b2b87ce9d1d0cf0eeb7e45a51241b", "wy": "01aabda82f880782f9d76480834b12d60c8e5e1c90ef75242d815eac13425cf07ee77354ff707098bb7830444beff8edfb4812c2961279b412e5078ef147db4d490d"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400f332fb45734adce50c95527c0ad83fe0965addb1a188a4578737037982b435d175c9e520793e7e8d832b1a2eb3aea271111a4b2b87ce9d1d0cf0eeb7e45a51241b01aabda82f880782f9d76480834b12d60c8e5e1c90ef75242d815eac13425cf07ee77354ff707098bb7830444beff8edfb4812c2961279b412e5078ef147db4d490d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA8zL7RXNK3OUMlVJ8Ctg/4JZa3bGh\niKRXhzcDeYK0NdF1yeUgeT5+jYMrGi6zrqJxERpLK4fOnR0M8O635FpRJBsBqr2o\nL4gHgvnXZICDSxLWDI5eHJDvdSQtgV6sE0Jc8H7nc1T/cHCYu3gwREvv+O37SBLC\nlhJ5tBLlB47xR9tNSQ0=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02417db4562c9a527ffcbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede3e29d92d74fb34ffabc8ad75ce5cc26bd5d0436a30dadeb208853218f027af0cd4a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401dfe31538bfdeb190ab46fc8836c0876897e956d40e6bc3b2c8afaf979f52b5c5d5bee0d2ef386ab0e59c8278b91be12ce437dd0a9dc1f9ac0807cae52fda502669016ad42dc451c2cad7d5b840d151de6cceb158f3755f76d496b959400f7b7b4779c167c4c7adf96cf53afc4dcc6f0e9f6012dc484c2014d1e82f09dc2be96ab1dac7", "wx": "01dfe31538bfdeb190ab46fc8836c0876897e956d40e6bc3b2c8afaf979f52b5c5d5bee0d2ef386ab0e59c8278b91be12ce437dd0a9dc1f9ac0807cae52fda502669", "wy": "016ad42dc451c2cad7d5b840d151de6cceb158f3755f76d496b959400f7b7b4779c167c4c7adf96cf53afc4dcc6f0e9f6012dc484c2014d1e82f09dc2be96ab1dac7"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401dfe31538bfdeb190ab46fc8836c0876897e956d40e6bc3b2c8afaf979f52b5c5d5bee0d2ef386ab0e59c8278b91be12ce437dd0a9dc1f9ac0807cae52fda502669016ad42dc451c2cad7d5b840d151de6cceb158f3755f76d496b959400f7b7b4779c167c4c7adf96cf53afc4dcc6f0e9f6012dc484c2014d1e82f09dc2be96ab1dac7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQB3+MVOL/esZCrRvyINsCHaJfpVtQO\na8OyyK+vl59StcXVvuDS7zhqsOWcgni5G+Es5DfdCp3B+awIB8rlL9pQJmkBatQt\nxFHCytfVuEDRUd5szrFY83VfdtSWuVlAD3t7R3nBZ8THrfls9Tr8TcxvDp9gEtxI\nTCAU0egvCdwr6Wqx2sc=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024200fb68ac5934a4fff97a4a315cb38b86afcec6197a989962aabb5343a703b3dbc7c53b25ae9f669ff57915aeb9cb984d7aba086d461b5bd64110a6431e04f5e19a94", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400e0e7553de34d89d37da9ae8eea572beb2a20445bfee82d145fae50d5d6fabc37895c47ba09bfb4025ae961e58155ea868c343431fc939161a10192f910385082a501c8c38c30dce71d50844617c42263df60e29694a702b315be202cf5282adf8170b8a94ea78aa9241db17efdb8a2539c2b71da884211b2b4e45b49ab9192a720c734", "wx": "00e0e7553de34d89d37da9ae8eea572beb2a20445bfee82d145fae50d5d6fabc37895c47ba09bfb4025ae961e58155ea868c343431fc939161a10192f910385082a5", "wy": "01c8c38c30dce71d50844617c42263df60e29694a702b315be202cf5282adf8170b8a94ea78aa9241db17efdb8a2539c2b71da884211b2b4e45b49ab9192a720c734"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400e0e7553de34d89d37da9ae8eea572beb2a20445bfee82d145fae50d5d6fabc37895c47ba09bfb4025ae961e58155ea868c343431fc939161a10192f910385082a501c8c38c30dce71d50844617c42263df60e29694a702b315be202cf5282adf8170b8a94ea78aa9241db17efdb8a2539c2b71da884211b2b4e45b49ab9192a720c734", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA4OdVPeNNidN9qa6O6lcr6yogRFv+\n6C0UX65Q1db6vDeJXEe6Cb+0AlrpYeWBVeqGjDQ0MfyTkWGhAZL5EDhQgqUByMOM\nMNznHVCERhfEImPfYOKWlKcCsxW+ICz1KCrfgXC4qU6niqkkHbF+/biiU5wrcdqI\nQhGytORbSauRkqcgxzQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02420162c9a527ffcbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede3e402ab8bbd93e12de2778bb0e38806ea1c2db3bbee7c3b06e36dfb0c192cd9a8e395d4e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401968ec94f16ecd9d5da1eda6db099b3b79ea1e08d39e7748637cb13c96836f64e0f3227a2ec7563ef58b27bbf18807a6e39cbb8092244ff3f4a3ee9297935a2fa1c011640f819fe9f6911f0c8a54f972e9444f8df518fa778a0978bc6e64e1d43b24ebb03b4ece0099a125115ecff3279b2cdca15d6e4e7d00f5b1c9db626a0196634de", "wx": "01968ec94f16ecd9d5da1eda6db099b3b79ea1e08d39e7748637cb13c96836f64e0f3227a2ec7563ef58b27bbf18807a6e39cbb8092244ff3f4a3ee9297935a2fa1c", "wy": "011640f819fe9f6911f0c8a54f972e9444f8df518fa778a0978bc6e64e1d43b24ebb03b4ece0099a125115ecff3279b2cdca15d6e4e7d00f5b1c9db626a0196634de"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401968ec94f16ecd9d5da1eda6db099b3b79ea1e08d39e7748637cb13c96836f64e0f3227a2ec7563ef58b27bbf18807a6e39cbb8092244ff3f4a3ee9297935a2fa1c011640f819fe9f6911f0c8a54f972e9444f8df518fa778a0978bc6e64e1d43b24ebb03b4ece0099a125115ecff3279b2cdca15d6e4e7d00f5b1c9db626a0196634de", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBlo7JTxbs2dXaHtptsJmzt56h4I05\n53SGN8sTyWg29k4PMiei7HVj71iye78YgHpuOcu4CSJE/z9KPukpeTWi+hwBFkD4\nGf6faRHwyKVPly6URPjfUY+neKCXi8bmTh1Dsk67A7Ts4AmaElEV7P8yebLNyhXW\n5OfQD1scnbYmoBlmNN4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024200f208d5b19d30a200d6986c210681ef2bbcb3fb0166aa66ab55cd5dac9b50b037f9355386f8cb08743980f42165f567e5d41ad492b7d8c43a7b848fc0f0ed591530", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04006c7518061f076c33c62f0a7494b619c08bcc2782cd99ef6ab6f04545fa17d8c545bac600c1f91830225ddad1a71cabbb8afeee7d80cf537f6e391049dd70c3264301f6bfbaa4244b80f062ab7a66a1b6a707de567cf119b46aa5364cbe396dd4b749daf156a84c02b2c25a204dd47d7cfbae5ba394a62a82f9390145b898cca14bc0e1", "wx": "6c7518061f076c33c62f0a7494b619c08bcc2782cd99ef6ab6f04545fa17d8c545bac600c1f91830225ddad1a71cabbb8afeee7d80cf537f6e391049dd70c32643", "wy": "01f6bfbaa4244b80f062ab7a66a1b6a707de567cf119b46aa5364cbe396dd4b749daf156a84c02b2c25a204dd47d7cfbae5ba394a62a82f9390145b898cca14bc0e1"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004006c7518061f076c33c62f0a7494b619c08bcc2782cd99ef6ab6f04545fa17d8c545bac600c1f91830225ddad1a71cabbb8afeee7d80cf537f6e391049dd70c3264301f6bfbaa4244b80f062ab7a66a1b6a707de567cf119b46aa5364cbe396dd4b749daf156a84c02b2c25a204dd47d7cfbae5ba394a62a82f9390145b898cca14bc0e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAbHUYBh8HbDPGLwp0lLYZwIvMJ4LN\nme9qtvBFRfoX2MVFusYAwfkYMCJd2tGnHKu7iv7ufYDPU39uORBJ3XDDJkMB9r+6\npCRLgPBiq3pmobanB95WfPEZtGqlNky+OW3Ut0na8VaoTAKywlogTdR9fPuuW6OU\npiqC+TkBRbiYzKFLwOE=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024142c736a8baabf2b4176c7d010f8fc17ed981ddcbb2b8df40abc9b37b5d251fd3c8eb361449a0c33cb9d5f63851ce3cb76dc1aab684a94417a52883de9bff64fb27", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04013d7ae4676db161db18cf231eded0e3dbc1b23bfa353f1cd6ed8c472d1e6fca9c963d0fe95243ce65323a063386cb173a02c65acf07b3beaf349e89a612a07065c0012990485c9033cec789ec67989516b488e5246f27499a26c4f30489f4a287435ff55c638d1d2c7e28aeec1b08233d1a8cbfea9b6762efd34e0d387c512b12966e6d", "wx": "013d7ae4676db161db18cf231eded0e3dbc1b23bfa353f1cd6ed8c472d1e6fca9c963d0fe95243ce65323a063386cb173a02c65acf07b3beaf349e89a612a07065c0", "wy": "012990485c9033cec789ec67989516b488e5246f27499a26c4f30489f4a287435ff55c638d1d2c7e28aeec1b08233d1a8cbfea9b6762efd34e0d387c512b12966e6d"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004013d7ae4676db161db18cf231eded0e3dbc1b23bfa353f1cd6ed8c472d1e6fca9c963d0fe95243ce65323a063386cb173a02c65acf07b3beaf349e89a612a07065c0012990485c9033cec789ec67989516b488e5246f27499a26c4f30489f4a287435ff55c638d1d2c7e28aeec1b08233d1a8cbfea9b6762efd34e0d387c512b12966e6d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBPXrkZ22xYdsYzyMe3tDj28GyO/o1\nPxzW7YxHLR5vypyWPQ/pUkPOZTI6BjOGyxc6AsZazwezvq80nommEqBwZcABKZBI\nXJAzzseJ7GeYlRa0iOUkbydJmibE8wSJ9KKHQ1/1XGONHSx+KK7sGwgjPRqMv+qb\nZ2Lv004NOHxRKxKWbm0=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0242016db3214ef7a2afbd7189087133217bc55cf3d8c1dd84021da2290495569e8d259289d8203c2245f281513eb96ac7a5aea41fadff0d2ee7cfe2df3eeb2dde5be815", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400a0c75547630e9ed64cc1e59725a3072b231b28c586080966db67800636e4b4c217162c8d03a7ba0edf49669fdc682a355ef90947d7e394406296b354722636e00501df341f469c7e460e87aa96df2c90a7774e7532fae10410e932262937247a2ace6512e0f9a37f9547f4a8fe3247f093018d000003917c195c1d0ce5b36ab613b9f0", "wx": "00a0c75547630e9ed64cc1e59725a3072b231b28c586080966db67800636e4b4c217162c8d03a7ba0edf49669fdc682a355ef90947d7e394406296b354722636e005", "wy": "01df341f469c7e460e87aa96df2c90a7774e7532fae10410e932262937247a2ace6512e0f9a37f9547f4a8fe3247f093018d000003917c195c1d0ce5b36ab613b9f0"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400a0c75547630e9ed64cc1e59725a3072b231b28c586080966db67800636e4b4c217162c8d03a7ba0edf49669fdc682a355ef90947d7e394406296b354722636e00501df341f469c7e460e87aa96df2c90a7774e7532fae10410e932262937247a2ace6512e0f9a37f9547f4a8fe3247f093018d000003917c195c1d0ce5b36ab613b9f0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAoMdVR2MOntZMweWXJaMHKyMbKMWG\nCAlm22eABjbktMIXFiyNA6e6Dt9JZp/caCo1XvkJR9fjlEBilrNUciY24AUB3zQf\nRpx+Rg6HqpbfLJCnd051MvrhBBDpMiYpNyR6Ks5lEuD5o3+VR/So/jJH8JMBjQAA\nA5F8GVwdDOWzarYTufA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201fff74db841d0ef64b39513b2cca37621d8e3a46f04deaf9a7a5fb55c74c74957af0b4946347b2e26e6130a5a732d26d39eccc7774e670a0a9ce1485556a606302b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401a714661c4d299f540f291a07f2deefe6145d9f7a9ed492f8539b9e0b29a0d24bdfb71a89f53c1620c8ada2e06fe98ccfe11b988077816f0a04cc338d52266632dc0193c3b9c6658381eb61e0221e002b15cc622984550cb0289fb99636d926ccba6d6424dc0d39579f39743c821284849527b524a8650a2914cfdf3b11deaee453c8a9", "wx": "01a714661c4d299f540f291a07f2deefe6145d9f7a9ed492f8539b9e0b29a0d24bdfb71a89f53c1620c8ada2e06fe98ccfe11b988077816f0a04cc338d52266632dc", "wy": "0193c3b9c6658381eb61e0221e002b15cc622984550cb0289fb99636d926ccba6d6424dc0d39579f39743c821284849527b524a8650a2914cfdf3b11deaee453c8a9"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401a714661c4d299f540f291a07f2deefe6145d9f7a9ed492f8539b9e0b29a0d24bdfb71a89f53c1620c8ada2e06fe98ccfe11b988077816f0a04cc338d52266632dc0193c3b9c6658381eb61e0221e002b15cc622984550cb0289fb99636d926ccba6d6424dc0d39579f39743c821284849527b524a8650a2914cfdf3b11deaee453c8a9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBpxRmHE0pn1QPKRoH8t7v5hRdn3qe\n1JL4U5ueCymg0kvftxqJ9TwWIMitouBv6YzP4RuYgHeBbwoEzDONUiZmMtwBk8O5\nxmWDgeth4CIeACsVzGIphFUMsCifuZY22SbMum1kJNwNOVefOXQ8ghKEhJUntSSo\nZQopFM/fOxHeruRTyKk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201ffee9b7083a1dec9672a27659946ec43b1c748de09bd5f34f4bf6ab8e98e92af63c50c04e5372cb760a648b39d6344016d5dd924e44477cd8b0720f38ebad3fc4d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040128f58edf565a7a48e7e40a66dcd17476a609ea0a2768fd3fe64c8d1ae82d236f6704ebe5b64dd03e59d52c4a87aecd342019899e2f06f30b090c862b325273dd1200cc34bb7753bc1ed8445b9450262549bab049161d78129bb273ba528eee2c4050040be5d30ecdd65d5fc01866a512de1519a2f60d80ff522760a16bd3f67c03728c", "wx": "0128f58edf565a7a48e7e40a66dcd17476a609ea0a2768fd3fe64c8d1ae82d236f6704ebe5b64dd03e59d52c4a87aecd342019899e2f06f30b090c862b325273dd12", "wy": "00cc34bb7753bc1ed8445b9450262549bab049161d78129bb273ba528eee2c4050040be5d30ecdd65d5fc01866a512de1519a2f60d80ff522760a16bd3f67c03728c"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040128f58edf565a7a48e7e40a66dcd17476a609ea0a2768fd3fe64c8d1ae82d236f6704ebe5b64dd03e59d52c4a87aecd342019899e2f06f30b090c862b325273dd1200cc34bb7753bc1ed8445b9450262549bab049161d78129bb273ba528eee2c4050040be5d30ecdd65d5fc01866a512de1519a2f60d80ff522760a16bd3f67c03728c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBKPWO31Zaekjn5Apm3NF0dqYJ6gon\naP0/5kyNGugtI29nBOvltk3QPlnVLEqHrs00IBmJni8G8wsJDIYrMlJz3RIAzDS7\nd1O8HthEW5RQJiVJurBJFh14Epuyc7pSju4sQFAEC+XTDs3WXV/AGGalEt4VGaL2\nDYD/UidgoWvT9nwDcow=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201ffe5e928c572ce2e1abf3b1865ea62658aaaed4d0e9c0ecf6f1f20155e55dc07187ecec395f32b47db39870cc799612f3beeead27a21e590792cf991c6cfa1c86f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400e0a499117991efa71c57c12cfb2cd253fc5a2f04c30fe247cf3496f41c546b5c36d962a186e4f01135756e4eb0d8021dd06e0e728498e773922900020e8ca191b6008ed0c9ef71c85b0d2a2b18d6517ba9fdd4ca5247dd2cdf033720b4c45b6512a3e83d1bb0ccd7167b405b48f548edd67ea1abdfab2969f758f3cdb3f174edcc4552", "wx": "00e0a499117991efa71c57c12cfb2cd253fc5a2f04c30fe247cf3496f41c546b5c36d962a186e4f01135756e4eb0d8021dd06e0e728498e773922900020e8ca191b6", "wy": "008ed0c9ef71c85b0d2a2b18d6517ba9fdd4ca5247dd2cdf033720b4c45b6512a3e83d1bb0ccd7167b405b48f548edd67ea1abdfab2969f758f3cdb3f174edcc4552"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400e0a499117991efa71c57c12cfb2cd253fc5a2f04c30fe247cf3496f41c546b5c36d962a186e4f01135756e4eb0d8021dd06e0e728498e773922900020e8ca191b6008ed0c9ef71c85b0d2a2b18d6517ba9fdd4ca5247dd2cdf033720b4c45b6512a3e83d1bb0ccd7167b405b48f548edd67ea1abdfab2969f758f3cdb3f174edcc4552", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA4KSZEXmR76ccV8Es+yzSU/xaLwTD\nD+JHzzSW9BxUa1w22WKhhuTwETV1bk6w2AId0G4OcoSY53OSKQACDoyhkbYAjtDJ\n73HIWw0qKxjWUXup/dTKUkfdLN8DNyC0xFtlEqPoPRuwzNcWe0BbSPVI7dZ+oavf\nqylp91jzzbPxdO3MRVI=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u1", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201979df32aadcaeb51bc3ee529f8b43fcf91e5890d91ae7b2a63f0b18fa96e702759995da7fcbb7637415833de4725b0afbc829046a73e8f6a3e8708472d1db0c8c4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401969b92c818d09e66fc0d1dbaff4093b934f72fcb6a45b13a2cca89d6ab4ea9294987ada9dfb8abdbbadbb879b6da74bebd108b9dfbca74ec56cb8ba9d05cfbccfc007321b6ea2f1640675e339feec4d93b0bd7f3dfa9c633c7da4ec05295b7b5fbd38d6ae348af87ba99fc7a29e204fe864137f9946efb7702ed34d1bc5e3458a31807", "wx": "01969b92c818d09e66fc0d1dbaff4093b934f72fcb6a45b13a2cca89d6ab4ea9294987ada9dfb8abdbbadbb879b6da74bebd108b9dfbca74ec56cb8ba9d05cfbccfc", "wy": "7321b6ea2f1640675e339feec4d93b0bd7f3dfa9c633c7da4ec05295b7b5fbd38d6ae348af87ba99fc7a29e204fe864137f9946efb7702ed34d1bc5e3458a31807"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401969b92c818d09e66fc0d1dbaff4093b934f72fcb6a45b13a2cca89d6ab4ea9294987ada9dfb8abdbbadbb879b6da74bebd108b9dfbca74ec56cb8ba9d05cfbccfc007321b6ea2f1640675e339feec4d93b0bd7f3dfa9c633c7da4ec05295b7b5fbd38d6ae348af87ba99fc7a29e204fe864137f9946efb7702ed34d1bc5e3458a31807", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBlpuSyBjQnmb8DR26/0CTuTT3L8tq\nRbE6LMqJ1qtOqSlJh62p37ir27rbuHm22nS+vRCLnfvKdOxWy4up0Fz7zPwAcyG2\n6i8WQGdeM5/uxNk7C9fz36nGM8faTsBSlbe1+9ONauNIr4e6mfx6KeIE/oZBN/mU\nbvt3Au000bxeNFijGAc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u2", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02415555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555554", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04006b6e33ab1fc74e112d5d7d279ac51b67427181454806ff33bdbdc67af37ad704b521c74dce1f9ffe38e253dd152b1c108a37db3b3fbc40a2e76131d0399bc0011e00c2d36db9647c3d71d98606698dae0afbc8dbb648c9dfd4e2ca523ddc72a0bd95e7f6abc7e6ce11a2c40123dc1cc985e155887535e2907a905d8d51e9d3ed01330e", "wx": "6b6e33ab1fc74e112d5d7d279ac51b67427181454806ff33bdbdc67af37ad704b521c74dce1f9ffe38e253dd152b1c108a37db3b3fbc40a2e76131d0399bc0011e", "wy": "00c2d36db9647c3d71d98606698dae0afbc8dbb648c9dfd4e2ca523ddc72a0bd95e7f6abc7e6ce11a2c40123dc1cc985e155887535e2907a905d8d51e9d3ed01330e"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004006b6e33ab1fc74e112d5d7d279ac51b67427181454806ff33bdbdc67af37ad704b521c74dce1f9ffe38e253dd152b1c108a37db3b3fbc40a2e76131d0399bc0011e00c2d36db9647c3d71d98606698dae0afbc8dbb648c9dfd4e2ca523ddc72a0bd95e7f6abc7e6ce11a2c40123dc1cc985e155887535e2907a905d8d51e9d3ed01330e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAa24zqx/HThEtXX0nmsUbZ0JxgUVI\nBv8zvb3GevN61wS1IcdNzh+f/jjiU90VKxwQijfbOz+8QKLnYTHQOZvAAR4AwtNt\nuWR8PXHZhgZpja4K+8jbtkjJ39TiylI93HKgvZXn9qvH5s4RosQBI9wcyYXhVYh1\nNeKQepBdjVHp0+0BMw4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0242009f57708fa97eba94c6d4782cdd4e33bb95c1353bde095232e3e2bab277bb5d2b48f55a53ffe928d034c29970a9e5f384a003907d3d9b82a86817cc61fb17f4c59e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401c0a09793e65ca31b1037cdcb9da8f8e0a8dd648a6a4f3d8cb98398dce856cd7a123da290e665a8a12819846e8f3462eef875abc5fadf8af9466ab7cca03ebeb4eb01c37331f2290f75ccd68b81f1e30daffb63d2319d8481272a7d65f9e4e3ac8ff34db0c403f86a6ea990436c66f24489f5f39643ac1046e99e11db924978d0e3812f", "wx": "01c0a09793e65ca31b1037cdcb9da8f8e0a8dd648a6a4f3d8cb98398dce856cd7a123da290e665a8a12819846e8f3462eef875abc5fadf8af9466ab7cca03ebeb4eb", "wy": "01c37331f2290f75ccd68b81f1e30daffb63d2319d8481272a7d65f9e4e3ac8ff34db0c403f86a6ea990436c66f24489f5f39643ac1046e99e11db924978d0e3812f"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401c0a09793e65ca31b1037cdcb9da8f8e0a8dd648a6a4f3d8cb98398dce856cd7a123da290e665a8a12819846e8f3462eef875abc5fadf8af9466ab7cca03ebeb4eb01c37331f2290f75ccd68b81f1e30daffb63d2319d8481272a7d65f9e4e3ac8ff34db0c403f86a6ea990436c66f24489f5f39643ac1046e99e11db924978d0e3812f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBwKCXk+ZcoxsQN83Lnaj44KjdZIpq\nTz2MuYOY3OhWzXoSPaKQ5mWooSgZhG6PNGLu+HWrxfrfivlGarfMoD6+tOsBw3Mx\n8ikPdczWi4Hx4w2v+2PSMZ2EgScqfWX55OOsj/NNsMQD+GpuqZBDbGbyRIn185ZD\nrBBG6Z4R25JJeNDjgS8=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u2", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024168d98fa90736eff3e90f8fcfe50838b6fa0bf2cde77bc51e3f41019c8006f4e9cbaeadce7dbb44462da6425be9cfdaecb234c41749ce695be1b5ead2e6b1205f35", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040062d009d1eeade4561686275a42e64e26cccd4bb6aa7385a61fa366ad56359a209fb315e738d64a9a88ae81c77bcf585de4ab41f258244e3749b56e6b284047fbd4008e58c40bca682bd13221dd61cd18485c1f831c2dd9e22525c127bab56f49741f30f39bae9fd74533662b883df06c15bc673919b36abfcd48f08c90f1b4042908e2", "wx": "62d009d1eeade4561686275a42e64e26cccd4bb6aa7385a61fa366ad56359a209fb315e738d64a9a88ae81c77bcf585de4ab41f258244e3749b56e6b284047fbd4", "wy": "008e58c40bca682bd13221dd61cd18485c1f831c2dd9e22525c127bab56f49741f30f39bae9fd74533662b883df06c15bc673919b36abfcd48f08c90f1b4042908e2"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040062d009d1eeade4561686275a42e64e26cccd4bb6aa7385a61fa366ad56359a209fb315e738d64a9a88ae81c77bcf585de4ab41f258244e3749b56e6b284047fbd4008e58c40bca682bd13221dd61cd18485c1f831c2dd9e22525c127bab56f49741f30f39bae9fd74533662b883df06c15bc673919b36abfcd48f08c90f1b4042908e2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAYtAJ0e6t5FYWhidaQuZOJszNS7aq\nc4WmH6NmrVY1miCfsxXnONZKmoiugcd7z1hd5KtB8lgkTjdJtW5rKEBH+9QAjljE\nC8poK9EyId1hzRhIXB+DHC3Z4iUlwSe6tW9JdB8w85uun9dFM2YriD3wbBW8ZzkZ\ns2q/zUjwjJDxtAQpCOI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024200e97ae66bcd4cae36fffffffffffffffffffffffffffffffffffffffffffffffffd68bc9726f02dbf8598a98b3e5077eff6f2491eb678ed040fb338c084a9ea8a4c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400bae91802d593acc94727e46d1b777887be8a8291c99689de4bd4b790b5d4f7ab7569f167d6f5d236f13ec04ffa25e8a76723d02a44ab586cb2f4c7433a272692810092d7569c829789ac45f625c72d50ecaf38fc8f7e82e5b89a986dcfc912225609018a6d618df087096b21c67c00db983bed0b0fbeba7a934ad2149cfb275c4d582d", "wx": "00bae91802d593acc94727e46d1b777887be8a8291c99689de4bd4b790b5d4f7ab7569f167d6f5d236f13ec04ffa25e8a76723d02a44ab586cb2f4c7433a27269281", "wy": "0092d7569c829789ac45f625c72d50ecaf38fc8f7e82e5b89a986dcfc912225609018a6d618df087096b21c67c00db983bed0b0fbeba7a934ad2149cfb275c4d582d"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400bae91802d593acc94727e46d1b777887be8a8291c99689de4bd4b790b5d4f7ab7569f167d6f5d236f13ec04ffa25e8a76723d02a44ab586cb2f4c7433a272692810092d7569c829789ac45f625c72d50ecaf38fc8f7e82e5b89a986dcfc912225609018a6d618df087096b21c67c00db983bed0b0fbeba7a934ad2149cfb275c4d582d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAuukYAtWTrMlHJ+RtG3d4h76KgpHJ\nloneS9S3kLXU96t1afFn1vXSNvE+wE/6JeinZyPQKkSrWGyy9MdDOicmkoEAktdW\nnIKXiaxF9iXHLVDsrzj8j36C5biamG3PyRIiVgkBim1hjfCHCWshxnwA25g77QsP\nvrp6k0rSFJz7J1xNWC0=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201ae66bcd4cae36ffffffffffffffffffffffffffffffffffffffffffffffffffffb3954212f8bea578d93e685e5dba329811b2542bb398233e2944bceb19263325d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400bb394e65fc89fabb9b54d97e4f6133a9091a74f0bd66b1fe77b0d7cae16e98beda1907f70c37ab300af9dfcbfe01d9aa433e1855159b663a653d973c92a0a6a01c0159494aa3b06f758c0df70d6244e6001543e0092fe2e4d692299fc442a30d37f834c478b84b58dd357830785404a3e0175ccc65e2e77bcd3751d59d7881ea88c077", "wx": "00bb394e65fc89fabb9b54d97e4f6133a9091a74f0bd66b1fe77b0d7cae16e98beda1907f70c37ab300af9dfcbfe01d9aa433e1855159b663a653d973c92a0a6a01c", "wy": "0159494aa3b06f758c0df70d6244e6001543e0092fe2e4d692299fc442a30d37f834c478b84b58dd357830785404a3e0175ccc65e2e77bcd3751d59d7881ea88c077"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400bb394e65fc89fabb9b54d97e4f6133a9091a74f0bd66b1fe77b0d7cae16e98beda1907f70c37ab300af9dfcbfe01d9aa433e1855159b663a653d973c92a0a6a01c0159494aa3b06f758c0df70d6244e6001543e0092fe2e4d692299fc442a30d37f834c478b84b58dd357830785404a3e0175ccc65e2e77bcd3751d59d7881ea88c077", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAuzlOZfyJ+rubVNl+T2EzqQkadPC9\nZrH+d7DXyuFumL7aGQf3DDerMAr538v+AdmqQz4YVRWbZjplPZc8kqCmoBwBWUlK\no7BvdYwN9w1iROYAFUPgCS/i5NaSKZ/EQqMNN/g0xHi4S1jdNXgweFQEo+AXXMxl\n4ud7zTdR1Z14geqIwHc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0242015ccd79a995c6dffffffffffffffffffffffffffffffffffffffffffffffffffffc2121badb58a518afa8010a82c03cad31fa94bbbde96820166d27e644938e00b1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04014e96e622ab11d843ed0df286c2a9fe40f20027ba9ab34c645de827d1867f3ed4045c781ae9d09424e8f77ef57422f52b38caa2b999e6cf1ead157a8834a5655679011db6f3c819fed7b71d766b41905304e61d9f19fc9a6c49267bf2181023e667ebe3465f5521cb1a1d55a5e2722e700c1624dc2d4c9653bcdce8b6fe793c568b35b7", "wx": "014e96e622ab11d843ed0df286c2a9fe40f20027ba9ab34c645de827d1867f3ed4045c781ae9d09424e8f77ef57422f52b38caa2b999e6cf1ead157a8834a5655679", "wy": "011db6f3c819fed7b71d766b41905304e61d9f19fc9a6c49267bf2181023e667ebe3465f5521cb1a1d55a5e2722e700c1624dc2d4c9653bcdce8b6fe793c568b35b7"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004014e96e622ab11d843ed0df286c2a9fe40f20027ba9ab34c645de827d1867f3ed4045c781ae9d09424e8f77ef57422f52b38caa2b999e6cf1ead157a8834a5655679011db6f3c819fed7b71d766b41905304e61d9f19fc9a6c49267bf2181023e667ebe3465f5521cb1a1d55a5e2722e700c1624dc2d4c9653bcdce8b6fe793c568b35b7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBTpbmIqsR2EPtDfKGwqn+QPIAJ7qa\ns0xkXegn0YZ/PtQEXHga6dCUJOj3fvV0IvUrOMqiuZnmzx6tFXqINKVlVnkBHbbz\nyBn+17cddmtBkFME5h2fGfyabEkme/IYECPmZ+vjRl9VIcsaHVWl4nIucAwWJNwt\nTJZTvNzotv55PFaLNbc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201cd4cae36fffffffffffffffffffffffffffffffffffffffffffffffffffffffffae18dcc11dff7526233d923a0b202cb29e713f22de8bb6ab0a12821c5abbe3f23", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400b7950ea9ebf9d05d460d451dd20d2b70916e258f58d0b550e215d160fb23bb5696efc9457ccededd93cff5f49eb2ae1e27e93fe9f463cdc75f12e317bf69935b7b00252c57f7438c12484feab9e78180588259b17cffe1f613bdef0bfb4dc33584ab3566eecbbb9626d67546b1b246323d3317b7e68273fcc6053eb16fddc0c5de8295", "wx": "00b7950ea9ebf9d05d460d451dd20d2b70916e258f58d0b550e215d160fb23bb5696efc9457ccededd93cff5f49eb2ae1e27e93fe9f463cdc75f12e317bf69935b7b", "wy": "252c57f7438c12484feab9e78180588259b17cffe1f613bdef0bfb4dc33584ab3566eecbbb9626d67546b1b246323d3317b7e68273fcc6053eb16fddc0c5de8295"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400b7950ea9ebf9d05d460d451dd20d2b70916e258f58d0b550e215d160fb23bb5696efc9457ccededd93cff5f49eb2ae1e27e93fe9f463cdc75f12e317bf69935b7b00252c57f7438c12484feab9e78180588259b17cffe1f613bdef0bfb4dc33584ab3566eecbbb9626d67546b1b246323d3317b7e68273fcc6053eb16fddc0c5de8295", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAt5UOqev50F1GDUUd0g0rcJFuJY9Y\n0LVQ4hXRYPsju1aW78lFfM7e3ZPP9fSesq4eJ+k/6fRjzcdfEuMXv2mTW3sAJSxX\n90OMEkhP6rnngYBYglmxfP/h9hO97wv7TcM1hKs1Zu7Lu5Ym1nVGsbJGMj0zF7fm\ngnP8xgU+sW/dwMXegpU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "edge case for u2", "msg": "************", "sig": "308187024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024122e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8ba2e8b9c4c3f73cc816143fac3412b62de4c63db08f8c57e4c58c31f1b457ca5e57e20a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401ababd5abee5cc082c9a4cd04721820998f21303f6fe43566a858563a23d8431bb9cbf5db84b0be4ce95cc962b78c7713374a34435f84fb065984b98fb2b486d78e000eb4914193029be32044ed049f06d0fe4d7db7c9938600237dfaee8643ea0f2a157e57198d5cd9020ed7c2ac952a072bd4e82a211ec1e20f22e871b81556db361a", "wx": "01ababd5abee5cc082c9a4cd04721820998f21303f6fe43566a858563a23d8431bb9cbf5db84b0be4ce95cc962b78c7713374a34435f84fb065984b98fb2b486d78e", "wy": "0eb4914193029be32044ed049f06d0fe4d7db7c9938600237dfaee8643ea0f2a157e57198d5cd9020ed7c2ac952a072bd4e82a211ec1e20f22e871b81556db361a"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401ababd5abee5cc082c9a4cd04721820998f21303f6fe43566a858563a23d8431bb9cbf5db84b0be4ce95cc962b78c7713374a34435f84fb065984b98fb2b486d78e000eb4914193029be32044ed049f06d0fe4d7db7c9938600237dfaee8643ea0f2a157e57198d5cd9020ed7c2ac952a072bd4e82a211ec1e20f22e871b81556db361a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBq6vVq+5cwILJpM0EchggmY8hMD9v\n5DVmqFhWOiPYQxu5y/XbhLC+TOlcyWK3jHcTN0o0Q1+E+wZZhLmPsrSG144ADrSR\nQZMCm+MgRO0EnwbQ/k19t8mThgAjffruhkPqDyoVflcZjVzZAg7XwqyVKgcr1Ogq\nIR7B4g8i6HG4FVbbNho=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0242010590b21642c8590b21642c8590b21642c8590b21642c8590b21642c8590b2164298eb57e5aff9343597a542d3132f9e734fdc305125e0ec139c5f780ee8e8cb9c2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040003b406bd7905cf188d2e6e3079dc66eac8411fbc6d1092a02cdc5e9b4d3eb5054a1032ff2fffd194413b2dbb95ef78262cad749195465e1a52f1dcfa858df3f90d01ca7a0dbffbcf6d74209a0a334d607c39c79aba96152d32ac3693c490b2a83d87b30428f418794b80cde96fb59b5e9030557d2411445c337411047f1d628ac23e2b", "wx": "03b406bd7905cf188d2e6e3079dc66eac8411fbc6d1092a02cdc5e9b4d3eb5054a1032ff2fffd194413b2dbb95ef78262cad749195465e1a52f1dcfa858df3f90d", "wy": "01ca7a0dbffbcf6d74209a0a334d607c39c79aba96152d32ac3693c490b2a83d87b30428f418794b80cde96fb59b5e9030557d2411445c337411047f1d628ac23e2b"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040003b406bd7905cf188d2e6e3079dc66eac8411fbc6d1092a02cdc5e9b4d3eb5054a1032ff2fffd194413b2dbb95ef78262cad749195465e1a52f1dcfa858df3f90d01ca7a0dbffbcf6d74209a0a334d607c39c79aba96152d32ac3693c490b2a83d87b30428f418794b80cde96fb59b5e9030557d2411445c337411047f1d628ac23e2b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAA7QGvXkFzxiNLm4wedxm6shBH7xt\nEJKgLNxem00+tQVKEDL/L//RlEE7LbuV73gmLK10kZVGXhpS8dz6hY3z+Q0BynoN\nv/vPbXQgmgozTWB8OceaupYVLTKsNpPEkLKoPYezBCj0GHlLgM3pb7WbXpAwVX0k\nEURcM3QRBH8dYorCPis=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201a4924924924924924924924924924924924924924924924924924924924924924445e10670ed0437c9db4125ac4175fbd70e9bd1799a85f44ca0a8e61a3354e808", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04009faf51f370945040e6609734445807a42bfbd11e24455e9c8da44df7e9637c76b39bd84dde33bf2c66e315124f6412fc3390aa2127d1d7c1803e32e10e8f9bd0dd0189b5ecb618bfd16cc67c671cc6da9724796e10350f8a15f9e2192d8d32d27e2b69897b5c61d3fb71b1c72146d31dceca0e4204abb2404e81ac664ca731fec6fe80", "wx": "009faf51f370945040e6609734445807a42bfbd11e24455e9c8da44df7e9637c76b39bd84dde33bf2c66e315124f6412fc3390aa2127d1d7c1803e32e10e8f9bd0dd", "wy": "0189b5ecb618bfd16cc67c671cc6da9724796e10350f8a15f9e2192d8d32d27e2b69897b5c61d3fb71b1c72146d31dceca0e4204abb2404e81ac664ca731fec6fe80"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004009faf51f370945040e6609734445807a42bfbd11e24455e9c8da44df7e9637c76b39bd84dde33bf2c66e315124f6412fc3390aa2127d1d7c1803e32e10e8f9bd0dd0189b5ecb618bfd16cc67c671cc6da9724796e10350f8a15f9e2192d8d32d27e2b69897b5c61d3fb71b1c72146d31dceca0e4204abb2404e81ac664ca731fec6fe80", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAn69R83CUUEDmYJc0RFgHpCv70R4k\nRV6cjaRN9+ljfHazm9hN3jO/LGbjFRJPZBL8M5CqISfR18GAPjLhDo+b0N0BibXs\nthi/0WzGfGccxtqXJHluEDUPihX54hktjTLSfitpiXtcYdP7cbHHIUbTHc7KDkIE\nq7JAToGsZkynMf7G/oA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201d5555555555555555555555555555555555555555555555555555555555555554fa6dbdcd91484ebc0d521569e4c5efb25910b1f0ddef19d0410c50c73e68db95f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400b99ef22bb58cc25edff6c197315f418dd66efe28bde5e25b3b696d6f9db1b1a017b4f269af407b8b609506080f29eaedd4cf7087e00b599dc5e4d2a8388867204a01b999ffdc2b3c5819366c9295e1df5c6468e70396c7fa8c9a926caebe6a1145b115c107c87d106f384f283fe2d2aec65b202745115a14ee506920d02a645ebaa7c5", "wx": "00b99ef22bb58cc25edff6c197315f418dd66efe28bde5e25b3b696d6f9db1b1a017b4f269af407b8b609506080f29eaedd4cf7087e00b599dc5e4d2a8388867204a", "wy": "01b999ffdc2b3c5819366c9295e1df5c6468e70396c7fa8c9a926caebe6a1145b115c107c87d106f384f283fe2d2aec65b202745115a14ee506920d02a645ebaa7c5"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400b99ef22bb58cc25edff6c197315f418dd66efe28bde5e25b3b696d6f9db1b1a017b4f269af407b8b609506080f29eaedd4cf7087e00b599dc5e4d2a8388867204a01b999ffdc2b3c5819366c9295e1df5c6468e70396c7fa8c9a926caebe6a1145b115c107c87d106f384f283fe2d2aec65b202745115a14ee506920d02a645ebaa7c5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAuZ7yK7WMwl7f9sGXMV9BjdZu/ii9\n5eJbO2ltb52xsaAXtPJpr0B7i2CVBggPKert1M9wh+ALWZ3F5NKoOIhnIEoBuZn/\n3Cs8WBk2bJKV4d9cZGjnA5bH+oyakmyuvmoRRbEVwQfIfRBvOE8oP+LSrsZbICdF\nEVoU7lBpINAqZF66p8U=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4fc31322e69da41162a76abf3a1b4507ae66074633446f259661a61c93be30eb5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401b261cd6f287105804bc2885d5a59e791712ab6b9f14fe6ff712f5f4dd693c7b01c272156f895b84f74c6718926baa5f55a73c235351746ec99f0898bfaa24bb4d0014b855367abf6f742976ff49e834612544bc983a842e56e0dc9cff9289c64f40efe350dc6301f848ed7c741d3b9ce824ebc779fd1e6c068cc77edc009aea1666592", "wx": "01b261cd6f287105804bc2885d5a59e791712ab6b9f14fe6ff712f5f4dd693c7b01c272156f895b84f74c6718926baa5f55a73c235351746ec99f0898bfaa24bb4d0", "wy": "014b855367abf6f742976ff49e834612544bc983a842e56e0dc9cff9289c64f40efe350dc6301f848ed7c741d3b9ce824ebc779fd1e6c068cc77edc009aea1666592"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401b261cd6f287105804bc2885d5a59e791712ab6b9f14fe6ff712f5f4dd693c7b01c272156f895b84f74c6718926baa5f55a73c235351746ec99f0898bfaa24bb4d0014b855367abf6f742976ff49e834612544bc983a842e56e0dc9cff9289c64f40efe350dc6301f848ed7c741d3b9ce824ebc779fd1e6c068cc77edc009aea1666592", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBsmHNbyhxBYBLwohdWlnnkXEqtrnx\nT+b/cS9fTdaTx7AcJyFW+JW4T3TGcYkmuqX1WnPCNTUXRuyZ8ImL+qJLtNABS4VT\nZ6v290KXb/Seg0YSVEvJg6hC5W4Nyc/5KJxk9A7+NQ3GMB+EjtfHQdO5zoJOvHef\n0ebAaMx37cAJrqFmZZI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0242017ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e9138640b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04003f6475303719ae52bbd2bb4606f8dcb862cbf38bb8545b7c0759d6bd8355f260e10459f788b319df203c3d41558fd61a1770bccdafb355861064acb5c19e022165011423d9b472d31faaffdfbd6a3e83f7c8e79ef1bed35a6135abe93e3994e17321c2e8ee6268b1767c29671a43e5bb0d99751e137937a1db4958a3b29c3ad76cac59", "wx": "3f6475303719ae52bbd2bb4606f8dcb862cbf38bb8545b7c0759d6bd8355f260e10459f788b319df203c3d41558fd61a1770bccdafb355861064acb5c19e022165", "wy": "011423d9b472d31faaffdfbd6a3e83f7c8e79ef1bed35a6135abe93e3994e17321c2e8ee6268b1767c29671a43e5bb0d99751e137937a1db4958a3b29c3ad76cac59"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004003f6475303719ae52bbd2bb4606f8dcb862cbf38bb8545b7c0759d6bd8355f260e10459f788b319df203c3d41558fd61a1770bccdafb355861064acb5c19e022165011423d9b472d31faaffdfbd6a3e83f7c8e79ef1bed35a6135abe93e3994e17321c2e8ee6268b1767c29671a43e5bb0d99751e137937a1db4958a3b29c3ad76cac59", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAP2R1MDcZrlK70rtGBvjcuGLL84u4\nVFt8B1nWvYNV8mDhBFn3iLMZ3yA8PUFVj9YaF3C8za+zVYYQZKy1wZ4CIWUBFCPZ\ntHLTH6r/371qPoP3yOee8b7TWmE1q+k+OZThcyHC6O5iaLF2fClnGkPluw2ZdR4T\neTeh20lYo7KcOtdsrFk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "edge case for u2", "msg": "************", "sig": "308188024200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc024201346cc7d4839b77f9f487c7e7f2841c5b7d05f966f3bde28f1fa080ce40037a74e3001a2b00bd39ee4c93072e9963724941383cf0812c02d1c838ad4502a12c619f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400ee030cdb40abf70726866681f7b7fedc534190929c05a650bb928b894a5bbfe9577eea83c6331a796fa27ed9fac95d9ecacdfef6d61c925502b0afddc6714635490155606dd4cab19330c57c2ee740cd9c7c88bd88d95f840f315d525379dfeb7ea9bd3677b2185b92957f374317cc6124aacc8708075c4c05c95cbbc355bd692c3708", "wx": "00ee030cdb40abf70726866681f7b7fedc534190929c05a650bb928b894a5bbfe9577eea83c6331a796fa27ed9fac95d9ecacdfef6d61c925502b0afddc671463549", "wy": "0155606dd4cab19330c57c2ee740cd9c7c88bd88d95f840f315d525379dfeb7ea9bd3677b2185b92957f374317cc6124aacc8708075c4c05c95cbbc355bd692c3708"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400ee030cdb40abf70726866681f7b7fedc534190929c05a650bb928b894a5bbfe9577eea83c6331a796fa27ed9fac95d9ecacdfef6d61c925502b0afddc6714635490155606dd4cab19330c57c2ee740cd9c7c88bd88d95f840f315d525379dfeb7ea9bd3677b2185b92957f374317cc6124aacc8708075c4c05c95cbbc355bd692c3708", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA7gMM20Cr9wcmhmaB97f+3FNBkJKc\nBaZQu5KLiUpbv+lXfuqDxjMaeW+iftn6yV2eys3+9tYcklUCsK/dxnFGNUkBVWBt\n1MqxkzDFfC7nQM2cfIi9iNlfhA8xXVJTed/rfqm9NneyGFuSlX83QxfMYSSqzIcI\nB1xMBclcu8NVvWksNwg=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "point duplication during verification", "msg": "************", "sig": "30818702420090c8d0d718cb9d8d81094e6d068fb13c16b4df8c77bac676dddfe3e68855bed06b9ba8d0f8a80edce03a9fac7da561e24b1cd22d459239a146695a671f81f73aaf02413ee5a0a544b0842134629640adf5f0637087b04a442b1e6a22555dc1d8b93f8784f1ddd0cf90f75944cc2cd7ae373e5c2bac356a60ff9d08adfcdba3fa1b7a9d1d", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400ee030cdb40abf70726866681f7b7fedc534190929c05a650bb928b894a5bbfe9577eea83c6331a796fa27ed9fac95d9ecacdfef6d61c925502b0afddc67146354900aa9f922b354e6ccf3a83d118bf32638377427726a07bf0cea2adac862014815642c9884de7a46d6a80c8bce8339edb553378f7f8a3b3fa36a3443caa4296d3c8f7", "wx": "00ee030cdb40abf70726866681f7b7fedc534190929c05a650bb928b894a5bbfe9577eea83c6331a796fa27ed9fac95d9ecacdfef6d61c925502b0afddc671463549", "wy": "00aa9f922b354e6ccf3a83d118bf32638377427726a07bf0cea2adac862014815642c9884de7a46d6a80c8bce8339edb553378f7f8a3b3fa36a3443caa4296d3c8f7"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400ee030cdb40abf70726866681f7b7fedc534190929c05a650bb928b894a5bbfe9577eea83c6331a796fa27ed9fac95d9ecacdfef6d61c925502b0afddc67146354900aa9f922b354e6ccf3a83d118bf32638377427726a07bf0cea2adac862014815642c9884de7a46d6a80c8bce8339edb553378f7f8a3b3fa36a3443caa4296d3c8f7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA7gMM20Cr9wcmhmaB97f+3FNBkJKc\nBaZQu5KLiUpbv+lXfuqDxjMaeW+iftn6yV2eys3+9tYcklUCsK/dxnFGNUkAqp+S\nKzVObM86g9EYvzJjg3dCdyage/DOoq2shiAUgVZCyYhN56RtaoDIvOgznttVM3j3\n+KOz+jajRDyqQpbTyPc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "duplication bug", "msg": "************", "sig": "30818702420090c8d0d718cb9d8d81094e6d068fb13c16b4df8c77bac676dddfe3e68855bed06b9ba8d0f8a80edce03a9fac7da561e24b1cd22d459239a146695a671f81f73aaf02413ee5a0a544b0842134629640adf5f0637087b04a442b1e6a22555dc1d8b93f8784f1ddd0cf90f75944cc2cd7ae373e5c2bac356a60ff9d08adfcdba3fa1b7a9d1d", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400ff928f93a654fca5db79158bf8a16960b77729b0663eb72f319054d453c171794e66fa10438beb55416bc89663c8a4d23c645417f7a3d23ec88358d674ee7c50a701ea26dd23b0b878125c75e3a524801b4b58d0eb7513b3ae7b8b6080a2f2b9286bfae256b5b6571ec3d72fa814aa1d02f1610529c41a68cbaf78783738bf961e3681", "wx": "00ff928f93a654fca5db79158bf8a16960b77729b0663eb72f319054d453c171794e66fa10438beb55416bc89663c8a4d23c645417f7a3d23ec88358d674ee7c50a7", "wy": "01ea26dd23b0b878125c75e3a524801b4b58d0eb7513b3ae7b8b6080a2f2b9286bfae256b5b6571ec3d72fa814aa1d02f1610529c41a68cbaf78783738bf961e3681"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400ff928f93a654fca5db79158bf8a16960b77729b0663eb72f319054d453c171794e66fa10438beb55416bc89663c8a4d23c645417f7a3d23ec88358d674ee7c50a701ea26dd23b0b878125c75e3a524801b4b58d0eb7513b3ae7b8b6080a2f2b9286bfae256b5b6571ec3d72fa814aa1d02f1610529c41a68cbaf78783738bf961e3681", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA/5KPk6ZU/KXbeRWL+KFpYLd3KbBm\nPrcvMZBU1FPBcXlOZvoQQ4vrVUFryJZjyKTSPGRUF/ej0j7Ig1jWdO58UKcB6ibd\nI7C4eBJcdeOlJIAbS1jQ63UTs657i2CAovK5KGv64la1tlcew9cvqBSqHQLxYQUp\nxBpoy694eDc4v5YeNoE=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "point with x-coordinate 0", "msg": "************", "sig": "3047020101024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040019eb6b28ff90f9d4987669186ecfd84ee28325831404d2bf61d31a8fce2f79435e676d2ad09498f813bcf343e929205b4c3941e5d9d1cd18c0f398b1e6dda088910153f6a8b606b96fa0178af70d8d591a2825fed4b6fbd23e37666f8a25df1d37b7d08cd0ed367e23e97112371c8ea0d737b6f2b13a19abf6a2359fd055d10c4e0d89", "wx": "19eb6b28ff90f9d4987669186ecfd84ee28325831404d2bf61d31a8fce2f79435e676d2ad09498f813bcf343e929205b4c3941e5d9d1cd18c0f398b1e6dda08891", "wy": "0153f6a8b606b96fa0178af70d8d591a2825fed4b6fbd23e37666f8a25df1d37b7d08cd0ed367e23e97112371c8ea0d737b6f2b13a19abf6a2359fd055d10c4e0d89"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040019eb6b28ff90f9d4987669186ecfd84ee28325831404d2bf61d31a8fce2f79435e676d2ad09498f813bcf343e929205b4c3941e5d9d1cd18c0f398b1e6dda088910153f6a8b606b96fa0178af70d8d591a2825fed4b6fbd23e37666f8a25df1d37b7d08cd0ed367e23e97112371c8ea0d737b6f2b13a19abf6a2359fd055d10c4e0d89", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAGetrKP+Q+dSYdmkYbs/YTuKDJYMU\nBNK/YdMaj84veUNeZ20q0JSY+BO880PpKSBbTDlB5dnRzRjA85ix5t2giJEBU/ao\ntga5b6AXivcNjVkaKCX+1Lb70j43Zm+KJd8dN7fQjNDtNn4j6XESNxyOoNc3tvKx\nOhmr9qI1n9BV0QxODYk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "point with x-coordinate 0", "msg": "************", "sig": "3081870242020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024166666666666666666666666666666666666666666666666666666666666666666543814e4d8ca31e157ff599db649b87900bf128581b85a7efbf1657d2e9d81401", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400c27585d9eebf4e8907b799c96ef0915953b2ad36bf450e018862062c170e2fa5110f4b04f172c3dff5f6bc5d756ca3e91fdc408b579c870df2b02a09f43db7ca16008d713c42d9f1d9b7b09e0253bfbc1ce88f7579986210f7a8281009817c163cb36b6e940acc38e53a88efdc34982f39d785054a48d06facf3ebe455dac6833b527c", "wx": "00c27585d9eebf4e8907b799c96ef0915953b2ad36bf450e018862062c170e2fa5110f4b04f172c3dff5f6bc5d756ca3e91fdc408b579c870df2b02a09f43db7ca16", "wy": "008d713c42d9f1d9b7b09e0253bfbc1ce88f7579986210f7a8281009817c163cb36b6e940acc38e53a88efdc34982f39d785054a48d06facf3ebe455dac6833b527c"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400c27585d9eebf4e8907b799c96ef0915953b2ad36bf450e018862062c170e2fa5110f4b04f172c3dff5f6bc5d756ca3e91fdc408b579c870df2b02a09f43db7ca16008d713c42d9f1d9b7b09e0253bfbc1ce88f7579986210f7a8281009817c163cb36b6e940acc38e53a88efdc34982f39d785054a48d06facf3ebe455dac6833b527c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAwnWF2e6/TokHt5nJbvCRWVOyrTa/\nRQ4BiGIGLBcOL6URD0sE8XLD3/X2vF11bKPpH9xAi1echw3ysCoJ9D23yhYAjXE8\nQtnx2bewngJTv7wc6I91eZhiEPeoKBAJgXwWPLNrbpQKzDjlOojv3DSYLznXhQVK\nSNBvrPPr5FXaxoM7Unw=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "comparison with point at infinity ", "msg": "************", "sig": "308187024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad024166666666666666666666666666666666666666666666666666666666666666666543814e4d8ca31e157ff599db649b87900bf128581b85a7efbf1657d2e9d81401", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04007da8b570bacaad47b6c1c87934ab4542ed8629303b2fe2167f452fcd0451995619fd175b4b4d03766901be3baad5afb87ba3104584cdebaae6df6503c18af70793006b2f42d5f7b3a626d2745cb597ea86c95bf37c0609716e186d3b8882549db5c8476b737421155706d6f8b753323700492cec0c8eb07d1f0edd341aa0a50a5f29d6", "wx": "7da8b570bacaad47b6c1c87934ab4542ed8629303b2fe2167f452fcd0451995619fd175b4b4d03766901be3baad5afb87ba3104584cdebaae6df6503c18af70793", "wy": "6b2f42d5f7b3a626d2745cb597ea86c95bf37c0609716e186d3b8882549db5c8476b737421155706d6f8b753323700492cec0c8eb07d1f0edd341aa0a50a5f29d6"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004007da8b570bacaad47b6c1c87934ab4542ed8629303b2fe2167f452fcd0451995619fd175b4b4d03766901be3baad5afb87ba3104584cdebaae6df6503c18af70793006b2f42d5f7b3a626d2745cb597ea86c95bf37c0609716e186d3b8882549db5c8476b737421155706d6f8b753323700492cec0c8eb07d1f0edd341aa0a50a5f29d6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAfai1cLrKrUe2wch5NKtFQu2GKTA7\nL+IWf0UvzQRRmVYZ/RdbS00DdmkBvjuq1a+4e6MQRYTN66rm32UDwYr3B5MAay9C\n1fezpibSdFy1l+qGyVvzfAYJcW4YbTuIglSdtchHa3N0IRVXBtb4t1MyNwBJLOwM\njrB9Hw7dNBqgpQpfKdY=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "3081870241433c219024277e7e682fcb288148c282747403279b1ccc06352c6e5505d769be97b3b204da6ef55507aa104a3a35c5af41cf2fa364d60fd967f43e3933ba6d783d024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040021664b32e13f605495abdc32094c61e78370642b4a8e66e316ae850d32107952198ff9e029777066b60b61b8733ea87495644cc790dd7b15ed9e952aa709d499230052ca4eea9d84e07ade2ba11b1f7ceb47d6b5bdc6dda6c1a903cc2ccab52c4b2d4311f28744cf6e660ef86775f76fc047ad1c08c10fab72d7ab61f5d83d01eae795", "wx": "21664b32e13f605495abdc32094c61e78370642b4a8e66e316ae850d32107952198ff9e029777066b60b61b8733ea87495644cc790dd7b15ed9e952aa709d49923", "wy": "52ca4eea9d84e07ade2ba11b1f7ceb47d6b5bdc6dda6c1a903cc2ccab52c4b2d4311f28744cf6e660ef86775f76fc047ad1c08c10fab72d7ab61f5d83d01eae795"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040021664b32e13f605495abdc32094c61e78370642b4a8e66e316ae850d32107952198ff9e029777066b60b61b8733ea87495644cc790dd7b15ed9e952aa709d499230052ca4eea9d84e07ade2ba11b1f7ceb47d6b5bdc6dda6c1a903cc2ccab52c4b2d4311f28744cf6e660ef86775f76fc047ad1c08c10fab72d7ab61f5d83d01eae795", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAIWZLMuE/YFSVq9wyCUxh54NwZCtK\njmbjFq6FDTIQeVIZj/ngKXdwZrYLYbhzPqh0lWRMx5DdexXtnpUqpwnUmSMAUspO\n6p2E4HreK6EbH3zrR9a1vcbdpsGpA8wsyrUsSy1DEfKHRM9uZg74Z3X3b8BHrRwI\nwQ+rcterYfXYPQHq55U=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3081860241433c219024277e7e682fcb288148c282747403279b1ccc06352c6e5505d769be97b3b204da6ef55507aa104a3a35c5af41cf2fa364d60fd967f43e3933ba6d783d0241492492492492492492492492492492492492492492492492492492492492492491795c5c808906cc587ff89278234a8566e3f565f5ca840a3d887dac7214bee9b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040197988b90bc98d04bfdd1aff24cf20528e22fee172ba943d88b8e1b1cd59e32ec51838fdd22e6cabba0b2e87f1153b9ba521d863050d14d838e3f77568daf73509a0046a063538fa6eceea045d0f6f2f9ebebd5187e1ebcbb2d762d89764fa17e15991935b57c606d6e0e1473830207ccdce9fc7a5644b9c559ec54f078f4ef53049c3a", "wx": "0197988b90bc98d04bfdd1aff24cf20528e22fee172ba943d88b8e1b1cd59e32ec51838fdd22e6cabba0b2e87f1153b9ba521d863050d14d838e3f77568daf73509a", "wy": "46a063538fa6eceea045d0f6f2f9ebebd5187e1ebcbb2d762d89764fa17e15991935b57c606d6e0e1473830207ccdce9fc7a5644b9c559ec54f078f4ef53049c3a"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040197988b90bc98d04bfdd1aff24cf20528e22fee172ba943d88b8e1b1cd59e32ec51838fdd22e6cabba0b2e87f1153b9ba521d863050d14d838e3f77568daf73509a0046a063538fa6eceea045d0f6f2f9ebebd5187e1ebcbb2d762d89764fa17e15991935b57c606d6e0e1473830207ccdce9fc7a5644b9c559ec54f078f4ef53049c3a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBl5iLkLyY0Ev90a/yTPIFKOIv7hcr\nqUPYi44bHNWeMuxRg4/dIubKu6Cy6H8RU7m6Uh2GMFDRTYOOP3dWja9zUJoARqBj\nU4+m7O6gRdD28vnr69UYfh68uy12LYl2T6F+FZkZNbV8YG1uDhRzgwIHzNzp/HpW\nRLnFWexU8Hj071MEnDo=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3081870241433c219024277e7e682fcb288148c282747403279b1ccc06352c6e5505d769be97b3b204da6ef55507aa104a3a35c5af41cf2fa364d60fd967f43e3933ba6d783d0242019999999999999999999999999999999999999999999999999999999999999999950e053936328c7855ffd6676d926e1e402fc4a1606e169fbefc595f4ba7605007", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04016ac1f5d968be279c2fe892cac70574297758dfe14e177fc5156c726c27a329227adf29ec4df435f68df6680fad993cb8417816406644eda75c0d871d24bc982fee01c457ad39b11fd7bab53bfa64019e0464b84300a27e5ad8ad676e0d57f6f4c198c64f9cad7dc9ad64a7e7d34c1f81cb9e11232443561acfe44c7676f8347a438e06", "wx": "016ac1f5d968be279c2fe892cac70574297758dfe14e177fc5156c726c27a329227adf29ec4df435f68df6680fad993cb8417816406644eda75c0d871d24bc982fee", "wy": "01c457ad39b11fd7bab53bfa64019e0464b84300a27e5ad8ad676e0d57f6f4c198c64f9cad7dc9ad64a7e7d34c1f81cb9e11232443561acfe44c7676f8347a438e06"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004016ac1f5d968be279c2fe892cac70574297758dfe14e177fc5156c726c27a329227adf29ec4df435f68df6680fad993cb8417816406644eda75c0d871d24bc982fee01c457ad39b11fd7bab53bfa64019e0464b84300a27e5ad8ad676e0d57f6f4c198c64f9cad7dc9ad64a7e7d34c1f81cb9e11232443561acfe44c7676f8347a438e06", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBasH12Wi+J5wv6JLKxwV0KXdY3+FO\nF3/FFWxybCejKSJ63ynsTfQ19o32aA+tmTy4QXgWQGZE7adcDYcdJLyYL+4BxFet\nObEf17q1O/pkAZ4EZLhDAKJ+WtitZ24NV/b0wZjGT5ytfcmtZKfn00wfgcueESMk\nQ1Yaz+RMdnb4NHpDjgY=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3081860241433c219024277e7e682fcb288148c282747403279b1ccc06352c6e5505d769be97b3b204da6ef55507aa104a3a35c5af41cf2fa364d60fd967f43e3933ba6d783d024166666666666666666666666666666666666666666666666666666666666666666543814e4d8ca31e157ff599db649b87900bf128581b85a7efbf1657d2e9d81402", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04006e11f2d1f855bb6e5c8dacc10256788eea73667362a4ce77a95630a7adebcdfe570c9eeb3d2c4837ec63bd6020426600396922eb210819acdf89c69d3792fe232d0096effa4c755ef75147841a90289b63930ea696e28a39278374949e7656f2f76fbb668571e00f81885331b5c9f8ad4e61446d14e2d0cfd584c92e2f9f75575acef2", "wx": "6e11f2d1f855bb6e5c8dacc10256788eea73667362a4ce77a95630a7adebcdfe570c9eeb3d2c4837ec63bd6020426600396922eb210819acdf89c69d3792fe232d", "wy": "0096effa4c755ef75147841a90289b63930ea696e28a39278374949e7656f2f76fbb668571e00f81885331b5c9f8ad4e61446d14e2d0cfd584c92e2f9f75575acef2"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004006e11f2d1f855bb6e5c8dacc10256788eea73667362a4ce77a95630a7adebcdfe570c9eeb3d2c4837ec63bd6020426600396922eb210819acdf89c69d3792fe232d0096effa4c755ef75147841a90289b63930ea696e28a39278374949e7656f2f76fbb668571e00f81885331b5c9f8ad4e61446d14e2d0cfd584c92e2f9f75575acef2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAbhHy0fhVu25cjazBAlZ4jupzZnNi\npM53qVYwp63rzf5XDJ7rPSxIN+xjvWAgQmYAOWki6yEIGazficadN5L+Iy0Alu/6\nTHVe91FHhBqQKJtjkw6mluKKOSeDdJSedlby92+7ZoVx4A+BiFMxtcn4rU5hRG0U\n4tDP1YTJLi+fdVdazvI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3081870241433c219024277e7e682fcb288148c282747403279b1ccc06352c6e5505d769be97b3b204da6ef55507aa104a3a35c5af41cf2fa364d60fd967f43e3933ba6d783d024201b6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db68d82a2b033628ca12ffd36ed0d3bf206957c063c2bf183d7132f20aac7c797a51", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040091454ea00df332bf33fe18dedaa20abaa330d95c4758c785186237f8e69d713563a457b4fa28863c0ea1f822f9ad6f3b678a8544a3c6337997c962f63943c1e1b301e120a9b5aa72a9998901e8db2cedff456071ce1702402d9292041b9ba4ce3bebd4498baa9708b73305eeec4722c9782d5b63259f9a30c821d1eb03513600a45f8f", "wx": "0091454ea00df332bf33fe18dedaa20abaa330d95c4758c785186237f8e69d713563a457b4fa28863c0ea1f822f9ad6f3b678a8544a3c6337997c962f63943c1e1b3", "wy": "01e120a9b5aa72a9998901e8db2cedff456071ce1702402d9292041b9ba4ce3bebd4498baa9708b73305eeec4722c9782d5b63259f9a30c821d1eb03513600a45f8f"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040091454ea00df332bf33fe18dedaa20abaa330d95c4758c785186237f8e69d713563a457b4fa28863c0ea1f822f9ad6f3b678a8544a3c6337997c962f63943c1e1b301e120a9b5aa72a9998901e8db2cedff456071ce1702402d9292041b9ba4ce3bebd4498baa9708b73305eeec4722c9782d5b63259f9a30c821d1eb03513600a45f8f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAkUVOoA3zMr8z/hje2qIKuqMw2VxH\nWMeFGGI3+OadcTVjpFe0+iiGPA6h+CL5rW87Z4qFRKPGM3mXyWL2OUPB4bMB4SCp\ntapyqZmJAejbLO3/RWBxzhcCQC2SkgQbm6TOO+vUSYuqlwi3MwXu7EciyXgtW2Ml\nn5owyCHR6wNRNgCkX48=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 420, "comment": "extreme value for k", "msg": "************", "sig": "3081860241433c219024277e7e682fcb288148c282747403279b1ccc06352c6e5505d769be97b3b204da6ef55507aa104a3a35c5af41cf2fa364d60fd967f43e3933ba6d783d02410eb10e5ab95f2f26a40700b1300fb8c3c8d5384ffbecf1fdb9e11e67cb7fd6a7f503e6e25ac09bb88b6c3983df764d4d72bc2920e233f0f7974a234a21b00bb447", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400d8926b405be46226d55f0c50c9fe7982ea9a4ce6dfedb745cd716912c6008bf0ff3705d5640edf04dc71346b6086b7bae476ee702908bb3f9a5815931e3d6801890116b45cf3739e7f3d69629e10f19f9f53c2d01f2284b6e98db0cd49f45887170ca0656d1c75d4505836ae3087e3c1187158a2774c46911361a34e5cd1e7dd9e4734", "wx": "00d8926b405be46226d55f0c50c9fe7982ea9a4ce6dfedb745cd716912c6008bf0ff3705d5640edf04dc71346b6086b7bae476ee702908bb3f9a5815931e3d680189", "wy": "0116b45cf3739e7f3d69629e10f19f9f53c2d01f2284b6e98db0cd49f45887170ca0656d1c75d4505836ae3087e3c1187158a2774c46911361a34e5cd1e7dd9e4734"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400d8926b405be46226d55f0c50c9fe7982ea9a4ce6dfedb745cd716912c6008bf0ff3705d5640edf04dc71346b6086b7bae476ee702908bb3f9a5815931e3d6801890116b45cf3739e7f3d69629e10f19f9f53c2d01f2284b6e98db0cd49f45887170ca0656d1c75d4505836ae3087e3c1187158a2774c46911361a34e5cd1e7dd9e4734", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA2JJrQFvkYibVXwxQyf55guqaTObf\n7bdFzXFpEsYAi/D/NwXVZA7fBNxxNGtghre65HbucCkIuz+aWBWTHj1oAYkBFrRc\n83Oefz1pYp4Q8Z+fU8LQHyKEtumNsM1J9FiHFwygZW0cddRQWDauMIfjwRhxWKJ3\nTEaRE2GjTlzR592eRzQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "308188024200c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66024200aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8c5d782813fba87792a9955c2fd033745693c9892d8896d3a3e7a925f85bd76ad", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04016559fc4580186d7c1e3e05e75305c6da336bc3c9aa8f999cbafc719d6f4dbf62ef91f1859b2f61463a77e43705208e34d648416349e4741ea8e5e779a37bd4a5be00a808ce498afde58cfbbf5dfb77e607ce294ce0d036873ead04c08d5fd1fd5d44fcf67e680d77727aad682a7a418065e26b2aeae17523cfbf50b0c178693eb35373", "wx": "016559fc4580186d7c1e3e05e75305c6da336bc3c9aa8f999cbafc719d6f4dbf62ef91f1859b2f61463a77e43705208e34d648416349e4741ea8e5e779a37bd4a5be", "wy": "00a808ce498afde58cfbbf5dfb77e607ce294ce0d036873ead04c08d5fd1fd5d44fcf67e680d77727aad682a7a418065e26b2aeae17523cfbf50b0c178693eb35373"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004016559fc4580186d7c1e3e05e75305c6da336bc3c9aa8f999cbafc719d6f4dbf62ef91f1859b2f61463a77e43705208e34d648416349e4741ea8e5e779a37bd4a5be00a808ce498afde58cfbbf5dfb77e607ce294ce0d036873ead04c08d5fd1fd5d44fcf67e680d77727aad682a7a418065e26b2aeae17523cfbf50b0c178693eb35373", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBZVn8RYAYbXwePgXnUwXG2jNrw8mq\nj5mcuvxxnW9Nv2LvkfGFmy9hRjp35DcFII401khBY0nkdB6o5ed5o3vUpb4AqAjO\nSYr95Yz7v137d+YHzilM4NA2hz6tBMCNX9H9XUT89n5oDXdyeq1oKnpBgGXiayrq\n4XUjz79QsMF4aT6zU3M=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 422, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "308187024200c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd660241492492492492492492492492492492492492492492492492492492492492492491795c5c808906cc587ff89278234a8566e3f565f5ca840a3d887dac7214bee9b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04001c31a43219d7959db2d48bfc30142d984303ac33b553b61906603916b9e09016ce6764c1816ac3a0ded1c5f160f66c9fe3bfe5fb5220bc4455c4bb2b568608bfbe01068731c675aab1443fc937440b6e2db5cca4695db6da63ec2fb94a0aca567b38e555383a0246bd397451b0a902cc147aad143454a1f0c1166286feca2bfc12fb7c", "wx": "1c31a43219d7959db2d48bfc30142d984303ac33b553b61906603916b9e09016ce6764c1816ac3a0ded1c5f160f66c9fe3bfe5fb5220bc4455c4bb2b568608bfbe", "wy": "01068731c675aab1443fc937440b6e2db5cca4695db6da63ec2fb94a0aca567b38e555383a0246bd397451b0a902cc147aad143454a1f0c1166286feca2bfc12fb7c"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004001c31a43219d7959db2d48bfc30142d984303ac33b553b61906603916b9e09016ce6764c1816ac3a0ded1c5f160f66c9fe3bfe5fb5220bc4455c4bb2b568608bfbe01068731c675aab1443fc937440b6e2db5cca4695db6da63ec2fb94a0aca567b38e555383a0246bd397451b0a902cc147aad143454a1f0c1166286feca2bfc12fb7c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAHDGkMhnXlZ2y1Iv8MBQtmEMDrDO1\nU7YZBmA5FrngkBbOZ2TBgWrDoN7RxfFg9myf47/l+1IgvERVxLsrVoYIv74BBocx\nxnWqsUQ/yTdEC24ttcykaV222mPsL7lKCspWezjlVTg6Aka9OXRRsKkCzBR6rRQ0\nVKHwwRZihv7KK/wS+3w=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 423, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "308188024200c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd660242019999999999999999999999999999999999999999999999999999999999999999950e053936328c7855ffd6676d926e1e402fc4a1606e169fbefc595f4ba7605007", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04014ac0270fb277eb45c8d3d7e49149ed14087c80c91b140e4fb1820ca4ef48b27e4266fdc1ac803c76e5f636fa4d4625bbbdbcf05729fcff600abab20b8d1dbc16b901a29aad56862835b6fff258a228f612a4926fa667587ad2f2ff3f5d63623870121195da66be32427f28a6493355590d7abf033594b2a1dc812a45e6c83c4c45e71b", "wx": "014ac0270fb277eb45c8d3d7e49149ed14087c80c91b140e4fb1820ca4ef48b27e4266fdc1ac803c76e5f636fa4d4625bbbdbcf05729fcff600abab20b8d1dbc16b9", "wy": "01a29aad56862835b6fff258a228f612a4926fa667587ad2f2ff3f5d63623870121195da66be32427f28a6493355590d7abf033594b2a1dc812a45e6c83c4c45e71b"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004014ac0270fb277eb45c8d3d7e49149ed14087c80c91b140e4fb1820ca4ef48b27e4266fdc1ac803c76e5f636fa4d4625bbbdbcf05729fcff600abab20b8d1dbc16b901a29aad56862835b6fff258a228f612a4926fa667587ad2f2ff3f5d63623870121195da66be32427f28a6493355590d7abf033594b2a1dc812a45e6c83c4c45e71b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBSsAnD7J360XI09fkkUntFAh8gMkb\nFA5PsYIMpO9Isn5CZv3BrIA8duX2NvpNRiW7vbzwVyn8/2AKurILjR28FrkBopqt\nVoYoNbb/8liiKPYSpJJvpmdYetLy/z9dY2I4cBIRldpmvjJCfyimSTNVWQ16vwM1\nlLKh3IEqRebIPExF5xs=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 424, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "308187024200c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66024166666666666666666666666666666666666666666666666666666666666666666543814e4d8ca31e157ff599db649b87900bf128581b85a7efbf1657d2e9d81402", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401c4984462ae091f6a3f81776997038fc47dece0dfc9abdac89bed4f6dddce8a676e8246cc3d92d8528281ab0c4a7b4f2a4d02327cc59739bb8e8088f2ccbe15034a0132bafddbb84aa5b8fb02478ad4c1b4d893224f0357d7dcd4713230baa635637e6b90e5910c128e40a32e88f1707319339db2a1f9774eef4c3de95583b14fdaaf9f", "wx": "01c4984462ae091f6a3f81776997038fc47dece0dfc9abdac89bed4f6dddce8a676e8246cc3d92d8528281ab0c4a7b4f2a4d02327cc59739bb8e8088f2ccbe15034a", "wy": "0132bafddbb84aa5b8fb02478ad4c1b4d893224f0357d7dcd4713230baa635637e6b90e5910c128e40a32e88f1707319339db2a1f9774eef4c3de95583b14fdaaf9f"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401c4984462ae091f6a3f81776997038fc47dece0dfc9abdac89bed4f6dddce8a676e8246cc3d92d8528281ab0c4a7b4f2a4d02327cc59739bb8e8088f2ccbe15034a0132bafddbb84aa5b8fb02478ad4c1b4d893224f0357d7dcd4713230baa635637e6b90e5910c128e40a32e88f1707319339db2a1f9774eef4c3de95583b14fdaaf9f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBxJhEYq4JH2o/gXdplwOPxH3s4N/J\nq9rIm+1Pbd3OimdugkbMPZLYUoKBqwxKe08qTQIyfMWXObuOgIjyzL4VA0oBMrr9\n27hKpbj7AkeK1MG02JMiTwNX19zUcTIwuqY1Y35rkOWRDBKOQKMuiPFwcxkznbKh\n+XdO70w96VWDsU/ar58=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "308188024200c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66024201b6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db68d82a2b033628ca12ffd36ed0d3bf206957c063c2bf183d7132f20aac7c797a51", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400fa43642508b0547ae7940582bc4baff4a88e26785ce904934b8246569fea474f1c5f91c2fa27a7a4e858089457399a41245ed61ca4c0a402c7ca89d9dc21e2f3110154f0d19aa4997cccb211a85f6717b54412179c2331e1f3998da55fb0bb6e2e53470332f6790f2ac036ded0352b2a33f14ea3685682aed64648012940759ccce2e7", "wx": "00fa43642508b0547ae7940582bc4baff4a88e26785ce904934b8246569fea474f1c5f91c2fa27a7a4e858089457399a41245ed61ca4c0a402c7ca89d9dc21e2f311", "wy": "0154f0d19aa4997cccb211a85f6717b54412179c2331e1f3998da55fb0bb6e2e53470332f6790f2ac036ded0352b2a33f14ea3685682aed64648012940759ccce2e7"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400fa43642508b0547ae7940582bc4baff4a88e26785ce904934b8246569fea474f1c5f91c2fa27a7a4e858089457399a41245ed61ca4c0a402c7ca89d9dc21e2f3110154f0d19aa4997cccb211a85f6717b54412179c2331e1f3998da55fb0bb6e2e53470332f6790f2ac036ded0352b2a33f14ea3685682aed64648012940759ccce2e7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQA+kNkJQiwVHrnlAWCvEuv9KiOJnhc\n6QSTS4JGVp/qR08cX5HC+ienpOhYCJRXOZpBJF7WHKTApALHyonZ3CHi8xEBVPDR\nmqSZfMyyEahfZxe1RBIXnCMx4fOZjaVfsLtuLlNHAzL2eQ8qwDbe0DUrKjPxTqNo\nVoKu1kZIASlAdZzM4uc=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 426, "comment": "extreme value for k", "msg": "************", "sig": "308187024200c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd6602410eb10e5ab95f2f26a40700b1300fb8c3c8d5384ffbecf1fdb9e11e67cb7fd6a7f503e6e25ac09bb88b6c3983df764d4d72bc2920e233f0f7974a234a21b00bb447", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650", "wx": "00c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66", "wy": "011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAxoWOBrcEBOnNnj7LZiOVtEKcZIE5\nBT+1Ifgor2BrTT26oUted+/nWSj+Hc<PERSON><PERSON>+o3jNIs8GFakKb+X5+McLlvWYBGDkp\naniaO8AEXIpftCx9G9mY9URJV5tEaBevvRcnPmYsl+5ymV70JkDFULkBP60HYTU8\ncIaicsJAiL6Udp/RZlA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 427, "comment": "testing point duplication", "msg": "************", "sig": "3081850240342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3a56f87db98089d208c89e902bb50ed289995ee7ccf6d6e6b1cec4aaf832d37340241492492492492492492492492492492492492492492492492492492492492492491795c5c808906cc587ff89278234a8566e3f565f5ca840a3d887dac7214bee9b8", "result": "invalid", "flags": []}, {"tcId": 428, "comment": "testing point duplication", "msg": "************", "sig": "308187024201ffcbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede3e402abcabb80e36ac16ffa82726f94af34218463bb8b8a7a21fdb3bba2ed9439e836c6f0e0b2cd50241492492492492492492492492492492492492492492492492492492492492492491795c5c808906cc587ff89278234a8566e3f565f5ca840a3d887dac7214bee9b8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd6600e7c6d6958765c43ffba375a04bd382e426670abbb6a864bb97e85042e8d8c199d368118d66a10bd9bf3aaf46fec052f89ecac38f795d8d3dbf77416b89602e99af", "wx": "00c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66", "wy": "00e7c6d6958765c43ffba375a04bd382e426670abbb6a864bb97e85042e8d8c199d368118d66a10bd9bf3aaf46fec052f89ecac38f795d8d3dbf77416b89602e99af"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd6600e7c6d6958765c43ffba375a04bd382e426670abbb6a864bb97e85042e8d8c199d368118d66a10bd9bf3aaf46fec052f89ecac38f795d8d3dbf77416b89602e99af", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAxoWOBrcEBOnNnj7LZiOVtEKcZIE5\nBT+1Ifgor2BrTT26oUted+/nWSj+Hc<PERSON><PERSON>+o3jNIs8GFakKb+X5+McLlvWYA58bW\nlYdlxD/7o3WgS9OC5CZnCru2qGS7l+hQQujYwZnTaBGNZqEL2b86r0b+wFL4nsrD\nj3ldjT2/d0FriWAuma8=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 429, "comment": "testing point duplication", "msg": "************", "sig": "3081850240342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3a56f87db98089d208c89e902bb50ed289995ee7ccf6d6e6b1cec4aaf832d37340241492492492492492492492492492492492492492492492492492492492492492491795c5c808906cc587ff89278234a8566e3f565f5ca840a3d887dac7214bee9b8", "result": "invalid", "flags": []}, {"tcId": 430, "comment": "testing point duplication", "msg": "************", "sig": "308187024201ffcbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede3e402abcabb80e36ac16ffa82726f94af34218463bb8b8a7a21fdb3bba2ed9439e836c6f0e0b2cd50241492492492492492492492492492492492492492492492492492492492492492491795c5c808906cc587ff89278234a8566e3f565f5ca840a3d887dac7214bee9b8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "04012a908bfc5b70e17bdfae74294994808bf2a42dab59af8b0523a026d640a2a3d6d344520b62177e2cfa339ca42fb0883ec425904fbda2833a3b5b0a9a00811365d8012333d532f8f8eb1a623c378a3694651192bbda833e3b8d7b8f90b2bfc9b045f8a55e1b6a5fe1512c400c4bc9c86fd7c699d642f5cee9bb827c8b0abc0da01cef1e", "wx": "012a908bfc5b70e17bdfae74294994808bf2a42dab59af8b0523a026d640a2a3d6d344520b62177e2cfa339ca42fb0883ec425904fbda2833a3b5b0a9a00811365d8", "wy": "012333d532f8f8eb1a623c378a3694651192bbda833e3b8d7b8f90b2bfc9b045f8a55e1b6a5fe1512c400c4bc9c86fd7c699d642f5cee9bb827c8b0abc0da01cef1e"}, "keyDer": "30819b301006072a8648ce3d020106052b810400230381860004012a908bfc5b70e17bdfae74294994808bf2a42dab59af8b0523a026d640a2a3d6d344520b62177e2cfa339ca42fb0883ec425904fbda2833a3b5b0a9a00811365d8012333d532f8f8eb1a623c378a3694651192bbda833e3b8d7b8f90b2bfc9b045f8a55e1b6a5fe1512c400c4bc9c86fd7c699d642f5cee9bb827c8b0abc0da01cef1e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQBKpCL/Ftw4XvfrnQpSZSAi/KkLatZ\nr4sFI6Am1kCio9bTRFILYhd+LPoznKQvsIg+xCWQT72igzo7WwqaAIETZdgBIzPV\nMvj46xpiPDeKNpRlEZK72oM+O417j5Cyv8mwRfilXhtqX+FRLEAMS8nIb9fGmdZC\n9c7pu4J8iwq8DaAc7x4=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 431, "comment": "pseudorandom signature", "msg": "", "sig": "308188024201eee90ae46276f5a4d085d97da8d3d73e3aa41e809aeef225fa7e1780128f43ddb99afd82aff727e7dacfa0f59b1023350741fead9de533527aa6ef6a3a3a285a6a024200b27e5ab4845f86ed525fac4e9e8500e56dd5a5161c02f0513393f4381a67ee307ef6516405445e931e6aaa3d7d3f969c6dd5f2044362304d112fa78c1956fe845c", "result": "valid", "flags": []}, {"tcId": 432, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "30818802420087b41aba38fbc1d3bed442302c6c35080808a772892d8d7cff937316aee26a78589562d6df69459426ddcc22eba2ad7b46f5d837364487d25064577c2350248f06024201e22bd1983a6da4b2ffc3002aafe484aef52a2ed9226e27c11a3e31a0f047a848a93e7383489cf305eba232b1f4daedc1db0606c198b95514cb0dd82596d055222f", "result": "valid", "flags": []}, {"tcId": 433, "comment": "pseudorandom signature", "msg": "************", "sig": "308187024140c2378fb645cb6892d3d78f11eba20a97baf8a78be3adadee1abb5d747dbfaea91d83276cd1430278c39bed88d720d6149932b29748a1b0a791048c8ab477601e024201866551c42bc508ca0be80cb459e5fc364c77b7cbe3a6cc95af31a10751240fa634ca1507884f9f88393000fabc8983c487e502a7837cb8f8a9140a1370774f8f45", "result": "valid", "flags": []}, {"tcId": 434, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "308188024201b4a3842c80a39f50a7de3cbd5676f7895a3047c833df0ff965820361ec0c42c3a3d1cc68b469ee43083371d83b49d72a94e525c1223690cef9eb1c5b49a546f92c024201a1b6d398ff656a7159b8d3393e14a17e03411d3ec7b409f68c88827b5e19f383843c198599ca4d22d6f81f7774b31baa6e95d5de02a31c7b56dce517460f603235", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400304b3d071ed1ef302391b566af8c9d1cb7afe9aabc141ac39ab39676c63e48c1b2c6451eb460e452bd573e1fb5f15b8e5f9c03f634d8db6897285064b3ce9bd98a00000000009b98bfd33398c2cf8606fc0ae468b6d617ccb3e704af3b8506642a775d5b4da9d00209364a9f0a4ad77cbac604a015c97e6b5a18844a589a4f1c7d9625", "wx": "304b3d071ed1ef302391b566af8c9d1cb7afe9aabc141ac39ab39676c63e48c1b2c6451eb460e452bd573e1fb5f15b8e5f9c03f634d8db6897285064b3ce9bd98a", "wy": "009b98bfd33398c2cf8606fc0ae468b6d617ccb3e704af3b8506642a775d5b4da9d00209364a9f0a4ad77cbac604a015c97e6b5a18844a589a4f1c7d9625"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400304b3d071ed1ef302391b566af8c9d1cb7afe9aabc141ac39ab39676c63e48c1b2c6451eb460e452bd573e1fb5f15b8e5f9c03f634d8db6897285064b3ce9bd98a00000000009b98bfd33398c2cf8606fc0ae468b6d617ccb3e704af3b8506642a775d5b4da9d00209364a9f0a4ad77cbac604a015c97e6b5a18844a589a4f1c7d9625", "keyPem": "-----BEGIN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAMEs9Bx7R7zAjkbVmr4ydHLev6aq8\nFBrDmrOWdsY+SMGyxkUetGDkUr1XPh+18VuOX5wD9jTY22iXKFBks86b2YoAAAAA\nAJuYv9MzmMLPhgb8CuRottYXzLPnBK87hQZkKnddW02p0AIJNkqfCkrXfLrGBKAV\nyX5rWhiESliaTxx9liU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 435, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "308188024200ec15522152bd082e8511a359e9c4fe2ec0e2e9971ad9368a35c3e9a6473c21d9597ef84d7217d38ffb090ebe308b6274deabc760ffd060d19d17e52cceaefd8a6a02420134cf6ed48b9873bf81ca9673aa8b0c06538fa999b59b73f7176339662f399278f3e9c70848c6e8f3ec639c287d21032a285310dfc5e570e861d7986b48139d9c5a", "result": "valid", "flags": []}, {"tcId": 436, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "308187024144a45dbf0c4d34cd9b7a612e8afcf36cf3ca269d64dc0d84b6f406b7c6781a02e24d5ebcb7d3595ae5a3c9f3a3fe18fba6526581deed81e4d74a1c77a85635a394024201e15d5a81aa0fbbb886830939e43fdec62cc3b64819e384663a51a1f193f0e16afdba98e1690f8ebb978a5684bd41e4dba7a74f21f71caab59d88afd0ef20946985", "result": "valid", "flags": []}, {"tcId": 437, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "308188024201acf1333a1d8860847e0b0f93b7822ad6c380d2aef10d2036294631a3cea154c9d06e980d0586aeb9f72766f50c35f707dedac887c88ce76eaf6b57a628af4e264802420168d686e958d31315313548734de94e45e47e836ac925b83ad0bfd919a87bcc09b3defcefd0c2f10b8ca4d705258f34eed5007ed72fac4c2cccf322d7f6f39fdb51", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400304b3d071ed1ef302391b566af8c9d1cb7afe9aabc141ac39ab39676c63e48c1b2c6451eb460e452bd573e1fb5f15b8e5f9c03f634d8db6897285064b3ce9bd98a01ffffffff6467402ccc673d3079f903f51b974929e8334c18fb50c47af99bd588a2a4b2562ffdf6c9b560f5b528834539fb5fea368194a5e77bb5a765b0e38269da", "wx": "304b3d071ed1ef302391b566af8c9d1cb7afe9aabc141ac39ab39676c63e48c1b2c6451eb460e452bd573e1fb5f15b8e5f9c03f634d8db6897285064b3ce9bd98a", "wy": "01ffffffff6467402ccc673d3079f903f51b974929e8334c18fb50c47af99bd588a2a4b2562ffdf6c9b560f5b528834539fb5fea368194a5e77bb5a765b0e38269da"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400304b3d071ed1ef302391b566af8c9d1cb7afe9aabc141ac39ab39676c63e48c1b2c6451eb460e452bd573e1fb5f15b8e5f9c03f634d8db6897285064b3ce9bd98a01ffffffff6467402ccc673d3079f903f51b974929e8334c18fb50c47af99bd588a2a4b2562ffdf6c9b560f5b528834539fb5fea368194a5e77bb5a765b0e38269da", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAMEs9Bx7R7zAjkbVmr4ydHLev6aq8\nFBrDmrOWdsY+SMGyxkUetGDkUr1XPh+18VuOX5wD9jTY22iXKFBks86b2YoB////\n/2RnQCzMZz0wefkD9RuXSSnoM0wY+1DEevmb1YiipLJWL/32ybVg9bUog0U5+1/q\nNoGUped7tadlsOOCado=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 438, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "308187024200a80553a081456c72e432aa6cf46a24571d9978e3af4f88ec8ffb2d5ee804b4128bc124ec1154e1f628f27f0fc3cdbeec39cc5baffb04ab112af1283865ec57790802417d31a19909b1b7a2314dcc5eb82388ef2e4e8acf0dc6de6d40cc3382c7d8b00227eb929b3b7d522438b0c4a652d1ef8eba9e4d4e91e5c7b3d7947fbc9640af1083", "result": "valid", "flags": []}, {"tcId": 439, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3081880242016ed83a23e3b31ee7591ad101167699e7f8d1c448ac2453ef29ac02928028b2d9119fa12ff5ce27f582642e7ea8fd4e991b5266caa9fd70079f5a23c06be1153f97024200bca1632542b3ef53951dc45e49c263ed3d09e2afa27a3bdc9f42083056bb3d65b8d4e95507a4f054a3abacf6f6f99234d86b698dd18c5d5bd3e9f86ac1b018a4e5", "result": "valid", "flags": []}, {"tcId": 440, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "308188024201eedb8deeb89366c66f53730afbce113ad37152691766d3e267c45d7ccd7430ddaa7ee5c870153776fce1deed2b7cb11fb06abf02d6f18c01e0b832bb97d7bb157d024200d9256266e952989dd515a8c7540049f9f8d66451b4580dedc736f6777e830216cb4c7bd9af029d4046f3ea747d5d104cad16f20e2e65fa15c7feea9c35b9a380af", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "040000000002fba6a061201ea6b1ed4265163568735ebab78600cdf6a71101dc63beaf546d97a214fc6396793b014eb1aa7a728f53deb2ff9999a3808ddfed15e9629b01993852dadc39299a5a45b6bd7c8dc8ec67e7adbb359fa8fa5d44977e15e2e5a9acf0c33645f3f2c68c526e07732fb35043719cfafc16063c8e58850a958436a4e5", "wx": "02fba6a061201ea6b1ed4265163568735ebab78600cdf6a71101dc63beaf546d97a214fc6396793b014eb1aa7a728f53deb2ff9999a3808ddfed15e9629b", "wy": "01993852dadc39299a5a45b6bd7c8dc8ec67e7adbb359fa8fa5d44977e15e2e5a9acf0c33645f3f2c68c526e07732fb35043719cfafc16063c8e58850a958436a4e5"}, "keyDer": "30819b301006072a8648ce3d020106052b8104002303818600040000000002fba6a061201ea6b1ed4265163568735ebab78600cdf6a71101dc63beaf546d97a214fc6396793b014eb1aa7a728f53deb2ff9999a3808ddfed15e9629b01993852dadc39299a5a45b6bd7c8dc8ec67e7adbb359fa8fa5d44977e15e2e5a9acf0c33645f3f2c68c526e07732fb35043719cfafc16063c8e58850a958436a4e5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAAAAAAvumoGEgHqax7UJlFjVoc166\nt4YAzfanEQHcY76vVG2XohT8Y5Z5OwFOsap6co9T3rL/mZmjgI3f7RXpYpsBmThS\n2tw5KZpaRba9fI3I7Gfnrbs1n6j6XUSXfhXi5ams8MM2RfPyxoxSbgdzL7NQQ3Gc\n+vwWBjyOWIUKlYQ2pOU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 441, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3081880242014dda0dcac3d018a298b7d6d76e3c16a0ce01088f126f413c113ee2eafd69baa0c476c84ca0ffa9cb7b28008b5ea117aabf71e5c147ae46a474303a6c5e967c7f01024201696a0251cc9c50b9ca9aed6786ca4f55d5cf866b0300eef84b3edec1c236890e482b942dc0e817fa23258da17e1d5f77d367f0ed1d5b25cdc483fae5863790e6e8", "result": "valid", "flags": []}, {"tcId": 442, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30818702414c5b2e5935dc9303c76b6b4cb8f30739b56fa4880f8fa58b5d4573cbd29d4cfa76239efd7d45fff8040b48d93867b8c81862f029c967f46667455ba715f1a5d15a02420168672a6fd60214647864a7d2715ce4890a1973c2fba1c02c3c4f3fb86e4eea946c46c09005aa5280f01c9be720ba37abd55c445cc1d9f5c3797fa27246d77e1c1c", "result": "valid", "flags": []}, {"tcId": 443, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3081870242016f2681a03e8d6cdefe9e3619397c6c951dfb42b04258f0222c36f810933fe04163a2c4cde88ce9c716b105e4d76a4cf8e84a1b0ade010b9943383b58ca4b03be98024150fa6b6be2fe3e66627a5091839753566ee40892af040d3840612b83323a0f0350ad22bc9611d41f8d43ed7d26be53220474feb8d34d01c7a51b17f41edbef5694", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0401fffffffe1d5d52b31ca52f8947a35593edf164cd324f833b90935846c64db1454df9f028dc8bc36bb04cb7f0cceceba01a3844097f7c35eeaa81428db0cca6333101b7c70277d0bf78a3c7b62c937f0cb2cad2565f5514f6205ceb1a193d4fdb45ba6e6cec07827bae0b16b8316c3539a15114d0de6d2de407fd7117551a70826eada6", "wx": "01fffffffe1d5d52b31ca52f8947a35593edf164cd324f833b90935846c64db1454df9f028dc8bc36bb04cb7f0cceceba01a3844097f7c35eeaa81428db0cca63331", "wy": "01b7c70277d0bf78a3c7b62c937f0cb2cad2565f5514f6205ceb1a193d4fdb45ba6e6cec07827bae0b16b8316c3539a15114d0de6d2de407fd7117551a70826eada6"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000401fffffffe1d5d52b31ca52f8947a35593edf164cd324f833b90935846c64db1454df9f028dc8bc36bb04cb7f0cceceba01a3844097f7c35eeaa81428db0cca6333101b7c70277d0bf78a3c7b62c937f0cb2cad2565f5514f6205ceb1a193d4fdb45ba6e6cec07827bae0b16b8316c3539a15114d0de6d2de407fd7117551a70826eada6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQB/////h1dUrMcpS+JR6NVk+3xZM0y\nT4M7kJNYRsZNsUVN+fAo3IvDa7BMt/DM7OugGjhECX98Ne6qgUKNsMymMzEBt8cC\nd9C/eKPHtiyTfwyyytJWX1UU9iBc6xoZPU/bRbpubOwHgnuuCxa4MWw1OaFRFNDe\nbS3kB/1xF1UacIJuraY=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 444, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30818702410f90341f535c12fc02021c81ebe2f9de8ca33bfe264c734bc946cfd82bccc7200a8b85cc0e3d31d84a002a962bee609813b486bfada692fd9385daa0af9a6d72d5024201b46889a27e6538ca39d58d1e1a097c8f1b96a3b947dfe28afeb9dfe707370478fa0bea1217e1030a5075582d90e709d752d8a30be0c26ac2b5f429360f5062e82f", "result": "valid", "flags": []}, {"tcId": 445, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3081880242008a51929349b7d9848df149e203a50d2c35a8a3ab12bfdebdf6fa6c45a05d75112c5d8f7cb60d615ee38eb4b98de1b8becf7e372653338e5db3c227db9edff36d79024201de6b675b626f7008bc0334a59bc163e64eaab23d75dc9adca3258bba2dea3cb4e21d062bc835a65f3182d86f1e0b5d3853443e362063d313afb3cabfe0035dd18d", "result": "valid", "flags": []}, {"tcId": 446, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3081880242019677f00b7d3182b2cd2ea615c6819864e9a37451564190a81d63947a6335059188d4b9a8cf9841500bf3eb49c09c4c4d330a4f32938ef7c7dd1cd4b80b76dcf50d0242018237771c8f4c00d420bc264078822ef2fba89f8dd65e0f8824cd89318a16c30611cecb0f24f142aa4c51c074dac7422d8cd78dd0ac40856940f81d916a55883784", "result": "valid", "flags": []}]}, {"key": {"curve": "secp521r1", "keySize": 521, "type": "EcPublicKey", "uncompressed": "0400c7c8817bf2f0652a4a4b5140c773e261080a0a111395856e8a3350f5eb5612bd63b367b965e92e9538ea3b7908aef1ade4b68e17f9f9148495c167d1c4dd4913490008bf0be2979abb8111fd0d768adcad774113a822c1bb60887053b5cf8c9563e76705a391ece154b5dfb114b20e351df4014bec19fa87720845801cf06b7fffffff", "wx": "00c7c8817bf2f0652a4a4b5140c773e261080a0a111395856e8a3350f5eb5612bd63b367b965e92e9538ea3b7908aef1ade4b68e17f9f9148495c167d1c4dd491349", "wy": "08bf0be2979abb8111fd0d768adcad774113a822c1bb60887053b5cf8c9563e76705a391ece154b5dfb114b20e351df4014bec19fa87720845801cf06b7fffffff"}, "keyDer": "30819b301006072a8648ce3d020106052b81040023038186000400c7c8817bf2f0652a4a4b5140c773e261080a0a111395856e8a3350f5eb5612bd63b367b965e92e9538ea3b7908aef1ade4b68e17f9f9148495c167d1c4dd4913490008bf0be2979abb8111fd0d768adcad774113a822c1bb60887053b5cf8c9563e76705a391ece154b5dfb114b20e351df4014bec19fa87720845801cf06b7fffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAx8iBe/LwZSpKS1FAx3PiYQgKChET\nlYVuijNQ9etWEr1js2e5ZekulTjqO3kIrvGt5LaOF/n5FISVwWfRxN1JE0kACL8L\n4peau4ER/Q12itytd0ETqCLBu2CIcFO1z4yVY+dnBaOR7OFUtd+xFLIONR30AUvs\nGfqHcghFgBzwa3////8=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 447, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "308188024200ebad623d0aee1d3f17a782297155018f848c38398211b0969ca0cc96771417d44e2894e9aacd44516c3188f7278ca6e5a13980ca943b8cbc8b75a5760b1b40de9a024200a4dfa438c8b53c33d5600aae164681b791f4e3a49cf82ef253ef2b40f2c361fbb42211cdafc5315f93937b70615e17a0b81960a894e466a17c9322fed55c7d55b4", "result": "valid", "flags": []}, {"tcId": 448, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "308187024113c33ad4251e52d9e6b12322a1794f4351b135c722b093dda22a1dcc885eaf3473e09af4307fc294db2d3f02649ad325898223be200c65a076d6fec1c4d054b0c80242009d78be212bd11cb5d36374a9743c6772196fb7d029462cdae5517a8ac52b42caabc5452de0a4904f05d5db20279e5663ad7cfe8f04efe6485a632458dd4ae1cb7c", "result": "valid", "flags": []}, {"tcId": 449, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3081880242019c5d65ec2257ece62f146d9c61661789151ccce3a045c60a03f2034eec2e84eb54414825529b9e1bc6c19581e3ab827166ff4e4722bc10447bbff9a5758839b82602420191e1bb04a72eac5dec7c021bae1fc37d9bc81e0acdd09ae63464009a01751394a8593084f634c191045a632073aae56eb65d88ac1ac6fb309dcbcf76f22ae652c9", "result": "valid", "flags": []}]}]}