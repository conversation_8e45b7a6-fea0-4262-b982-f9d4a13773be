{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 454, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "wx": "00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7", "wy": "00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6tqTvhCyRJ4ei7WDBdUgCAE8VxB8GiCj\nF6bLp+ymcjQMA9HS4JZjKGaR31UGn6JUkMndn5wLsrU=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c394766fb67a65fe0af6c154f7cbd285ea180b4c6150cdafafb0f6f0f", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021cc6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "valid", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "valid", "flags": []}, {"tcId": 4, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d0280691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab028000c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "303f0000021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0500", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "3042498177303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "30412500303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "303f303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "30422221498177021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "304122202500021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "3045221e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0004deadbeef021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3042021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2222498177021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab22212500021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "3045021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab221f021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045aa00bb00cd00303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043aa02aabb303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "30452224aa00bb00cd00021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "30432222aa02aabb021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2225aa00bb00cd00021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2223aa02aabb021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30412280021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2280021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30412280031c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2280031d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3041300102303c1c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c1c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": ["BER"]}, {"tcId": 57, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e00", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e05000000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e060811220000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000fe02beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0002beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "303f3000021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append empty sequence", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e3000", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3040021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2ebf7f00", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "sequence of sequence", "msg": "313233343030", "sig": "303f303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e02811c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02811d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f0282001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0282001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021d691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021b691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021e00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30420285010000001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3042021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0285010000001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046028901000000000000001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab028901000000000000001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304102847fffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02847fffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30410284ffffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0284ffffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30420285ffffffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3042021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0285ffffffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30450288ffffffffffffffff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3045021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0288ffffffffffffffff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d02ff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02ff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "removing integer", "msg": "313233343030", "sig": "301f021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302002021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "301f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab02", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021e691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021f00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0000", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021e0000691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021f000000c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0000021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021e691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0500021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021f00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e0500", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30210281021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3020021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0281", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30210500021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3020021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0500", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d001c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d011c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d031c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d041c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303dff1c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab001d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab011d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab031d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab041d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92abff1d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30210200021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3020021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab0200", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "30412220020169021b1c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "3041021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab2221020100021cc6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021c6b1c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d02c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf922b021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbbae", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021b691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021b1c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021dff691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021eff00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022090180021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3021021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022020100021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3021021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01691c723dd6a7f5d11b8c8e8bd08173428bc48a2c3f031caaec3bbce8021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dff691c723dd6a7f5d11b8c8e8bd08345fcca52a9b01748ca203383686e021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c96e38dc229580a2ee47371742f7da36054f46611d4da0c9a70206d55021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d0096e38dc229580a2ee47371742f7cba0335ad564fe8b735dfcc7c9792021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dfe96e38dc229580a2ee47371742f7e8cbd743b75d3c0fce35513c44318021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d0096e38dc229580a2ee47371742f7da36054f46611d4da0c9a70206d55021d00c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d01c6b899049859a01f5093eab0834104e71ff12bb612ad778fbda8e56b", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021cc6b899049859a01f5093eab08342d7a15e7f4b39eaf3250504f090f1", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021dff394766fb67a65fe0af6c154f7cbe11bbc0c7c488012fb1b59eb344d2", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021dfe394766fb67a65fe0af6c154f7cbefb18e00ed449ed52887042571a95", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021d01c6b899049859a01f5093eab08341ee443f383b77fed04e4a614cbb2e", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303c021c691c723dd6a7f5d11b8c8e8bd0825c9fab0b99ee2b25f3658fdf92ab021c394766fb67a65fe0af6c154f7cbe11bbc0c7c488012fb1b59eb344d2", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3639313930", "sig": "303c021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021c221a25eb9cc8dd66fdf156b2f6ab601ab6d9c509247f8de5d2671a96", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "33393439313934313732", "sig": "303c021c3b3008ed596b7fa276498def40d96b1eb2ffb731a44050ffb732e4e6021c6dbb08c56db737e9392ff4f3a54d8b806d70af226ecf413b3465de55", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "35333637363431383737", "sig": "303d021d00d1fe269c3061e4b94604e8d612d70887068cc7d5232cd5a9b72923a1021c3c1cbc027d33fb2451d52dce3a828a8c7ecc490a28a94e5e5bb2c4d7", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "35363731343831303935", "sig": "303d021c04586134cc679295dd93499311c4a8af37cb94dadbae18d8ee279b9b021d00bf9170a1b65b665664cf567d40a995ce252a23d6a9f962b05e364486", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "3131323037313732393039", "sig": "303d021d00c1f51009b935b4773374364ec3eed72a24b70926e0349c77862f3475021c46df3d98f104ba6602f8041a5bf5495fb240e103d1bd17f2fa878923", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "3131323938303334323336", "sig": "303e021d00e822242872f1ecf338a4f773df87b67e9b21bb283acac7d66b26551e021d0094d4e0fc3c6359994a6eaedddd1533f490f72ef85139f8d3b39cf07b", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "39383736303239363833", "sig": "303c021c7fd45528eb7bfc3710e273c4468f0b50ebf93f94cd0e7a602a4929a6021c46613dd1ffd85df8d71f3498001721fda4982c27a1c291359b05b1b8", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "3230323034323936353139", "sig": "303d021c36d137b69171a486933b50138d1db1842724766afd25c85b0032daf5021d008e700de21f2fc350a34c7cc19054cf371ecab6f7331ccecf68fca0f4", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "31343531363639313830", "sig": "303e021d00da3b436908f5a82f26bc17a8577ad2a782946e3a7587b01d253b1dd0021d00a6544e38f24e8117370c049b5d1f6712ea14337a94511224df4496a3", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "31303933363835393531", "sig": "303c021c4314a2bd139d47be3d9fd9ebdd72a06a220219c7596b944178ee6f5f021c0e6f1d2f57c699654e9c705d7b8fa3c1ccb0f939f6368bed246b2e10", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "36323139353630323031", "sig": "303d021c6a25643464682679d84970c603927f4a8ca83e7ef9715dd1ed84c28f021d00932b78d165c225a5253e6201c0b1ded0898ba24de44b23233eb78054", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "35363832343734333033", "sig": "303c021c476aaa58677d9e60477cffd026c43248e2cf3cc21e8fdccb75ceefad021c7799fc7af8f9b929203faf899bb5ca1aecf2492555157282dfde790d", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "33373336353331373836", "sig": "303d021c63a98614a1421e2ebb278de53b61618bafc757122647affd358c667a021d008edba806e0a7e438ca35f98405a8ad2d5c3e8cc2d5c4384233aef0a5", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "34373935393033373932", "sig": "303e021d00880b5238a014f8b44655b83c175880eb1e8307899a824ea3e07dbd6d021d00a4724c8649fd74e5bc8d7fe6a9067a1376fb8e08dbdaed68980b0f50", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "39333939363131303037", "sig": "303e021d00f8743588234634dd9891f4f2f40f4e46b77f97b82dc5dbe234aa6b5d021d0080656e5262bc25e158f3b78f51ae0d6a41cc8cca1aa457221b2eb7fb", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "31303837343931313835", "sig": "303d021c2a2357e3d8fe34434582be4dabd58b77b388d1d52adcc664f45dece4021d0094be3a369b7c2788df4587ec5bd4163c4cbc40b77de1a85e8bcfb251", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "33323336363738353030", "sig": "303d021d00b6b0c0aba6c611300ecad8816242c415f183a2bd4d46cd7769033d9b021c7750b24be02f22dc0b656fe4af377413f9453dff99226915dbb6e08f", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "31343438393937373033", "sig": "303d021d00a5c1a75c2779f3eb83a65e295927cce4288e9d5c2132a4c7ca92526e021c10fe30f0be33a785385137b57d806140a402b9bd3c1df1b57de6da63", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "35373134363332383037", "sig": "303d021d00b92b5521d1a7abe77e8524dbd3001121cf83c08017e3917bc58b5d1c021c224b113779017f6a522171edf930f1b5d4f5e7dedc6d2d514fd7883c", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "323236343837343932", "sig": "303e021d00ebd3ecf3aa64cdcdd171585a141a4a673a8d5de0ca087dfcdf62432e021d00e0f1a0f7b8f5ac4a42632f87156ad1094079393b03f2051a9fd60249", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "35333533343439343739", "sig": "303d021c6c3854297e1f267b64a28e0cd6148e0fadcf85bc8d5c23947543bcb8021d00aa0594ee11312f5d4767d296e5ca83df68072811f81a8d27e178ca5d", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "34373837333033383830", "sig": "303c021c785ac8c956d7797ae67498775b3c446c41735eb15f3430b49f6a09f2021c5710879ab83994e809c8d2cbd6f2ac5c205b4b8d6226e98be03e7967", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32323332313935383233", "sig": "303e021d00f1f3d016693125ba73981c8f9a1748e5dce1d9634355f26fa536190e021d00b574e97def60dcd0e9177106483791b2edb4ab0342b9f5ebb957d5b0", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "3130373339333931393137", "sig": "303e021d00e64f3371522cb1a5f0d1511b152b20e01deca0b3284786853cac279a021d00c9a2e5f4ffde22b9d4ed0179ce74fff408ea918dda7685c7980ae61a", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "31383831303237333135", "sig": "303d021c1f99dd6ef72feeeda6c123baa4fabb126d7dedb64130fae3f4230797021d00e441ec51dca6271b043e95753c4043d7cb4e76fdc13d6aea45fbf243", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "36303631363933393037", "sig": "303d021d008637a09627c52766bf96f0b6cea6f2ac3eb303001c5f6fe6d628e4ba021c10b66c599455d40077bb865ed43e2c2cc403473baa6d63b16be48c84", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "38383935323237303934", "sig": "303c021c52a010a23e4f9ebb459bbe9f3057e6c19761fb99d25c9b16b8f007d8021c526dc1f34444de00447ba23c76950f2c159579d548b6335d127ea321", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "31353830323334303934", "sig": "303d021d00fc49caaada528f3357e5a264f2e7f970ca1b15ca5fee28741d1202ac021c175e884d10d0bfd20b39311ce2c53083da167d1f3dfeb990e59ed628", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "33393635393931353132", "sig": "303d021d00d95d539a85c9edacd4e02ede27b0e0b737099612d166c637c83a9f34021c59936a2b90b7f3f3da83f64dec8e347a3bfa57baadf9acea18c071d8", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "32323838373332313938", "sig": "303c021c1895e65593d71e5635cce022dda19bd155bb9a7f2e307e5ce9127ade021c121b487c320c544dcdd471d46fcde2ce5dc9d17fda70544c4eab50a2", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "32323330383837333139", "sig": "303d021d00b5f4c85b13b51a5da81a236f1105937f3d98856d2aeb57101b6b499c021c3be74ae770fa6467f76a742eb9e504a815a4a60e74b38bcaa89f9b06", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "313239303536393337", "sig": "303c021c07a57197667a0c40423d4811ff96384c9330467e8a28eaa4c0d519f4021c011062c8694494baaed24ff610e1e4227efb59a163c33fafd40100f9", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32373438363536343338", "sig": "303c021b7f718615ba1d0a9d27a8c5a678a6225ffe1233ed8b417c9343b310021d00cf6a87e4496725c6a2612f4034ddf4b31c7435e2fc3a163e92d463ba", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "37353833353032363034", "sig": "303e021d00ba8f95a4363c493a9f78bb61dbefaa9587d028bb8344420de2b0cf21021d00b3798c2d6e27a2591c2edc18320b78bf11df194b11b3fb498c595232", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "32333237373534323739", "sig": "303c021c596b74077801db2e889d3b2aaa3990fe180abc152d48528385ca955d021c38bffd416f779843fad389750003eb0708112a4834c554f476a3e0d1", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "373735353038353834", "sig": "303d021d008547f62967523a06c9690e5ff66c3f2254cda28f09ffccc222433d39021c3d9ebf664ee551bb7b33157d6c6c5fd456bda3d4ae460215ec1a5f94", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "3137393832363438333832", "sig": "303d021d0090ee3fab9c6ce373a1b35fc135fe878280ee25e58a4bd7529e91b4f0021c6451e7526505b44e88472b46eda3fd2679824dcdfc445e67f35ea382", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "32333936373737333635", "sig": "303c021c0a530530b6a9238d2d1a3cf468986c87f3b208f61ea0486d53140c17021c5f027a73f31a5cc2bee81ff0019477c542fd782ecde0e551fcd37e93", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "35393938313035383031", "sig": "303e021d00beab4abd23df5e2acfff07c82e245dfa7d587d0238c2c9ab9c88a96a021d0098c6507635536840edf604f9baae6408ce4d3fbee694db3abd825011", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "3136363737383237303537", "sig": "303d021c3ec8c36335cb98fa07b983c35b7fc256f44a5aa192d6087595145a15021d00c32b7a47ac6271f4593562bbbf91f9e07395a5e4d46970495cf29f05", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "323036323134333632", "sig": "303d021d00bd635a741f1f2a1d9ac1698baf5cfc491d5e3f8e15f1cacbe4ffe4dc021c4bb606cf7cc11d0d7d96b83966f42276095ccc445882ed5afddabf1e", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "36383432343936303435", "sig": "303d021d00812c08622c0a09d949b7628585c4f4f2db4c5591b5da148ff46d5cd4021c2104f9bc9d0079acb3077d2db58f388119500c5322cb9b5389b5c5d7", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "33323639383937333231", "sig": "303e021d00fa4e1c8b0006f269c855eb495fa3a113f643fa8b1fef2b08ab145994021d00fe85b8b522c7f9e8943e0f62643395bd1fcdabc892c315d108b75f65", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "31333837333234363932", "sig": "303e021d008c1d9b7911bacb6b4a09582648b992d46a1832eb006178c0c77fcb10021d00becbe12b99f243766da5bdad07461b9226a8298672b4f1adb35357ef", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "34313138383837353336", "sig": "303c021c78850a40530aa258e478e7c547d3a5e4944d3524f1676443e4dfb37d021c687058e1ca478f52a30c9a3f8e2eea9d8c40599cd47ef66b9430d17d", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "393838363036353435", "sig": "303c021c066e7268a6abefe1b4b916ca51c3e958e40dc78c3661313e0ed2e77d021c6404d8a332a837f2ab6bd13e3ee4aad1e9307d449e7f9b7d6332030c", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "32343739313135383435", "sig": "303c021c4eca73709a67c41603ca5af494c8132483ffc2e0bf171b52de5a5e81021c2c79137cd2add3ce3a76792270e347221a3ad786eafc2682b39bcf95", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "35303736383837333637", "sig": "303d021c0178512f8844984222393a63263e0a009601869e632f07eb7470aa05021d00e32657cded1122cee0a4f66ff50a32da1f05de4c5e217acdf5eb6fe2", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "393838353036393637", "sig": "303d021d00e2c7bf1222ca23a56492873c2d3fa6c7030cc166d693142dcea272b6021c715a4c82fda4404217dea6c0bbf3ac24f8faa2b435fbc6d51a32c4a8", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "32373231333036313331", "sig": "303c021c49886a8c26c91d649cbfecda6ce8d1441e764c66f5d81dceedb6c5ba021c4370d8bcd4f052fac9491d62850b6a6a85d5acc44d9248c3dff30bf2", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "33323034313031363535", "sig": "303e021d00e1ae225e1aeca40747ff3e7ad1f75eb9bc90d637160a7f58ce12e591021d00b97cbea3a9323110315760b7e2ede496514b30f0eec521ffeb07a634", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "33313530363830393530", "sig": "303d021d008a93b87b46512544fb9a7af5c41e3aa72e40235ef87ccb7108daae48021c157db617ac697df407af7a11626c52a1af7ef189514da39918c43010", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31373237343630313033", "sig": "303e021d00ebdebe6388b9f460fce6d84faa67ded1e864ef09e77ea3ce58a5deff021d00be5052033eb40380c2b1325fe97dcc55841e147a89f02a296b4505ef", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "3134353731343631323235", "sig": "303e021d00e85d0667972d003c82afb9e18b702357119b4f38401a5ebdfcbea88c021d00eb7b3e5268a4ce6280f72d7e9a3d74e5cac50b1c3a5296cdb5a49d82", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "34313739353136303930", "sig": "303c021c3d243581c0874fd4eb4d80f896c5067429ad455314881951ab5ec6e3021c0ec47aba08ccba88c1a6ddc289f595bda08dc2dd34d12dcefb68094d", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "35383932373133303534", "sig": "303d021c75c966bbdcef9157d47a134231229f9f5ee8ce458775fc747d4509bd021d00e344fa716e2088d95a55d02a978a416da10f22a5cccf35a2863227cf", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "33383936313832323937", "sig": "303e021d00cfdf599e986d770b73784d97149f9945fd16d22c302bb796156e7fb4021d00c6409785047b0083f008771b40db8502583208b61c8984671acb0929", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "38323833333436373332", "sig": "303e021d00c53c4aeec8f2e7a5cc0e885a6031aa1a6c1b7b7fec83b5084cbe291f021d00b0e6d10a8fd86f958c3b0f4662ed8ca0d6eadbc892aac4200fcf8315", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "33333636393734383931", "sig": "303d021c2386550d6e75350bcc32507bfc9beb9c26d72ff82f671c1f5282e98b021d00a55b8de808c4359fb529b80a80d9fc6eddb5ce08082c3b677c689991", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "32313939313533323239", "sig": "303d021c1fbd192d955ce02b64a3be5bb21bef22b53a6c6f9576d8f889b09e4e021d00f5a9b673a4ee5aabf1ca8e8289f25b62a3e08b956f7418c03e2d3031", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "35363030333136383232", "sig": "303d021d00b80ffba451db9fc2194e450bdd43bc0f53a7d0f4a78900c09fb8d9bc021c0124eeeab9035b6c845959e70b04d1e187d554807d6751afabcc1802", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "383639363531363935", "sig": "303c021c187fb026ade3ad16dd4b2813e8ebda433cb6cc3af1615bedf486a9e2021c6fbee53fa884d296f34f7719f74919434d1b7090c485eeed2fb8fd6c", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "36353833393236333732", "sig": "303d021d00e598a16fe12da79e9814f6985c9a9334010f287dc9e38de857ca5fc0021c19e0ed54f0e08ad091a163b4c7b86d0634da2c86a7a8991f5d8706d8", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "3133323035303135373235", "sig": "303d021d00b31a10480e397c8aa46f52a0f2fb5c22ebc0534fba156718b50cf6ea021c602004df4b47a2065130ca3b05f1eb02d0b37b79b04b1eb799408346", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "35303835333330373931", "sig": "303e021d00bc47e242d19dcc6321913980d73923e430bc6623d219529d586619b6021d0081397dd2f52811b534ed754a937d904f04a7de278fa3bc8926de6946", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "37383636383133313139", "sig": "303c021c5be0e0dfb26b1caa88f866504aa8e76f035a82abe00028d962bcfafa021c3c3c1df06026123471bed324ca79c51b28b3d10b1ce877cef21b852d", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "32303832353339343239", "sig": "303e021d00fe79d0cfe455724792cb5ab0580ad4f2918c1403ec12f0bdd2ce6528021d00f1357cd4afc402994ab868b0163f41701e0f00e561fdd97e0db6f7b9", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "3130303635393536363937", "sig": "303d021c1858c5d857124cd703e7c2f5e99d5025d6d979539c6f50b1d00fbd34021d00d94a5adb6d9c5001162620415541d49334fb929bc86a350ca4591195", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "33303234313831363034", "sig": "303e021d00e6b2ec967cfa25f57234b9ef1d87c5945502cbbd5831c862f00774d1021d00caea26368bffc8e562b2bd03aa6c9dc41c529659fefe6597fce9cd9c", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37373637383532383734", "sig": "303d021d00a59b438b2472074a93a289b33f5b13e604977dd3ab4d744d08e1061b021c699574a17dc8c7298c9321ca78552e5128ea801d056f387ba42f7a09", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "353434313939393734", "sig": "303d021c748481709c6882c4a130193834a57f4bc714906211ec6cc12c400dff021d00eec6c9d5a06786f821a8117eec3dc025ed3ac74e39e98a16a4aa285c", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "35383433343830333931", "sig": "303e021d00bc8991b506997403e123136a9c140a4336364733b0815f40d1dbd5fe021d00819503ea3b4c07fc157f948f6949705d560a881fc1c6af4b7391765c", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "373138383932363239", "sig": "303c021c1caece75c8e31bb0c5cceb0842f23683b8557a97036574ea88ceeabd021c645ad3276aaee22b693647f00dce5f91a03b678b789b667cd3b8e751", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "31373433323233343433", "sig": "303c021c3a7d582068aaecaba945203bc445b3312e5cb40886522987aced68d0021c39b3c612b6743a13bb2ffb83514d690cfcb9a7055e3a993cb0863938", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "32343036303035393336", "sig": "303e021d00f773c49fd0645716d16e559e22c39101df266cdfa7cb61ce46f85280021d00df6109fd77a241031cf03b376e001d8a3cd2a6b646edbf9e578133f1", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "31363134303336393838", "sig": "303c021c79cf893f66f7faa5ca08553ea03456107e7bb391a5e51260cedaea84021c32e8e3509468da7216c59975d4f3d5493848a03f864b2332044e68d1", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "32303935343235363835", "sig": "303d021c025ecd1a7ab765fbfd25a6d7cd3c461e17f465e6958bce9f492b7a5a021d00a1ca95038603d302761e416935acbd6b716a316c9b79c57d4053cb79", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "31303038303938393833", "sig": "303d021c3d14a4c21ba4dbd338fdd8b15fcdd0a9228f157cfaf2b09dd4f2aa67021d00e1640e8bd2a6110dc18d6f290b7325814710c0dc88b76f127c5e9e21", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "31353734313437393237", "sig": "303c021c258dce916ef78b9d8a87beaf6edd35bcccc08c5de488586e1b7b749a021c4ff500db4d665c7062179c099b2985a814f99fbfa44a3a709024d589", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32383636373731353232", "sig": "303e021d00cecf0aec5357749f357c459575298a3384dc4ac381438ff99acd9993021d00da7adb092a6890e0918c235a62d4a949b0cae5e57856975108fb2b91", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "31363934323830373837", "sig": "303d021d00d77f2e547fd68d5db314901da1ff7ecaf3d0c17ec047a974a7cec33e021c443a97afdf882272bf0233c8c4a8d23c9352ad89b1770c26240f6650", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "39393231363932353638", "sig": "303e021d00d5dcf93e6e1b93323ea2642d3405a7423cb04f59c03420193f394886021d00ddd5842e4928ee4b5d77d43d4a4bfc7f991c899727b75fc941b52995", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "3131363039343339373938", "sig": "303d021d00a9bc3ebc6ee34421326711ce29518d02bd403ead806a3e4502efa0ce021c12610b89a61689a8eb6e062d2524278155fe499ffecc0e0d940d48a7", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "37313836313632313030", "sig": "303e021d00c703c508784ef71b596dcd61c5b01b45c6c69d2b36a5a3b7701e5976021d00f05444a777204118f3ac2afc92d0212831bf7002158e7c656f4c07db", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "33323934333437313737", "sig": "303e021d0080674b740b64d383677c049a6f4baeb214f4a6b5933033853e634578021d009b3a804c75ed790e31966bc25730b7428af8c73c65fb503c06c597eb", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "3138353134343535313230", "sig": "303c021c7ed658c30f4a0dcc894c39f9320f59a185509ffee45eac6023577c7c021c47ac94a501806d5adffea9fcf3ccd8cf79f3cc47eca9fe38fc4886b4", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "343736303433393330", "sig": "303d021c397f669cc399a91da96c16efd233f6fe60d1b7caa397cc257843b30b021d00f19375fe66eae4738ec9dc5b7ef51cb33d4cb258f36944d37dd245cb", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "32353637333738373431", "sig": "303c021c537ec369b3f0d891e166f6c74e5d73dd2c4822210c5fe5f978c34072021c0b183c48b5f6e69245cb76e1e2c39663eedfb74ba9538739ac495ff5", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "35373339393334393935", "sig": "303d021d00d0ed7159cc3a79988f3c279287ca8ed10bb8f02c8b5a6204aead1223021c75ee1e5c00e81899bfa8545edcc64fdf707dae1f61d976d2f0883777", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "33343738333636313339", "sig": "303e021d00cf43329a9781db8044a920758e58399866fe7748c0f5d6a3bcdcbcbd021d00d9740d2dd716290ad4160345bcd4af03af01c44b610b1e5953199075", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "363439303532363032", "sig": "303e021d008ab2e92c8c9143f9d8da3bdb1d935cce3ab60ae99b3ccfe863b15d14021d0088c89302e8a9c591c6ed16b1ae46f966004d0b2685449842e291d742", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "34373633383837343936", "sig": "303d021c04f60f8450b448198cf7981116de06d4c4888cd26be3a5947092238f021d00cb23fcb33c14f089c2ae030146d68fa65eb9b086fa792f95be8ecf35", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "353739303230303830", "sig": "303e021d00f270f7a70a96a0f916c7530c6dea7178e6c087ddbcc60aacd8a7c553021d008b2c378554121365a180ad4edf1a12e566ba55eeabf525356783e603", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "35333434373837383438", "sig": "303e021d0085ad01b236ca4a5451969242e16165d322428235a2af8fdcd6c4c7b9021d008eb2998c5e0aaf279793caff59a266ca2068d94ebf77bae81fd0fb6a", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "3139323636343130393230", "sig": "303d021d00cffdb8d64b5b84b490ff73d77e51cc7797bf67c5ee0a4999def17230021c3baf4b34e1a35e9606a460b395063a554264a9c43cc302ab5abf473e", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "33373033393135373035", "sig": "303c021c66cda58a5a6ddb9476e36dbad5df542be88d7e447bdc3dfe1d9e8b2c021c0d99d387486a964ebab4e29bad583e46a5a200391d1065768a4e35fd", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "3831353435373730", "sig": "303c021c3200761902825bd353908accd2be6b482645646971f96dc490706a37021c3ed77899efdbe418370fa7998df3b7c924bed6864535277f805c894f", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "313935353330333737", "sig": "303e021d00ba0eff0ee46aa9fca5ab8ad64aee4037931d3ad0b953d404ef9f7bdc021d00afdf21df0dcbe39c2f5fa9ef7e1a2bca87d1213d1eca438929ad8982", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "31323637383130393033", "sig": "303e021d00a20c6883fc6ec1ca4bb378ac88ed670a742a6284113d5fa3182a1858021d00e0a73b913b94163175d264224cc70736f2fb8e8d58e914b18c921323", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "3131313830373230383135", "sig": "303e021d00f2f4af956b0c5409949d377e9bc68e4f1abef7969b518f8beacf27db021d00df3a7b5993d2393ade70a2cfc1e8671a78ca4fecb56425a661a2d2fc", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "38333831383639323930", "sig": "303c021c331a1a553494f8524adb4e8a722d558965fb703ae432bf3cbdb255c2021c5ab6e3dee6a2516fc4e0ac88e6dfc81d2bc37c98949cc03e521d389d", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33313331323837323737", "sig": "303e021d00867135558e06e19796ebce8e3555c607a6607d46f7c8da6b8552ffc1021d008e827e8b9a4f74efeec7d7ba5c23428fde0227df55a1efc179a353b1", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "3134333331393236353338", "sig": "303d021c6746903ca095bfd3f6378a70092723483ca190b2392d8b1ad337969f021d00f33bfae0835c23a80ec9f33ce9a9035c192836a0b2fadd347d803f96", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "333434393038323336", "sig": "303d021c7fc0d8739ecfe349e506e71203a6e60e628a1bb0c67d5e574cb8831c021d00cf8bb1557152c57550a0fde6571456fa752782f7f92f7bb235dde39f", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "36383239383335393239", "sig": "303e021d00b4486e3139e0b1542892db3d3f51b0524894e19cb00cd07b03ee9c97021d00ad9728d77a8b7b4fa435b3345847860c332d65d8152aa6503ab18755", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "33343435313538303233", "sig": "303e021d00afbbdc8e50e801ecbd2e3705079717f4f9d69f3b3d85215aeecb4fbc021d00eceadd4e2cc9cea10b56d16a03fa551fec3eb808bd8d9f0926d14ed3", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "3132363937393837363434", "sig": "303d021c4a762f7d146f9eafff5ad11a6978260c818b801c3488dd60411f5cf6021d009ea77512585620ef2cfae8b8c9d8171229a32197e1949561bb75a049", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "333939323432353533", "sig": "303d021c227fe52b579833feee16c287d29273e2256df68aff0b94d2752d877b021d00bd79935e5faa8e9356622fea0135ecf796daf60333d5ab125f71e512", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "31363031393737393737", "sig": "303e021d00cd5365983eb165db39ba0c66c3a45b2ce1370c9ad14a9aa76dd4633a021d00a8c77ce42ab1c888a6b5d04b71139fd882328622e15e80252e5cf7da", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "3130383738373535313435", "sig": "303c021c54d6d44373f7dfc98455a22cd39a0b320fabc33215216b37365b5a16021c29cc690f2467c02e07bc416ad47204975af8c5c3346973f2b03ded3d", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "37303034323532393939", "sig": "303c021c2f5048c9ef9f30da7cb3fe4624552200f9e57a46d79db0484a0d9cf2021c06dad3a4682725852869a1a459bec865661e1a38a9e546eeaac7cb84", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "31353635333235323833", "sig": "303e021d00abbf0a02332fbea779899d31d3abd2d22c9c02d4058ced639bf06c45021d00cce0570f3812e5cfcb23376c554c7fc35dbcfeb623a7958c664ac6a4", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "3233383236333432333530", "sig": "303d021c1c30cb8bc21087b77eb1216ee8629e3676d925f1ae15077cc631da4f021d00ee998157bdefb77d1044e983a6afec7d91a23d95c937fc5c6548c989", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "31343437383437303635", "sig": "303c021c43ee11a7ab62e2125e765c2ce5d4f84704183539810512268d87f195021c65897e54025777659ee802b39c6bfd5ccc5706a9d1b38f95c078abaf", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "3134323630323035353434", "sig": "303e021d00a1fe3f4d3f43aaa3dcafa79ed99fbc045c11c352caacd89f0f63847e021d00ca2e37bd2c13b9fb3f8a55b7a67eb034240395abd39fecde75141336", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "31393933383335323835", "sig": "303e021d00bd290286ca08485ea4137010c67203c2455e7b669d153c6be40087c7021d0097dd7502ba3637f33baea5b2398647ad24c0fe35072bd963149b5aa0", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "34323932313533353233", "sig": "303e021d00c917269a5a4ce80b7fe54a8bed49326b50527a4d2fb0a3093182b5a5021d00a195ec0e69e3172e854e87dd651b44433fcd7dcbb7bd59515d2afe8e", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "34343539393031343936", "sig": "303c021c0b7b5aab8364dd4b11001a0b986d5aa4fb61ee720237417a7f63722f021c7f13b411e645e819fed1b925ebe807d9560b44d0ba1b75bd2fbd1294", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31333933393731313731", "sig": "303c021c505b974f8ecf07b60ffdbd2b2df9324de92b39476eb763a4c25f126a021c1c36ed1dee772c724205f717c383f49a87a5bc3caa0ef81360f9d800", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "32333930363936343935", "sig": "303d021c24219e49b98a9b64e56d21c908c870eb88b447d9f1ddb735083d6df2021d00bc4d7644faeff1e134443b2bb3bb2a20e2a4a7c193180626127ce937", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "3131343436303536323634", "sig": "303d021c083246081cf2f8c5e1cd42b60450fc6cac3b0ab03d38bdd271cd7370021d008d117ec32dbf939394499f7dbc2ab77290e9222d6d60ea02ce45c58a", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "363835303034373530", "sig": "303c021c24916961dd0d168c2878ca4fd065b81311c03b7f23f8416f4a23b14b021c1e37e3c03b2333b33bbb2ebe05b031042af19315adfdccdfc8d078ee", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "3232323035333630363139", "sig": "303e021d008df5468b123b92477a5c57ea86c54c5c9e41d119370dc18922aa8303021d0086bdf06b75f4d49d02c5806926f5d01b1a4f6a8146664a03fa820772", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "36323135363635313234", "sig": "303d021d00f65bf16f7ced97b0cdc22b08c62ef811306813134b001bc51140e828021c3a9b7c008cdaf803368df9ee50e274c7a9f9369344d9918e0c08dba9", "result": "valid", "flags": []}, {"tcId": 354, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "303c021c6239877430e268f1a3ada2c90357247c6ca6687f49023bed0fb5b597021c355c60c09f0dacb9d74b7ccde71806c50fda8750c6ecb7abba910ac7", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044408e5c95e332ab6c2823a63959391d60a6d69c59eb1f7bd272206b9f5278e901fb4773aeeb2d8255ba4df3cf3db7e0557dbc6134c55f3a6", "wx": "4408e5c95e332ab6c2823a63959391d60a6d69c59eb1f7bd272206b9", "wy": "00f5278e901fb4773aeeb2d8255ba4df3cf3db7e0557dbc6134c55f3a6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044408e5c95e332ab6c2823a63959391d60a6d69c59eb1f7bd272206b9f5278e901fb4773aeeb2d8255ba4df3cf3db7e0557dbc6134c55f3a6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAERAjlyV4zKrbCgjpjlZOR1gptacWesfe9\nJyIGufUnjpAftHc67rLYJVuk3zzz234FV9vGE0xV86Y=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "valid", "flags": []}, {"tcId": 356, "comment": "r too large", "msg": "313233343030", "sig": "303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04315a83008dba00b351c3f9fca0811c3ae1884fa9a2a75e6d5e71f269504bbe6a25be253b582efab4b8b9e61372767a7a3a423c0943127296", "wx": "315a83008dba00b351c3f9fca0811c3ae1884fa9a2a75e6d5e71f269", "wy": "504bbe6a25be253b582efab4b8b9e61372767a7a3a423c0943127296"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004315a83008dba00b351c3f9fca0811c3ae1884fa9a2a75e6d5e71f269504bbe6a25be253b582efab4b8b9e61372767a7a3a423c0943127296", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEMVqDAI26ALNRw/n8oIEcOuGIT6mip15t\nXnHyaVBLvmolviU7WC76tLi55hNydnp6OkI8CUMScpY=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "r,s are large", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042f6983b6e9f8ef96c2d981f69be54b06591ed73fe40c8a546b936a7971bf57726c26c811d7625a9d851951c1fffe236b0eb3b896bc4c98ef", "wx": "2f6983b6e9f8ef96c2d981f69be54b06591ed73fe40c8a546b936a79", "wy": "71bf57726c26c811d7625a9d851951c1fffe236b0eb3b896bc4c98ef"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042f6983b6e9f8ef96c2d981f69be54b06591ed73fe40c8a546b936a7971bf57726c26c811d7625a9d851951c1fffe236b0eb3b896bc4c98ef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEL2mDtun475bC2YH2m+VLBlke1z/kDIpU\na5NqeXG/V3JsJsgR12JanYUZUcH//iNrDrO4lrxMmO8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d1f515971cc9391153569c2befa1f915e2931110757760ebd7e61f8641c3db8beea20b13205389dcc4ba8a6af4d6da2604cacd7184ec9dbc", "wx": "00d1f515971cc9391153569c2befa1f915e2931110757760ebd7e61f86", "wy": "41c3db8beea20b13205389dcc4ba8a6af4d6da2604cacd7184ec9dbc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d1f515971cc9391153569c2befa1f915e2931110757760ebd7e61f8641c3db8beea20b13205389dcc4ba8a6af4d6da2604cacd7184ec9dbc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0fUVlxzJORFTVpwr76H5FeKTERB1d2Dr\n1+YfhkHD24vuogsTIFOJ3MS6imr01tomBMrNcYTsnbw=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e8f90a717714f0158d9521f18c14ae8c83bf1eeba115c46cbdabb20b66f50ac13461c02da02edfe4296a1f543dde7b4359f905e04193d3cf", "wx": "00e8f90a717714f0158d9521f18c14ae8c83bf1eeba115c46cbdabb20b", "wy": "66f50ac13461c02da02edfe4296a1f543dde7b4359f905e04193d3cf"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e8f90a717714f0158d9521f18c14ae8c83bf1eeba115c46cbdabb20b66f50ac13461c02da02edfe4296a1f543dde7b4359f905e04193d3cf", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6PkKcXcU8BWNlSHxjBSujIO/HuuhFcRs\nvauyC2b1CsE0YcAtoC7f5ClqH1Q93ntDWfkF4EGT088=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04723bc0c9b7ce6ea784ec075036cede90452c76576bd8fb5be4dc0fb1cf405820d92f48552b551c7b11f49406dc892fd659971ae7f9e74b59", "wx": "723bc0c9b7ce6ea784ec075036cede90452c76576bd8fb5be4dc0fb1", "wy": "00cf405820d92f48552b551c7b11f49406dc892fd659971ae7f9e74b59"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004723bc0c9b7ce6ea784ec075036cede90452c76576bd8fb5be4dc0fb1cf405820d92f48552b551c7b11f49406dc892fd659971ae7f9e74b59", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEcjvAybfObqeE7AdQNs7ekEUsdldr2Ptb\n5NwPsc9AWCDZL0hVK1UcexH0lAbciS/WWZca5/nnS1k=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a0dcce127084f955a4e49a7c86b9b91b05ae7afd6eb07225a6541d88f10a1d4fef93934967bb6c5d8792bbd47ab3abb406899a00b1c91b4a", "wx": "00a0dcce127084f955a4e49a7c86b9b91b05ae7afd6eb07225a6541d88", "wy": "00f10a1d4fef93934967bb6c5d8792bbd47ab3abb406899a00b1c91b4a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a0dcce127084f955a4e49a7c86b9b91b05ae7afd6eb07225a6541d88f10a1d4fef93934967bb6c5d8792bbd47ab3abb406899a00b1c91b4a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEoNzOEnCE+VWk5Jp8hrm5GwWuev1usHIl\nplQdiPEKHU/vk5NJZ7tsXYeSu9R6s6u0BomaALHJG0o=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020104", "result": "valid", "flags": []}, {"tcId": 363, "comment": "r is larger than n", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e10abc9fe15bcc63f009e161aaee26602415bcb45bc6c99ce7ab2b10fbebff4e4de0dfaaf04594dd603cee80b5d9ab78b6707608a95e574d", "wx": "00e10abc9fe15bcc63f009e161aaee26602415bcb45bc6c99ce7ab2b10", "wy": "00fbebff4e4de0dfaaf04594dd603cee80b5d9ab78b6707608a95e574d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e10abc9fe15bcc63f009e161aaee26602415bcb45bc6c99ce7ab2b10fbebff4e4de0dfaaf04594dd603cee80b5d9ab78b6707608a95e574d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4Qq8n+FbzGPwCeFhqu4mYCQVvLRbxsmc\n56srEPvr/05N4N+q8EWU3WA87oC12at4tnB2CKleV00=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "s is larger than n", "msg": "313233343030", "sig": "3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04fbfabe6c640856ae5dcdc9e4b706fb3db23ddca46b80b9057ab9e44f6b62d4697977ffe19bf3185083b1ede2161aa5725401a8f57851fc82", "wx": "00fbfabe6c640856ae5dcdc9e4b706fb3db23ddca46b80b9057ab9e44f", "wy": "6b62d4697977ffe19bf3185083b1ede2161aa5725401a8f57851fc82"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004fbfabe6c640856ae5dcdc9e4b706fb3db23ddca46b80b9057ab9e44f6b62d4697977ffe19bf3185083b1ede2161aa5725401a8f57851fc82", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE+/q+bGQIVq5dzcnktwb7PbI93KRrgLkF\nernkT2ti1Gl5d//hm/MYUIOx7eIWGqVyVAGo9XhR/II=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0491a85b3c5e90b409f6b8d3bca9117a54a40f4162b388bb9367fd6439f1cedf20ab52eb7154b7ea1f2934a9c8292906e18a0e572002cd2f7c", "wx": "0091a85b3c5e90b409f6b8d3bca9117a54a40f4162b388bb9367fd6439", "wy": "00f1cedf20ab52eb7154b7ea1f2934a9c8292906e18a0e572002cd2f7c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000491a85b3c5e90b409f6b8d3bca9117a54a40f4162b388bb9367fd6439f1cedf20ab52eb7154b7ea1f2934a9c8292906e18a0e572002cd2f7c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEkahbPF6QtAn2uNO8qRF6VKQPQWKziLuT\nZ/1kOfHO3yCrUutxVLfqHyk0qcgpKQbhig5XIALNL3w=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d1ca7a5c1aa086b2951c1ac14e005f0072fb28383973a05117f9652cce523c05ebe94991c47fecd241d0a07e86c88ab3c620eae792aba3d1", "wx": "00d1ca7a5c1aa086b2951c1ac14e005f0072fb28383973a05117f9652c", "wy": "00ce523c05ebe94991c47fecd241d0a07e86c88ab3c620eae792aba3d1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d1ca7a5c1aa086b2951c1ac14e005f0072fb28383973a05117f9652cce523c05ebe94991c47fecd241d0a07e86c88ab3c620eae792aba3d1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0cp6XBqghrKVHBrBTgBfAHL7KDg5c6BR\nF/llLM5SPAXr6UmRxH/s0kHQoH6GyIqzxiDq55Kro9E=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043565af2a481f9390e71d7642717d0427e02e5e7de8a3c0c1ffd5f33e9474547e0d54dcaae85494c74faa23394a056e41c2839638b8523b72", "wx": "3565af2a481f9390e71d7642717d0427e02e5e7de8a3c0c1ffd5f33e", "wy": "009474547e0d54dcaae85494c74faa23394a056e41c2839638b8523b72"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043565af2a481f9390e71d7642717d0427e02e5e7de8a3c0c1ffd5f33e9474547e0d54dcaae85494c74faa23394a056e41c2839638b8523b72", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAENWWvKkgfk5DnHXZCcX0EJ+AuXn3oo8DB\n/9XzPpR0VH4NVNyq6FSUx0+qIzlKBW5BwoOWOLhSO3I=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0429c694790fbd23777cfde434badcb061a326a5534264bcfe193c716c178a943f7bd4fb132565ba602358b13433a5217ac04cc035566c73f8", "wx": "29c694790fbd23777cfde434badcb061a326a5534264bcfe193c716c", "wy": "178a943f7bd4fb132565ba602358b13433a5217ac04cc035566c73f8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000429c694790fbd23777cfde434badcb061a326a5534264bcfe193c716c178a943f7bd4fb132565ba602358b13433a5217ac04cc035566c73f8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEKcaUeQ+9I3d8/eQ0utywYaMmpVNCZLz+\nGTxxbBeKlD971PsTJWW6YCNYsTQzpSF6wEzANVZsc/g=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048fd43aac8556f4665fd4c13f4e151140f42a395763c5da247a398f979687d24a9fcd6b20a59451c348a6364d0ffaf0ecfe164313db6594ab", "wx": "008fd43aac8556f4665fd4c13f4e151140f42a395763c5da247a398f97", "wy": "009687d24a9fcd6b20a59451c348a6364d0ffaf0ecfe164313db6594ab"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048fd43aac8556f4665fd4c13f4e151140f42a395763c5da247a398f979687d24a9fcd6b20a59451c348a6364d0ffaf0ecfe164313db6594ab", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEj9Q6rIVW9GZf1ME/ThURQPQqOVdjxdok\nejmPl5aH0kqfzWsgpZRRw0imNk0P+vDs/hZDE9tllKs=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c2ae0e357a43f97549a725ae3704449051c96bf3633355c35b6eb7e96a84dfb6d4517d1de46b18786a506178724bf4ae4f9e418c75ab17ef", "wx": "00c2ae0e357a43f97549a725ae3704449051c96bf3633355c35b6eb7e9", "wy": "6a84dfb6d4517d1de46b18786a506178724bf4ae4f9e418c75ab17ef"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c2ae0e357a43f97549a725ae3704449051c96bf3633355c35b6eb7e96a84dfb6d4517d1de46b18786a506178724bf4ae4f9e418c75ab17ef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEwq4ONXpD+XVJpyWuNwREkFHJa/NjM1XD\nW2636WqE37bUUX0d5GsYeGpQYXhyS/SuT55BjHWrF+8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "s == 1", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101", "result": "valid", "flags": []}, {"tcId": 372, "comment": "s == 0", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04961617d9855f202fd600b584abe94a46674927cfdc6333c5be56ce7b89b4150d9ccdfbd77e7682ca862c0c3e96d89c918b7d3b7bbb92ff43", "wx": "00961617d9855f202fd600b584abe94a46674927cfdc6333c5be56ce7b", "wy": "0089b4150d9ccdfbd77e7682ca862c0c3e96d89c918b7d3b7bbb92ff43"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004961617d9855f202fd600b584abe94a46674927cfdc6333c5be56ce7b89b4150d9ccdfbd77e7682ca862c0c3e96d89c918b7d3b7bbb92ff43", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAElhYX2YVfIC/WALWEq+lKRmdJJ8/cYzPF\nvlbOe4m0FQ2czfvXfnaCyoYsDD6W2JyRi307e7uS/0M=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048db53fe4168df43ee538bc9d758b8c26fa433fb0101bcbad039585de2310dfc20835379ea406993036fd4bb0f67d14760e1eb414c32dd1f3", "wx": "008db53fe4168df43ee538bc9d758b8c26fa433fb0101bcbad039585de", "wy": "2310dfc20835379ea406993036fd4bb0f67d14760e1eb414c32dd1f3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048db53fe4168df43ee538bc9d758b8c26fa433fb0101bcbad039585de2310dfc20835379ea406993036fd4bb0f67d14760e1eb414c32dd1f3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEjbU/5BaN9D7lOLyddYuMJvpDP7AQG8ut\nA5WF3iMQ38IINTeepAaZMDb9S7D2fRR2Dh60FMMt0fM=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040b7fa61983e7a227f738847d457f3e8cf0a4085c312fb6dcec822570ee7434ce2ff3fbcc1d0960379876e9dd5bed28aad576eea233a44b0d", "wx": "0b7fa61983e7a227f738847d457f3e8cf0a4085c312fb6dcec822570", "wy": "00ee7434ce2ff3fbcc1d0960379876e9dd5bed28aad576eea233a44b0d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040b7fa61983e7a227f738847d457f3e8cf0a4085c312fb6dcec822570ee7434ce2ff3fbcc1d0960379876e9dd5bed28aad576eea233a44b0d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEC3+mGYPnoif3OIR9RX8+jPCkCFwxL7bc\n7IIlcO50NM4v8/vMHQlgN5h26d1b7Siq1XbuojOkSw0=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0455b212919cd6886b13cd7a2556430ce442e86942f1bf6e4618ae363e795c664ae960ee1106308b7dba91240ab0c3ef8beb7d0a4d7a102a7f", "wx": "55b212919cd6886b13cd7a2556430ce442e86942f1bf6e4618ae363e", "wy": "795c664ae960ee1106308b7dba91240ab0c3ef8beb7d0a4d7a102a7f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000455b212919cd6886b13cd7a2556430ce442e86942f1bf6e4618ae363e795c664ae960ee1106308b7dba91240ab0c3ef8beb7d0a4d7a102a7f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVbISkZzWiGsTzXolVkMM5ELoaULxv25G\nGK42PnlcZkrpYO4RBjCLfbqRJAqww++L630KTXoQKn8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "u1 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c0288a63ce32263f3651198dab801c896fb9308362fc40e35959e14010d00bd1c228cfb6a5faa647387804e34fa1a7f9fcc472c05ea2eeda", "wx": "00c0288a63ce32263f3651198dab801c896fb9308362fc40e35959e140", "wy": "10d00bd1c228cfb6a5faa647387804e34fa1a7f9fcc472c05ea2eeda"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c0288a63ce32263f3651198dab801c896fb9308362fc40e35959e14010d00bd1c228cfb6a5faa647387804e34fa1a7f9fcc472c05ea2eeda", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEwCiKY84yJj82URmNq4AciW+5MINi/EDj\nWVnhQBDQC9HCKM+2pfqmRzh4BONPoaf5/MRywF6i7to=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00bc07ff041506dc73a75086a4325211e696eb6b31da8ff5c2c728d38d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040c8e2cb5f6a903e1cccf3ac2d465f1d0dc3452237fd9e8a4df5d5341d044ca8ceecb54a1b951270971e5ab4eb226116c48c553499d1a4899", "wx": "0c8e2cb5f6a903e1cccf3ac2d465f1d0dc3452237fd9e8a4df5d5341", "wy": "00d044ca8ceecb54a1b951270971e5ab4eb226116c48c553499d1a4899"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040c8e2cb5f6a903e1cccf3ac2d465f1d0dc3452237fd9e8a4df5d5341d044ca8ceecb54a1b951270971e5ab4eb226116c48c553499d1a4899", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEDI4stfapA+HMzzrC1GXx0Nw0UiN/2eik\n311TQdBEyozuy1ShuVEnCXHlq06yJhFsSMVTSZ0aSJk=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "u2 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041ff6b9901784d88b25527b3702622a2734b83d8a0fed0f740bb784e20e83ee0aa82933dcdc637a3760606a04974c2dc75f12095f8fdaf003", "wx": "1ff6b9901784d88b25527b3702622a2734b83d8a0fed0f740bb784e2", "wy": "0e83ee0aa82933dcdc637a3760606a04974c2dc75f12095f8fdaf003"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041ff6b9901784d88b25527b3702622a2734b83d8a0fed0f740bb784e20e83ee0aa82933dcdc637a3760606a04974c2dc75f12095f8fdaf003", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEH/a5kBeE2IslUns3AmIqJzS4PYoP7Q90\nC7eE4g6D7gqoKTPc3GN6N2BgagSXTC3HXxIJX4/a8AM=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b21faca17b68058752d943a81f853b800562df8b2172e150953c624201c2c0f5ed3b342956cacd26f9097562d0fb0a3ddab91c5ae7e90c01", "wx": "00b21faca17b68058752d943a81f853b800562df8b2172e150953c6242", "wy": "01c2c0f5ed3b342956cacd26f9097562d0fb0a3ddab91c5ae7e90c01"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b21faca17b68058752d943a81f853b800562df8b2172e150953c624201c2c0f5ed3b342956cacd26f9097562d0fb0a3ddab91c5ae7e90c01", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsh+soXtoBYdS2UOoH4U7gAVi34shcuFQ\nlTxiQgHCwPXtOzQpVsrNJvkJdWLQ+wo92rkcWufpDAE=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c152aafea3a8612ec83a7dc9448e6600ae6a772d75ad2caf19f9390e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f49278419e4f506889b0168b1fce1f87ee5b61efa0e73c7833eeb29cb1b334f81be8f05f3b2e98d38b030cff57947b96135ec4465c5e53f3", "wx": "00f49278419e4f506889b0168b1fce1f87ee5b61efa0e73c7833eeb29c", "wy": "00b1b334f81be8f05f3b2e98d38b030cff57947b96135ec4465c5e53f3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f49278419e4f506889b0168b1fce1f87ee5b61efa0e73c7833eeb29cb1b334f81be8f05f3b2e98d38b030cff57947b96135ec4465c5e53f3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9JJ4QZ5PUGiJsBaLH84fh+5bYe+g5zx4\nM+6ynLGzNPgb6PBfOy6Y04sDDP9XlHuWE17ERlxeU/M=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c4e158ef86cc53054f1635c74e65508206048929315e097a59f1519e2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0475c6a886e22bc04b996d4a19575ce0c6686b449b6e05ef1301bd8ba233ab29f65df2d4144da2b21e90359a064765c95e325bb7e54ca28e40", "wx": "75c6a886e22bc04b996d4a19575ce0c6686b449b6e05ef1301bd8ba2", "wy": "33ab29f65df2d4144da2b21e90359a064765c95e325bb7e54ca28e40"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000475c6a886e22bc04b996d4a19575ce0c6686b449b6e05ef1301bd8ba233ab29f65df2d4144da2b21e90359a064765c95e325bb7e54ca28e40", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEdcaohuIrwEuZbUoZV1zgxmhrRJtuBe8T\nAb2LojOrKfZd8tQUTaKyHpA1mgZHZcleMlu35UyijkA=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00e2ac0b24512e84f6fb015620d689d30d14736cf00c18838753c3814f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f554014cc14f319c18f5fa6cd739249075ff35ba3b2afdab5329ef0fd2c501f25a704addbd85c0e022748956e5998d99c387fbfd343c89e0", "wx": "00f554014cc14f319c18f5fa6cd739249075ff35ba3b2afdab5329ef0f", "wy": "00d2c501f25a704addbd85c0e022748956e5998d99c387fbfd343c89e0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f554014cc14f319c18f5fa6cd739249075ff35ba3b2afdab5329ef0fd2c501f25a704addbd85c0e022748956e5998d99c387fbfd343c89e0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9VQBTMFPMZwY9fps1zkkkHX/Nbo7Kv2r\nUynvD9LFAfJacErdvYXA4CJ0iVblmY2Zw4f7/TQ8ieA=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c5221f3c2de0c6fbc07ff04150679b57f57512b814f413aebafe731", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bcfa8db704aca56feb23bd4b4049213233aa652045a0a81a2e0da64c091b359f7be7ae00a0e9777d9510f847430b5dfda878e66d4fb0d62f", "wx": "00bcfa8db704aca56feb23bd4b4049213233aa652045a0a81a2e0da64c", "wy": "091b359f7be7ae00a0e9777d9510f847430b5dfda878e66d4fb0d62f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bcfa8db704aca56feb23bd4b4049213233aa652045a0a81a2e0da64c091b359f7be7ae00a0e9777d9510f847430b5dfda878e66d4fb0d62f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvPqNtwSspW/rI71LQEkhMjOqZSBFoKga\nLg2mTAkbNZ97564AoOl3fZUQ+EdDC139qHjmbU+w1i8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c221f3c2de0c6fbc07ff041506dc71b5a312063d87beb4c30c289210f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049fd4d828ae98056be58fa69eaf9cde98ca0ed9b415d6463fa1864d9fb2a5e41f10e8789450217daafd259f204aed87b0e26100f43f7c5bad", "wx": "009fd4d828ae98056be58fa69eaf9cde98ca0ed9b415d6463fa1864d9f", "wy": "00b2a5e41f10e8789450217daafd259f204aed87b0e26100f43f7c5bad"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049fd4d828ae98056be58fa69eaf9cde98ca0ed9b415d6463fa1864d9fb2a5e41f10e8789450217daafd259f204aed87b0e26100f43f7c5bad", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEn9TYKK6YBWvlj6aer5zemMoO2bQV1kY/\noYZNn7Kl5B8Q6HiUUCF9qv0lnyBK7Yew4mEA9D98W60=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c443e785bc18df780ffe082a0db8e36b46240c7b0f7d698618512421e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046123a33969f2e036fc27885f55755d391cb0c2d3fafb0c4056c1995da03bb490047e88fe7e608912a6205b65f950a8a0a360362d3339e62c", "wx": "6123a33969f2e036fc27885f55755d391cb0c2d3fafb0c4056c1995d", "wy": "00a03bb490047e88fe7e608912a6205b65f950a8a0a360362d3339e62c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046123a33969f2e036fc27885f55755d391cb0c2d3fafb0c4056c1995da03bb490047e88fe7e608912a6205b65f950a8a0a360362d3339e62c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEYSOjOWny4Db8J4hfVXVdORywwtP6+wxA\nVsGZXaA7tJAEfoj+fmCJEqYgW2X5UKigo2A2LTM55iw=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c2de0c6fbc07ff041506dc73a74fd50136878b7e1341521b2f880b19", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a10b7aa7785b2f2791b1d4c43e127aab5669612d740b38abaa0d306ec178f216fad379ad80baa0eac57bf9a56d446d685576371b74762382", "wx": "00a10b7aa7785b2f2791b1d4c43e127aab5669612d740b38abaa0d306e", "wy": "00c178f216fad379ad80baa0eac57bf9a56d446d685576371b74762382"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a10b7aa7785b2f2791b1d4c43e127aab5669612d740b38abaa0d306ec178f216fad379ad80baa0eac57bf9a56d446d685576371b74762382", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEoQt6p3hbLyeRsdTEPhJ6q1ZpYS10Czir\nqg0wbsF48hb603mtgLqg6sV7+aVtRG1oVXY3G3R2I4I=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d009f56aa80ae2bcf689be2c11b5db7e3a28983b4a7590692edcf5f8db6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e012c23c6867e9553313d0179e9db953de7c368cdb59abe05f1c52bad352a57bb59c45159352c114eeb696ec3b79caa835ef5c2ae71ddcfa", "wx": "00e012c23c6867e9553313d0179e9db953de7c368cdb59abe05f1c52ba", "wy": "00d352a57bb59c45159352c114eeb696ec3b79caa835ef5c2ae71ddcfa"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e012c23c6867e9553313d0179e9db953de7c368cdb59abe05f1c52bad352a57bb59c45159352c114eeb696ec3b79caa835ef5c2ae71ddcfa", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4BLCPGhn6VUzE9AXnp25U958NozbWavg\nXxxSutNSpXu1nEUVk1LBFO62luw7ecqoNe9cKucd3Po=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3ead55015c579ed137c58236bb70b0a2324e79109e2ffc964262f12f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b9ccd7f0f3594954aa729bda4be883e107e7f1226465b64c2ca7105789829d787016c5c118d3ba3317a2da0a0daaf56d3004c10962333a9f", "wx": "00b9ccd7f0f3594954aa729bda4be883e107e7f1226465b64c2ca71057", "wy": "0089829d787016c5c118d3ba3317a2da0a0daaf56d3004c10962333a9f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b9ccd7f0f3594954aa729bda4be883e107e7f1226465b64c2ca7105789829d787016c5c118d3ba3317a2da0a0daaf56d3004c10962333a9f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEuczX8PNZSVSqcpvaS+iD4Qfn8SJkZbZM\nLKcQV4mCnXhwFsXBGNO6Mxei2goNqvVtMATBCWIzOp8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00de03ff820a836e39d3a8435219289444bbd22db7f7368f8411c27ee5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04321a17de024fe89c1864e128b9e0af3e6b48800a70d6e802b8b6dffeb1a8ae96911ddbdeb83948a992b1b0fe316679c64814b6a45ec56fe9", "wx": "321a17de024fe89c1864e128b9e0af3e6b48800a70d6e802b8b6dffe", "wy": "00b1a8ae96911ddbdeb83948a992b1b0fe316679c64814b6a45ec56fe9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004321a17de024fe89c1864e128b9e0af3e6b48800a70d6e802b8b6dffeb1a8ae96911ddbdeb83948a992b1b0fe316679c64814b6a45ec56fe9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEMhoX3gJP6JwYZOEoueCvPmtIgApw1ugC\nuLbf/rGorpaRHdveuDlIqZKxsP4xZnnGSBS2pF7Fb+k=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00f15605922897427b7d80ab106b4474d7fa962e970ffad666580fd5c6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0408842f19b114d16be27bb4b6971377ed6b1d0915e133a9ebf01674ee4c97738b6912ff71553c4a747c782eddd9d2a20fbeae38864d217859", "wx": "08842f19b114d16be27bb4b6971377ed6b1d0915e133a9ebf01674ee", "wy": "4c97738b6912ff71553c4a747c782eddd9d2a20fbeae38864d217859"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000408842f19b114d16be27bb4b6971377ed6b1d0915e133a9ebf01674ee4c97738b6912ff71553c4a747c782eddd9d2a20fbeae38864d217859", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAECIQvGbEU0Wvie7S2lxN37WsdCRXhM6nr\n8BZ07kyXc4tpEv9xVTxKdHx4Lt3Z0qIPvq44hk0heFk=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0484d651596fd2348f1bb5c8ae9d22c8b21c4f7509240b609abad5cc243196b67b4cfaffaf0dce25ab00bfeaa1a64821332efa6dedd87cc9e7", "wx": "0084d651596fd2348f1bb5c8ae9d22c8b21c4f7509240b609abad5cc24", "wy": "3196b67b4cfaffaf0dce25ab00bfeaa1a64821332efa6dedd87cc9e7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000484d651596fd2348f1bb5c8ae9d22c8b21c4f7509240b609abad5cc243196b67b4cfaffaf0dce25ab00bfeaa1a64821332efa6dedd87cc9e7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEhNZRWW/SNI8btciunSLIshxPdQkkC2Ca\nutXMJDGWtntM+v+vDc4lqwC/6qGmSCEzLvpt7dh8yec=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048fbe39e75bc4fd8a15e4b52e4bbebe2047d54385a7117e17a4d0b2b207abdb40824538e5787c718d6548583f523f6b5bbfa239a7f622c8a0", "wx": "008fbe39e75bc4fd8a15e4b52e4bbebe2047d54385a7117e17a4d0b2b2", "wy": "07abdb40824538e5787c718d6548583f523f6b5bbfa239a7f622c8a0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048fbe39e75bc4fd8a15e4b52e4bbebe2047d54385a7117e17a4d0b2b207abdb40824538e5787c718d6548583f523f6b5bbfa239a7f622c8a0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEj74551vE/YoV5LUuS76+IEfVQ4WnEX4X\npNCysger20CCRTjleHxxjWVIWD9SP2tbv6I5p/YiyKA=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c336b340bc99d46c2c52df5428b6a0c4eb2da76c423530f767cc7652f3ab9981bd05d2955123935a379cbb2d4361a17d19878673e1e17dcc", "wx": "00c336b340bc99d46c2c52df5428b6a0c4eb2da76c423530f767cc7652", "wy": "00f3ab9981bd05d2955123935a379cbb2d4361a17d19878673e1e17dcc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c336b340bc99d46c2c52df5428b6a0c4eb2da76c423530f767cc7652f3ab9981bd05d2955123935a379cbb2d4361a17d19878673e1e17dcc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEwzazQLyZ1GwsUt9UKLagxOstp2xCNTD3\nZ8x2UvOrmYG9BdKVUSOTWjecuy1DYaF9GYeGc+Hhfcw=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04816fdcf370827e3f7771564e1aa73ed73e62556deadad89711cef663edcda0ea42235f4c9a8c13f787351ffe5ceb32f15fc0ccb24e0a409c", "wx": "00816fdcf370827e3f7771564e1aa73ed73e62556deadad89711cef663", "wy": "00edcda0ea42235f4c9a8c13f787351ffe5ceb32f15fc0ccb24e0a409c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004816fdcf370827e3f7771564e1aa73ed73e62556deadad89711cef663edcda0ea42235f4c9a8c13f787351ffe5ceb32f15fc0ccb24e0a409c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEgW/c83CCfj93cVZOGqc+1z5iVW3q2tiX\nEc72Y+3NoOpCI19MmowT94c1H/5c6zLxX8DMsk4KQJw=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046429d2b7b07ab0d5ea352902df0efc036d7270a0a6ed39f635d04f394f7932883bc45394151324aab26ae29bbd7385fa6a42c3db84432897", "wx": "6429d2b7b07ab0d5ea352902df0efc036d7270a0a6ed39f635d04f39", "wy": "4f7932883bc45394151324aab26ae29bbd7385fa6a42c3db84432897"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046429d2b7b07ab0d5ea352902df0efc036d7270a0a6ed39f635d04f394f7932883bc45394151324aab26ae29bbd7385fa6a42c3db84432897", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEZCnSt7B6sNXqNSkC3w78A21ycKCm7Tn2\nNdBPOU95Mog7xFOUFRMkqrJq4pu9c4X6akLD24RDKJc=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04288f38fd77dd1603ff0275cb11cba280ae3408affa6a760f396f1a1ec84ca6fd772c6ac6cc523cc72c2e7e95eb6a36a66b5cca5a58ba078a", "wx": "288f38fd77dd1603ff0275cb11cba280ae3408affa6a760f396f1a1e", "wy": "00c84ca6fd772c6ac6cc523cc72c2e7e95eb6a36a66b5cca5a58ba078a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004288f38fd77dd1603ff0275cb11cba280ae3408affa6a760f396f1a1ec84ca6fd772c6ac6cc523cc72c2e7e95eb6a36a66b5cca5a58ba078a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEKI84/XfdFgP/AnXLEcuigK40CK/6anYP\nOW8aHshMpv13LGrGzFI8xywufpXrajama1zKWli6B4o=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c769c138f9d71ffff113273b71a4afde4f9996a1c4be658a3903cf7f430e512b868b37bb96bc17a09b0ab01b262f2e23f34f00418f6b63d6", "wx": "00c769c138f9d71ffff113273b71a4afde4f9996a1c4be658a3903cf7f", "wy": "430e512b868b37bb96bc17a09b0ab01b262f2e23f34f00418f6b63d6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c769c138f9d71ffff113273b71a4afde4f9996a1c4be658a3903cf7f430e512b868b37bb96bc17a09b0ab01b262f2e23f34f00418f6b63d6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEx2nBOPnXH//xEyc7caSv3k+ZlqHEvmWK\nOQPPf0MOUSuGize7lrwXoJsKsBsmLy4j808AQY9rY9Y=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0475f007c11b93e6f46e9a815cb765990a8305d3ad8d22c76fe6b257cc71b5c1951b5d464c66df7c290cf0a4f156bbf52f1e41a79dc63abce5", "wx": "75f007c11b93e6f46e9a815cb765990a8305d3ad8d22c76fe6b257cc", "wy": "71b5c1951b5d464c66df7c290cf0a4f156bbf52f1e41a79dc63abce5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000475f007c11b93e6f46e9a815cb765990a8305d3ad8d22c76fe6b257cc71b5c1951b5d464c66df7c290cf0a4f156bbf52f1e41a79dc63abce5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEdfAHwRuT5vRumoFct2WZCoMF062NIsdv\n5rJXzHG1wZUbXUZMZt98KQzwpPFWu/UvHkGnncY6vOU=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041255fb94a0f20e6faa2505c394cc3c39f07def4107127dffc4dacb6eea73c1044544a1496560bd1b049ff615e68ae0d483220327569884e1", "wx": "1255fb94a0f20e6faa2505c394cc3c39f07def4107127dffc4dacb6e", "wy": "00ea73c1044544a1496560bd1b049ff615e68ae0d483220327569884e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041255fb94a0f20e6faa2505c394cc3c39f07def4107127dffc4dacb6eea73c1044544a1496560bd1b049ff615e68ae0d483220327569884e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEElX7lKDyDm+qJQXDlMw8OfB970EHEn3/\nxNrLbupzwQRFRKFJZWC9GwSf9hXmiuDUgyIDJ1aYhOE=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f656a632a0804cf688446b261208f793373c5ff4454bd1e0a882113f30a25d6f586e02dd4dcbf73d96af3e483b7acb5f8f4c06450dec1982", "wx": "00f656a632a0804cf688446b261208f793373c5ff4454bd1e0a882113f", "wy": "30a25d6f586e02dd4dcbf73d96af3e483b7acb5f8f4c06450dec1982"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f656a632a0804cf688446b261208f793373c5ff4454bd1e0a882113f30a25d6f586e02dd4dcbf73d96af3e483b7acb5f8f4c06450dec1982", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9lamMqCATPaIRGsmEgj3kzc8X/RFS9Hg\nqIIRPzCiXW9YbgLdTcv3PZavPkg7estfj0wGRQ3sGYI=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3f074d20e1da7232d279461732bc1bae0c5416ab9d696308622e7ffe8", "wx": "008fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3", "wy": "00f074d20e1da7232d279461732bc1bae0c5416ab9d696308622e7ffe8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3f074d20e1da7232d279461732bc1bae0c5416ab9d696308622e7ffe8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEj7Vy3k2vdnAmJM5O2BnQJnYiJOilQhW/\ngbICo/B00g4dpyMtJ5RhcyvBuuDFQWq51pYwhiLn/+g=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "point duplication during verification", "msg": "313233343030", "sig": "303e021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021d00ec0ce3fa725c1027475a5f5bf4ee980de61c3b4875afe8b654b24ee2", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a30f8b2df1e258dcd2d86b9e8cd43e451e3abe95462969cf79dd180019", "wx": "008fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a3", "wy": "0f8b2df1e258dcd2d86b9e8cd43e451e3abe95462969cf79dd180019"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048fb572de4daf76702624ce4ed819d026762224e8a54215bf81b202a30f8b2df1e258dcd2d86b9e8cd43e451e3abe95462969cf79dd180019", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEj7Vy3k2vdnAmJM5O2BnQJnYiJOilQhW/\ngbICow+LLfHiWNzS2GuejNQ+RR46vpVGKWnPed0YABk=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "duplication bug", "msg": "313233343030", "sig": "303e021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021d00ec0ce3fa725c1027475a5f5bf4ee980de61c3b4875afe8b654b24ee2", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e5462d3a838d4a14de96a7b0b1071eb622ae6e71ede8f95ff01c2121368e3a90d8584e194616d3211a7541f6a0960339cab28e8bfd6b1dfd", "wx": "00e5462d3a838d4a14de96a7b0b1071eb622ae6e71ede8f95ff01c2121", "wy": "368e3a90d8584e194616d3211a7541f6a0960339cab28e8bfd6b1dfd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e5462d3a838d4a14de96a7b0b1071eb622ae6e71ede8f95ff01c2121368e3a90d8584e194616d3211a7541f6a0960339cab28e8bfd6b1dfd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE5UYtOoONShTelqewsQcetiKubnHt6Plf\n8BwhITaOOpDYWE4ZRhbTIRp1QfaglgM5yrKOi/1rHf0=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045d97670c1f121f7f1ba541505609f20143b312a7bb49d376690e1831c1b4567141a7b534e21bd2f706ae034169ab9c3f8536147904de8c5f", "wx": "5d97670c1f121f7f1ba541505609f20143b312a7bb49d376690e1831", "wy": "00c1b4567141a7b534e21bd2f706ae034169ab9c3f8536147904de8c5f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045d97670c1f121f7f1ba541505609f20143b312a7bb49d376690e1831c1b4567141a7b534e21bd2f706ae034169ab9c3f8536147904de8c5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEXZdnDB8SH38bpUFQVgnyAUOzEqe7SdN2\naQ4YMcG0VnFBp7U04hvS9wauA0Fpq5w/hTYUeQTejF8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d2675278da2d7daa8373dd63b7aa46cb14766571c2d8098b83a102a5699b572d4b951497418a376930022d48fe59966b158fa08340e24b98", "wx": "00d2675278da2d7daa8373dd63b7aa46cb14766571c2d8098b83a102a5", "wy": "699b572d4b951497418a376930022d48fe59966b158fa08340e24b98"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d2675278da2d7daa8373dd63b7aa46cb14766571c2d8098b83a102a5699b572d4b951497418a376930022d48fe59966b158fa08340e24b98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0mdSeNotfaqDc91jt6pGyxR2ZXHC2AmL\ng6ECpWmbVy1LlRSXQYo3aTACLUj+WZZrFY+gg0DiS5g=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045a5cd1162388348734dae20e2235ae2c464adef0a196f9aaf02482ca2ae94e8b9a024375036429b632ab485e02c5a9665b289b8a47bade8f", "wx": "5a5cd1162388348734dae20e2235ae2c464adef0a196f9aaf02482ca", "wy": "2ae94e8b9a024375036429b632ab485e02c5a9665b289b8a47bade8f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045a5cd1162388348734dae20e2235ae2c464adef0a196f9aaf02482ca2ae94e8b9a024375036429b632ab485e02c5a9665b289b8a47bade8f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEWlzRFiOINIc02uIOIjWuLEZK3vChlvmq\n8CSCyirpTouaAkN1A2QptjKrSF4CxalmWyibike63o8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04cacd93eb11a821de3d882bab7411e7c77f23c08da174189cc987dc41716fe378ab842161bc16def6e037d4ba9d30d8cb41ad30cf0656e50b", "wx": "00cacd93eb11a821de3d882bab7411e7c77f23c08da174189cc987dc41", "wy": "716fe378ab842161bc16def6e037d4ba9d30d8cb41ad30cf0656e50b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004cacd93eb11a821de3d882bab7411e7c77f23c08da174189cc987dc41716fe378ab842161bc16def6e037d4ba9d30d8cb41ad30cf0656e50b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEys2T6xGoId49iCurdBHnx38jwI2hdBic\nyYfcQXFv43irhCFhvBbe9uA31LqdMNjLQa0wzwZW5Qs=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04cf46960060453e55577f1bee6a9c4709e7cdcba45ca8020bb3536931ea4ec33309213864a1318aee0a86d8b6f0c1b9741cd6bd5dea4f4066", "wx": "00cf46960060453e55577f1bee6a9c4709e7cdcba45ca8020bb3536931", "wy": "00ea4ec33309213864a1318aee0a86d8b6f0c1b9741cd6bd5dea4f4066"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004cf46960060453e55577f1bee6a9c4709e7cdcba45ca8020bb3536931ea4ec33309213864a1318aee0a86d8b6f0c1b9741cd6bd5dea4f4066", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEz0aWAGBFPlVXfxvuapxHCefNy6RcqAIL\ns1NpMepOwzMJIThkoTGK7gqG2Lbwwbl0HNa9XepPQGY=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0462f4eaf3797bdc3d5d8cfaa07b5af7060e131b183ca4eded4819e561bff3eadd7b55db2dc01bd20569e6c47c9212f9b2d6793795b51e4f6c", "wx": "62f4eaf3797bdc3d5d8cfaa07b5af7060e131b183ca4eded4819e561", "wy": "00bff3eadd7b55db2dc01bd20569e6c47c9212f9b2d6793795b51e4f6c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000462f4eaf3797bdc3d5d8cfaa07b5af7060e131b183ca4eded4819e561bff3eadd7b55db2dc01bd20569e6c47c9212f9b2d6793795b51e4f6c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEYvTq83l73D1djPqge1r3Bg4TGxg8pO3t\nSBnlYb/z6t17VdstwBvSBWnmxHySEvmy1nk3lbUeT2w=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c4a4bf5ae0138587f50ab7a2c336a430527a86f59f9765c2f3f5488df9419bf9df5f121de3a32db17b49c72b606b2be5ce56acb565cc12b7", "wx": "00c4a4bf5ae0138587f50ab7a2c336a430527a86f59f9765c2f3f5488d", "wy": "00f9419bf9df5f121de3a32db17b49c72b606b2be5ce56acb565cc12b7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c4a4bf5ae0138587f50ab7a2c336a430527a86f59f9765c2f3f5488df9419bf9df5f121de3a32db17b49c72b606b2be5ce56acb565cc12b7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExKS/WuAThYf1CreiwzakMFJ6hvWfl2XC\n8/VIjflBm/nfXxId46MtsXtJxytgayvlzlastWXMErc=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e7cb5ae54dbe619ab5069f14566236b3c6b0b44f1c4c531e66d89b3e64be7fdc18789629dfddf7158f8ff27abd553bfac3f7c874bccdc31b", "wx": "00e7cb5ae54dbe619ab5069f14566236b3c6b0b44f1c4c531e66d89b3e", "wy": "64be7fdc18789629dfddf7158f8ff27abd553bfac3f7c874bccdc31b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e7cb5ae54dbe619ab5069f14566236b3c6b0b44f1c4c531e66d89b3e64be7fdc18789629dfddf7158f8ff27abd553bfac3f7c874bccdc31b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE58ta5U2+YZq1Bp8UVmI2s8awtE8cTFMe\nZtibPmS+f9wYeJYp3933FY+P8nq9VTv6w/fIdLzNwxs=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0430db5d8279319cf5a3b6768a0c5e5c84752f6314f735d63f6c5650cdd32fb54f74d4a5088e6774a13201683642790d2e69e55e4f47612934", "wx": "30db5d8279319cf5a3b6768a0c5e5c84752f6314f735d63f6c5650cd", "wy": "00d32fb54f74d4a5088e6774a13201683642790d2e69e55e4f47612934"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000430db5d8279319cf5a3b6768a0c5e5c84752f6314f735d63f6c5650cdd32fb54f74d4a5088e6774a13201683642790d2e69e55e4f47612934", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEMNtdgnkxnPWjtnaKDF5chHUvYxT3NdY/\nbFZQzdMvtU901KUIjmd0oTIBaDZCeQ0uaeVeT0dhKTQ=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047db27da4d67a2de0c78815809719bdf6976332c67ef0f3827df4adc22ab37aec2eed0d5e67acfd6a195f21032d9af71ce73e120fdda29f1a", "wx": "7db27da4d67a2de0c78815809719bdf6976332c67ef0f3827df4adc2", "wy": "2ab37aec2eed0d5e67acfd6a195f21032d9af71ce73e120fdda29f1a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047db27da4d67a2de0c78815809719bdf6976332c67ef0f3827df4adc22ab37aec2eed0d5e67acfd6a195f21032d9af71ce73e120fdda29f1a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEfbJ9pNZ6LeDHiBWAlxm99pdjMsZ+8POC\nffStwiqzeuwu7Q1eZ6z9ahlfIQMtmvcc5z4SD92inxo=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d1c19d46b517bb3bd7bdf074ff975c0dbd2bde10d1ad217e58ebc8c55ac898c040a185804ddb032b48103d6c8d12043d3a4fec93aba7a6d7", "wx": "00d1c19d46b517bb3bd7bdf074ff975c0dbd2bde10d1ad217e58ebc8c5", "wy": "5ac898c040a185804ddb032b48103d6c8d12043d3a4fec93aba7a6d7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d1c19d46b517bb3bd7bdf074ff975c0dbd2bde10d1ad217e58ebc8c55ac898c040a185804ddb032b48103d6c8d12043d3a4fec93aba7a6d7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0cGdRrUXuzvXvfB0/5dcDb0r3hDRrSF+\nWOvIxVrImMBAoYWATdsDK0gQPWyNEgQ9Ok/sk6unptc=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d95ac96ae9dbfb80911862e00a4cadbcb2359f499b53be007f0711c093d3da931acbb9242800dc521695b4f19ff2dffc3613f40bdb15c3cd", "wx": "00d95ac96ae9dbfb80911862e00a4cadbcb2359f499b53be007f0711c0", "wy": "0093d3da931acbb9242800dc521695b4f19ff2dffc3613f40bdb15c3cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d95ac96ae9dbfb80911862e00a4cadbcb2359f499b53be007f0711c093d3da931acbb9242800dc521695b4f19ff2dffc3613f40bdb15c3cd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE2VrJaunb+4CRGGLgCkytvLI1n0mbU74A\nfwcRwJPT2pMay7kkKADcUhaVtPGf8t/8NhP0C9sVw80=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "extreme value for k", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIb03Y4i19yP7TCLf5s1DdaBaB0dkRNWBmYUAfjQ=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 418, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00bc07ff041506dc73a75086a4325211e696eb6b31da8ff5c2c728d38d021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIULInHdKCNwEs90gGTK8il6l+Libuyp+Znr/gc0=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 420, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00bc07ff041506dc73a75086a4325211e696eb6b31da8ff5c2c728d38d021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "wx": "4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466", "wy": "00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETCRmcGWKHUH113vOJGy+OGrCKEjiabnU\nzWfEZt3ZRxU9ObLUJTOkYN7yaIBAjK8t091I/oiM0XY=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "pseudorandom signature", "msg": "", "sig": "303e021d00f72915d6d916014279616186869a01228fcd9f1b4078353018b399ab021d00b67f2b91eeeb910381f5b461a4a39c642aea4792013d4eb63da1832b", "result": "valid", "flags": []}, {"tcId": 422, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303e021d00a5d179c336ccdc760dfddd913cdf8ea468d0f4686f7b2d3825698ed7021d00a77f12060a4d1b94b0d1c443eae3ad6e21b7eacfdf6fbf39a2b29658", "result": "valid", "flags": []}, {"tcId": 423, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "303e021d00b7c65dce56abe24fb4592ece5ac1e6ee8353431f7452409add736884021d00e5fe5db7988931026b937dc4ef983fe446ca134d29b94ac777cde317", "result": "valid", "flags": []}, {"tcId": 424, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303d021c05c563d3a4bad874e4610adfa57777a59f995bfa06ef97bf125a4988021d0097ed68f546cf4bb4998524c18356f3af162d2bf2744be93357bc4b4b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "wx": "00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf", "wy": "008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErtb8rSQAxNlOVdu2sBLOPUwrRoQ/vpnU\nKJ5uz4okqJ5xND19FR0ljSy2kDScLVazZt0QpgAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00c7a6f358b7d93815189ae5d2c3ab4d4e05f43176a52dd4fc5b48a34a021d00a2458512bb8dbe6f1bd6eb01d2d77d5624e8547bf87d85fc731c0c86", "result": "valid", "flags": []}, {"tcId": 426, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c5f56ca587d16664a20dad13df85a475978e5cee81a8d0f49faaf6158021d00b64ef59d79461fe1a09a5864907435f70bd75f183afb11903f560b7c", "result": "valid", "flags": []}, {"tcId": 427, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00dd94f5b56e9947d007e7c8efd894a5c882f1d0b5dd56c32b5b266521021d00fbc883741bd27c59958ae17ba6e4a41ad1edeca9a3ba31c8f233b5ac", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "wx": "00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1", "wy": "73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxns/kP/4on2mfR5MWFFuaf3Nwuezlqx\nISF08XPVKJSa6RQvgYut5xqWBAeWO+C2SCpqYP////8=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 428, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303e021d008071e6682c6e8a32706dc7e411503946546b31fff27dcce188ae389f021d00dc396c797d44edf794432d1da091f8c762974d8ce1f06e08ca013622", "result": "valid", "flags": []}, {"tcId": 429, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c791624e5f234b8950d509d0b456ef6fa778b19dccd609d496b62a211021c6c51e846fa53d03d42f798e6bb90954f9a48c1794b47e84ac97b460a", "result": "valid", "flags": []}, {"tcId": 430, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021b34befa1d25b756ce76b383a6e8753741c12a59266c2c7921ff6e8b021d00bc44e3823e4d807cbc92fa786a89e62a4b217b5fb0c0f1865d4a7e43", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "wx": "26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000", "wy": "00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJuWr8TXLVOqqFraeSwspInU0Toignfbf\ngAAAAOq4kd5U4/Jv9Qq5ifMz2sVRWD1GiuYjxZZDSvA=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 431, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c224a38e733ebd3fac274ecc50ecef2e7c3189be2b9d093a8dcc6fa3a021c134fa5a4f923d296b3c6dd4683d249ccf0ad272890e4149c9a0d7415", "result": "valid", "flags": []}, {"tcId": 432, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c338d07d990879ad844e24c1788e362269d8aca70500357d385768227021d00f745cc4ebaaf1cd42830026a66e5b95564cdbee5edf853bb2cc91259", "result": "valid", "flags": []}, {"tcId": 433, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c689fce4b33d8212a663640a1ae0efaa7a7d7711beba719374fe634ee021c04bd9981fa52293063076f0fd70fc31875d580ef94f020d2f95440e0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "wx": "00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff", "wy": "41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7GJ/NFVF0D+Mbb0I5XVScRZWf+N1+eyq\n/////0G/cFaX1fcWvPeHGNU5O2OphpH0ofJCRjdVOP0=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 434, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c2a4287e01510e7fb5fed2e1ccc3f2a6929cf7d03850e49d7ae8a504a021c355c3915f3fa9637dc8001438a8c04e15d14934cabd430feb0cb5ba5", "result": "valid", "flags": []}, {"tcId": 435, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00b5bf795a38adb052b401468ffcab81103d2d9fca2e15b8d08ab98ce8021c5ec0d2c6aec71888c941af324c7272bec192abb292f9df82a24e8a41", "result": "valid", "flags": []}, {"tcId": 436, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c100ed07f467133bf10917f7a15ab2bfda519bdbc2653b95955e22211021d00b38a081f7c2e2b775d1da868d0381c09ba1559c9613b5be7159363ad", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "762d28f1fdc219184f81681fbff566d465b5f1f31e872df5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWgAAAAB2LSjx/cIZGE+BaB+/9WbUZbXx8x6HLfU=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 437, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c54e6add8ac910e52c6228fe3980d8f586218334d8d859ba9a3329917021c5836cc79ec88519eab4a6b2614c501628c9fee32fbafd93e32158409", "result": "valid", "flags": []}, {"tcId": 438, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c1230d5409f379584b4d548b7bccba64baf81d512a9f2e6398c4e3a66021c1937a298f8cbdfa85b8e6fcf0a12be4966d80270cade85a0c37ee6f3", "result": "valid", "flags": []}, {"tcId": 439, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00862f43b044fb32adb45e00378ba083ae761c84452054f17b1341bf5b021d0095d8d8e5e3a6cc2b0a06c792252ca11a642257721831578520f96b9e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWv////+J0tcOAj3m57B+l99ACpkrmkoODOF40gw=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 440, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00cb5cabb1ca01b847a6bc70558d1e5d3a204d1741bbe800f4b159af35021c3580cc85f218394130bddf1c4eac04fe96f59f14fb436686950398be", "result": "valid", "flags": []}, {"tcId": 441, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00c9d83dc04cf4ee89c405045d0fd1d704f627ca5bbe350f40b826bbc1021c74fedc9e55045e9759f2124460fdfb991dc620cfee6effc0b4adaa9e", "result": "valid", "flags": []}, {"tcId": 442, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c46dd65b6e7f10c0841841b01033a5befd3a0e78c85f1f390bb3cdf25021d00f33acea3d47cf0dd5273735b004104f6512ed641052509422c0325a7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "wx": "00f7e4713d085112112c37cdf4601ff688da796016b71a727a", "wy": "00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAAAAAPfkcT0IURIRLDfN9GAf9ojaeWAW\ntxpyet5ansFlBUzJh/nch+mZG5Lk+mScplXurp8qMOE=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 443, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00ddb4a7e400a1e98118f474722da3f421f65a76eec61f4f7b699faf07021d00db80cba199859cdfe916d6ab3deb91d76aaf0ed554c8f9ed7e5aa59d", "result": "valid", "flags": []}, {"tcId": 444, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c4c260b546280604e4c80384721c9e803ef704e7fb70168e6730fc1f3021d00a8aceae219ac25c9f04231b4e0c171413db1d26df1c1e8430062eb2b", "result": "valid", "flags": []}, {"tcId": 445, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00f4098d2c0240e78fceabb0183df0b39e7ad3e7f5d6da1587fa09853c021d00d42412b2abaa614c95eb11f9b9346282ce3a1c93aac35ce7aa372f4a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "wx": "00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725", "wy": "0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/////+rffO6NNNBM8iyPfeNWdPsvUB0k\nKnb3JYbECTCdOY5gzh4KTJ4FqdMmJ1d+jOLMfzr6LD4=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 446, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c48ddc497f9a4732c677e46c0e2bdabec54fc9d27e46ab595056db4d9021d00b8219ebbfaebc2fe4311efab0c35d4392751351bcc1971e8d01941e4", "result": "valid", "flags": []}, {"tcId": 447, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00e1abaf51d27a6d7d4c9b28078325cac2d7ce3d5403916c68903760b7021c2c45a99e2770f782fee5ca1d713eaecf07e62d53c64b7cf93de9900d", "result": "valid", "flags": []}, {"tcId": 448, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00868cd127c99e1149f7fc8d878cdfa986b62e99addea281149611ff15021c16e5953820135b7d462ce5434ef85920e973eec9e4d14d7cb3cc2a3f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4QAAAAAOKrDoSV6FnrKvsAdp1uf+YmoRkWfAtrw=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 449, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00a375929718ec4e6ada9c9370c51df6bdaee7ebab2a70675d42a0b6b3021d009eaf4802efaf7ca082ffbf5ed774af43792d9b3fd711c6b1c36112ff", "result": "valid", "flags": []}, {"tcId": 450, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00d97b32f3bf8bc11ec2672dd6320418beeed99527a63fe4c52199ec61021c68dd9006b03319ccbe651d0bdaf84c63356f03cb007a6865ee3e0206", "result": "valid", "flags": []}, {"tcId": 451, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d008ee5794dc2e66f2584910ea1d8361e5b53db535adcf5c1c35e128309021c5d1d8b9b996c0a488e05af14421b86e9841f0cba706027fc827d4d95", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4f/////x1U8XtqF6YU1QT/eWKRgBnZXubpg/SUU=\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 452, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c7999727c0cc02d88ef274012a762afcbb19e7fce19091a02acd00564021d00dbfacf67999f22c499d48a60a6fe4bbb746199c29957a1ec7a0900e0", "result": "valid", "flags": []}, {"tcId": 453, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c5797c21c0162e42f69693c6c0244dfdf9218c01e9235760177b61a54021c5452c887b27fb342a8a00d27579c7195dddb73df399233ed0dea567b", "result": "valid", "flags": []}, {"tcId": 454, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c0eb9dc5d67bb0d4009544f8654977907dfe770e7fae4571d31d7b4fa021d00ab5cda53e868bff5198be4be3681b186cb0c1396d272c71f093f8b12", "result": "valid", "flags": []}]}]}