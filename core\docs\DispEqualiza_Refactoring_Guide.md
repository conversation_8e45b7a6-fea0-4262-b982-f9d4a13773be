# DispEqualiza 重构指导文档 - 共同逻辑提取

## 概述

本文档详细分析了 `DispEqualiza.cpp` 中四个处理函数的共同逻辑模块，为后续重构提供具体的实施指导。通过提取这些共同模块，可以显著提高代码的可维护性、可测试性和性能。

## 1. 核心信号处理流水线模块

### 1.1 SignalGradientGenerator 类

**功能**：统一梯度信号生成逻辑

**当前重复代码模式**：
```cpp
// handle_first_category (3次调用)
disp_gen_gradient(first_samples_int, freq_delta/samples, amp_delta/samples, 
                  avg_amplitude, sampling_rate, current_phase, 
                  mainSignalBuffer, filteredSignalBuffer);

// handle_second_category (多次调用)  
disp_gen_gradient(segment_duration_int, frequency_diff/duration, 
                  amplitude_diff/duration, normalized_amplitude, 
                  frequency, phase, mainSignalBuffer, filteredSignalBuffer);

// handle_third_category (3次调用)
disp_gen_gradient(num_samples_int, freq_diff/(samples-1.0), 0, 
                  target_amplitude, current_frequency, phase, 
                  mainSignalBuffer, filteredSignalBuffer);

// handle_multi_envelope (多次调用)
disp_gen_gradient(sample_step, freq_increment/(step-1.0), 
                  amp_change/(step-1.0), amplitude, frequency, 
                  phase, mainSignalBuffer, filteredSignalBuffer);
```

**建议重构接口**：
```cpp
class SignalGradientGenerator {
public:
    struct GradientParams {
        int32_t sample_count;
        double frequency_start;
        double frequency_end;
        double amplitude_start;
        double amplitude_end;
        double phase_offset;
    };
    
    static void generate(const GradientParams& params, 
                        double* phase_buffer, 
                        double* amplitude_buffer);
};
```

### 1.2 SignalFilter 类

**功能**：统一滤波处理和增益应用

**当前重复代码模式**：
```cpp
// 所有函数中都有的滤波模式
calc_filter(amplitude_array, processedSignalBuffer, sample_count,
            normalizationCoeffs2, normalizationCoeffs1, 3, filterStateVector);

// 增益应用模式
if (sample_count >= 1) {
    for (int32_t i = 0; i < sample_count; i++) {
        amplitude_array[i] = filterGain * processedSignalBuffer[i];
    }
}
```

**建议重构接口**：
```cpp
class SignalFilter {
private:
    double* normalizationCoeffs1;
    double* normalizationCoeffs2;
    double* filterStateVector;
    double filterGain;
    
public:
    void applyFilter(double* input_buffer, double* output_buffer, 
                    int32_t sample_count);
    void resetState();
};
```

### 1.3 AmplitudeLimiter 类

**功能**：统一振幅限制逻辑

**当前重复代码模式**：
```cpp
// 在所有函数中都有的振幅限制模式
int32_t freq_response = int32_t(current_frequency + 0.5) - 50;
double amplitude_limit = motor->frequency_amplitude_limits[freq_response];
if (current_amplitude > amplitude_limit) {
    signal_buffer[i] *= (amplitude_limit / current_amplitude);
}
```

**建议重构接口**：
```cpp
class AmplitudeLimiter {
private:
    LinearMotorPhysicalModel* motor;
    
public:
    void limitAmplitude(double* signal_buffer, int32_t sample_count,
                       double current_frequency, double current_amplitude);
    void limitAmplitudeRange(double* signal_buffer, int32_t sample_count,
                            double start_freq, double end_freq,
                            double start_amp, double end_amp);
};
```

## 2. 包络处理模块

### 2.1 EnvelopeProcessor 类

**功能**：统一各种包络处理算法

**当前重复代码模式**：
```cpp
// 指数衰减包络 (handle_first_category, handle_multi_envelope)
double normalized_position = double(sample_index - total_samples) / total_samples;
double exponential_factor = std::exp(-double(sample_index) / total_samples);
filteredSignalBuffer[sample_index] *= (normalized_position * exponential_factor + 1.0);

// 余弦调制包络 (handle_second_category, handle_third_category)
double phase_ratio = (current_time - end_time) / duration;
double decay_factor = exp(-offset / duration);
double envelope = cos(phase_ratio * decay_factor * R_PI + R_PI);
double scaled_envelope = (envelope + 1.0) * 0.5;
signal_buffer[i] *= scaled_envelope;

// 复合包络 (handle_third_category)
double cos_factor = cos(R_PI - inverse_sample_count / samples * exp_factor * R_PI);
filteredSignalBuffer[offset] *= (cos_factor + 1.0) * 0.5;
```

**建议重构接口**：
```cpp
class EnvelopeProcessor {
public:
    enum EnvelopeType {
        EXPONENTIAL_DECAY,
        COSINE_MODULATION,
        COMPOSITE_ENVELOPE,
        LINEAR_FADE
    };
    
    struct EnvelopeParams {
        EnvelopeType type;
        double duration;
        double start_time;
        double end_time;
        double decay_factor;
    };
    
    static void applyEnvelope(double* signal_buffer, int32_t sample_count,
                             const EnvelopeParams& params);
};
```

## 3. 信号分析模块

### 3.1 SignalAnalyzer 类

**功能**：峰值检测和信号质量评估

**当前重复代码模式**：
```cpp
// 峰值检测 (handle_third_category, handle_multi_envelope)
int32_t peak_index = findfirstpeakidx(&filteredSignalBuffer[10], 
                                     segment_samples_int - 10);

// 信号质量评估 (handle_second_category, handle_multi_envelope)
int32_t high_amplitude_count = 0;
double sum_squares = 0.0;
for (int32_t i = 0; i < sample_count; i++) {
    double abs_sample = fabs(signal_buffer[i]);
    if (abs_sample > 8.0) {
        sum_squares += signal_buffer[i] * signal_buffer[i];
        high_amplitude_count++;
    }
}
bool use_prebaked = high_amplitude_count && 
                   sqrt(sum_squares / high_amplitude_count) > 9.0;
```

**建议重构接口**：
```cpp
class SignalAnalyzer {
public:
    struct QualityMetrics {
        int32_t high_amplitude_count;
        double rms_value;
        double peak_amplitude;
        int32_t peak_index;
        bool use_prebaked_signal;
    };
    
    static QualityMetrics analyzeSignal(const double* signal_buffer, 
                                       int32_t sample_count,
                                       double amplitude_threshold = 8.0,
                                       double quality_threshold = 9.0);
    
    static int32_t findFirstPeak(const double* signal_buffer, 
                                int32_t sample_count,
                                int32_t start_offset = 10);
};
```

## 4. 时间分段管理模块

### 4.1 TimeSegmentManager 类

**功能**：统一时间分段计算和管理

**当前重复代码模式**：
```cpp
// 分段计算模式 (所有函数)
double segment_start = base_time + ((segment_index - 1) * interval);
double segment_duration = fmin(max_duration, total_time - segment_start);
double segment_end = segment_start + segment_duration;
int32_t segment_samples = (int32_t)(segment_duration + 1.0);

// 时间阈值计算
double time_threshold = 3000.0 / actual_frequency + 16.0;
double transition_time = fmax(750.0 / freq1, 750.0 / freq2);
double steady_state_time = 1500.0 / real_frequency;
```

**建议重构接口**：
```cpp
class TimeSegmentManager {
public:
    struct SegmentInfo {
        double start_time;
        double end_time;
        double duration;
        int32_t sample_count;
        bool is_last_segment;
    };
    
    struct TimeThresholds {
        double transition_time;
        double steady_state_time;
        double fade_time;
    };
    
    static TimeThresholds calculateThresholds(double frequency, 
                                            double real_frequency);
    
    static std::vector<SegmentInfo> createSegments(double total_time,
                                                  double interval,
                                                  double samples_per_ms);
};
```

## 5. 物理模型计算模块

### 5.1 PhysicalModelCalculator 类

**功能**：封装线性马达物理模型计算

**当前重复代码模式**：
```cpp
// 频率范围检查和振幅计算 (handle_multi_envelope, handle_third_category)
if (frequencyLowerLimit <= frequency && frequency <= frequencyUpperLimit) {
    amplitude_scale = motor->max_amplitude;
} else {
    double angular_frequency = frequency * 2 * R_PI;
    double term1 = springConstant - angular_frequency * angular_frequency * motor->mass;
    double term2 = angular_frequency * dampingCoefficient;
    double denominator = term1 * term1 + term2 * term2;
    amplitude_scale = sqrt(/* 复杂计算 */) * 8.0;
}
```

**建议重构接口**：
```cpp
class PhysicalModelCalculator {
private:
    LinearMotorPhysicalModel* motor;
    double frequencyLowerLimit;
    double frequencyUpperLimit;
    double springConstant;
    double dampingCoefficient;
    
public:
    double calculateAmplitudeScale(double frequency);
    bool isFrequencyInRange(double frequency);
    double calculateResonanceResponse(double frequency);
};
```

## 6. 重构实施计划

### 阶段1：核心模块提取 (Week 1-2)
1. 提取 `SignalGradientGenerator`
2. 提取 `SignalFilter`  
3. 提取 `AmplitudeLimiter`
4. 创建基础测试用例

### 阶段2：高级功能整合 (Week 3-4)
1. 提取 `EnvelopeProcessor`
2. 提取 `SignalAnalyzer`
3. 重构现有函数使用新模块
4. 性能测试和优化

### 阶段3：系统完善 (Week 5-6)
1. 提取 `TimeSegmentManager`
2. 提取 `PhysicalModelCalculator`
3. 完善错误处理和日志
4. 全面测试和文档更新

## 7. 预期收益

### 代码质量提升
- 减少重复代码约60%
- 提高函数可读性
- 统一错误处理机制

### 维护性改善
- 模块化设计便于单独测试
- 统一接口降低耦合度
- 便于功能扩展和优化

### 性能优化
- 统一的缓冲区管理
- 优化的算法实现
- 减少内存分配开销

## 8. 详细代码重复分析表

### 8.1 disp_gen_gradient 调用统计

| 函数名 | 调用次数 | 行号范围 | 参数模式 |
|--------|----------|----------|----------|
| handle_first_category | 3次 | 985-989, 1066-1073, 1106-1113 | 三阶段处理模式 |
| handle_second_category | 6次+ | 1247-1252, 1294-1299, 1386-1391, 1464-1469 | 分段迭代模式 |
| handle_third_category | 3次 | 1646-1651, 1729-1732, 1969-1974 | 复杂过渡模式 |
| handle_multi_envelope | 10次+ | 2959-2966, 3155-3162, 3270-3277 | 多点插值模式 |

**重复度**：95% - 几乎所有调用都遵循相同的参数计算模式

### 8.2 calc_filter 调用统计

| 函数名 | 调用次数 | 行号范围 | 处理模式 |
|--------|----------|----------|----------|
| handle_first_category | 3次 | 1006-1007, 1078-1079, 1132-1133 | 分阶段滤波 |
| handle_second_category | 6次+ | 1264-1265, 1311-1312, 1395-1396, 1520-1521 | 分段滤波 |
| handle_third_category | 7次+ | 1655-1656, 1775-1776, 1786-1788, 1894-1895 | 复杂滤波链 |
| handle_multi_envelope | 8次+ | 2977-2978, 3166-3167, 3315-3316, 3326-3331 | 多级滤波 |

**重复度**：90% - 滤波参数和后处理逻辑高度一致

### 8.3 振幅限制代码重复

| 函数名 | 重复片段数 | 典型代码模式 |
|--------|------------|--------------|
| handle_first_category | 3处 | `freq_response = int32_t(freq + 0.5) - 50; limit = motor->frequency_amplitude_limits[freq_response]` |
| handle_second_category | 4处 | 相同模式 + 循环应用 |
| handle_third_category | 3处 | 相同模式 + 条件检查 |
| handle_multi_envelope | 5处 | 相同模式 + 复杂索引计算 |

**重复度**：100% - 完全相同的计算逻辑

### 8.4 包络处理重复模式

| 包络类型 | 出现函数 | 代码相似度 | 典型实现 |
|----------|----------|------------|----------|
| 指数衰减 | first_category, multi_envelope | 85% | `exp(-sample_index / total_samples)` |
| 余弦调制 | second_category, third_category | 90% | `cos(phase_ratio * decay_factor * R_PI + R_PI)` |
| 复合包络 | third_category, multi_envelope | 75% | 多种函数组合 |

### 8.5 信号传输 deliver 调用

| 函数名 | 调用次数 | 错误处理 | 返回值处理 |
|--------|----------|----------|------------|
| handle_first_category | 1次 | 标准模式 | return 0 |
| handle_second_category | 3次 | 标准模式 | return 0 |
| handle_third_category | 3次 | 标准模式 | return 0 |
| handle_multi_envelope | 5次+ | 标准模式 | goto LABEL_146 |

**重复度**：95% - 错误处理逻辑完全一致

### 8.6 峰值检测重复

| 函数名 | findfirstpeakidx调用 | 参数模式 | 后处理逻辑 |
|--------|---------------------|----------|------------|
| handle_third_category | 1次 | `&filteredSignalBuffer[10], peak_search_start` | 复杂衰减处理 |
| handle_multi_envelope | 1次 | `&filteredSignalBuffer[10], temp_voffset_first_peak` | 相似衰减处理 |

**重复度**：80% - 调用模式和后处理逻辑高度相似

### 8.7 缓冲区操作重复

| 操作类型 | 重复次数 | 函数分布 | 标准化程度 |
|----------|----------|----------|------------|
| memcpy | 15次+ | 所有函数 | 高 |
| memset | 10次+ | 所有函数 | 高 |
| 滤波器状态重置 | 8次 | 所有函数 | 完全一致 |

### 8.8 重构优先级矩阵

| 模块 | 重复度 | 复杂度 | 影响范围 | 重构优先级 |
|------|--------|--------|----------|------------|
| SignalGradientGenerator | 95% | 中 | 全部函数 | 🔴 极高 |
| AmplitudeLimiter | 100% | 低 | 全部函数 | 🔴 极高 |
| SignalFilter | 90% | 中 | 全部函数 | 🟡 高 |
| EnvelopeProcessor | 85% | 高 | 3个函数 | 🟡 高 |
| SignalAnalyzer | 80% | 中 | 2个函数 | 🟢 中 |
| TimeSegmentManager | 70% | 高 | 全部函数 | 🟢 中 |
| PhysicalModelCalculator | 60% | 高 | 2个函数 | 🔵 低 |

## 9. 重构实施检查清单

### 阶段1检查项
- [ ] SignalGradientGenerator 类实现
- [ ] AmplitudeLimiter 类实现
- [ ] 单元测试覆盖率 > 90%
- [ ] 性能回归测试通过
- [ ] 第一个函数重构完成

### 阶段2检查项
- [ ] SignalFilter 类实现
- [ ] EnvelopeProcessor 类实现
- [ ] 集成测试通过
- [ ] 内存使用优化验证
- [ ] 两个函数重构完成

### 阶段3检查项
- [ ] 所有模块实现完成
- [ ] 全部函数重构完成
- [ ] 代码覆盖率 > 95%
- [ ] 性能提升验证
- [ ] 文档更新完成

通过系统性的重构，可以将当前的四个复杂函数转换为清晰的模块化架构，显著提升代码质量和维护效率。重复代码减少预期可达60%以上，同时提供更好的可测试性和可扩展性。

## 10. Goto标签重命名优化

### 10.1 重命名原则

为了提高代码可读性和维护性，我们对四个处理函数中的goto标签进行了语义化重命名，遵循以下原则：

1. **语义明确**：标签名称应清楚表达代码逻辑意图
2. **命名规范**：使用大写字母和下划线，符合Google C++规范
3. **逻辑分组**：相同功能的标签使用相似的命名模式
4. **避免数字**：不使用无意义的数字编号

### 10.2 标签重命名映射表

#### handle_second_category 函数
| 原标签名 | 新标签名 | 逻辑含义 | 代码位置 |
|----------|----------|----------|----------|
| LABEL_52 | PROCESS_SECOND_STAGE | 处理第二阶段信号生成 | 第二段处理开始 |
| LABEL_71 | PROCESS_THIRD_STAGE | 处理第三阶段信号生成 | 第三段处理开始 |
| LABEL_82 | APPLY_FILTER_AND_GAIN | 应用滤波器和增益处理 | 滤波和振幅处理 |

#### handle_third_category 函数
| 原标签名 | 新标签名 | 逻辑含义 | 代码位置 |
|----------|----------|----------|----------|
| LABEL_50 | START_SEGMENT_PROCESSING | 开始分段处理逻辑 | 分段处理入口 |
| LABEL_69 | PROCESS_FINAL_SEGMENT | 处理最终分段 | 最后分段处理 |
| LABEL_30 | CHECK_SEGMENT_QUALITY | 检查分段质量 | 分段质量评估 |

#### handle_multi_envelope 函数
| 原标签名 | 新标签名 | 逻辑含义 | 代码位置 |
|----------|----------|----------|----------|
| LABEL_70 | START_ENVELOPE_PROCESSING | 开始包络处理 | 包络处理入口 |
| LABEL_64 | SET_SIGNAL_FLAGS | 设置信号处理标志 | 信号标志配置 |
| LABEL_65 | DELIVER_SIGNAL | 执行信号传输 | 信号传输处理 |
| LABEL_146 | CLEANUP_AND_EXIT | 清理资源并退出 | 错误处理和清理 |
| LABEL_73 | CONTINUE_LOOP | 继续循环处理 | 循环控制逻辑 |

### 10.3 重命名效果分析

#### 代码可读性提升
- **语义清晰**：新标签名称直接表达代码意图，无需查看实现即可理解逻辑流程
- **维护友好**：开发者可以快速定位特定功能模块，提高调试效率
- **文档化**：标签名称本身就是代码文档，减少额外注释需求

#### 重构准备
- **模块识别**：清晰的标签名称有助于识别可提取的功能模块
- **依赖分析**：通过标签跳转可以更好地理解函数内部的控制流依赖
- **测试设计**：每个标签代表一个逻辑分支，便于设计针对性测试用例

### 10.4 后续重构建议

基于重命名后的标签分析，建议的重构优先级：

1. **高优先级重构目标**：
   - `APPLY_FILTER_AND_GAIN`：滤波和增益处理逻辑高度重复，应优先提取
   - `CLEANUP_AND_EXIT`：错误处理和资源清理逻辑标准化
   - `DELIVER_SIGNAL`：信号传输逻辑统一化

2. **中优先级重构目标**：
   - `START_SEGMENT_PROCESSING`：分段处理逻辑模块化
   - `START_ENVELOPE_PROCESSING`：包络处理逻辑抽象化

3. **低优先级重构目标**：
   - `PROCESS_SECOND_STAGE`、`PROCESS_THIRD_STAGE`：阶段处理逻辑优化
   - `SET_SIGNAL_FLAGS`：信号标志管理简化

### 10.5 重构实施指导

#### 消除goto的策略
1. **状态机模式**：将goto跳转转换为状态机状态转换
2. **函数分解**：将标签后的代码块提取为独立函数
3. **RAII模式**：使用RAII确保资源清理，消除cleanup类型的goto
4. **异常处理**：用异常机制替代错误处理的goto跳转

#### 重构验证方法
1. **单元测试**：为每个标签对应的逻辑块编写测试
2. **集成测试**：验证重构后的控制流正确性
3. **性能测试**：确保重构不影响性能要求
4. **代码审查**：确保新代码符合编程规范

### 10.6 重命名后的逻辑流程图

```mermaid
flowchart TD
    A[DispEqualiza算法入口] --> B{时间阈值判断}

    B -->|时间 < 15ms| C[handle_first_category]
    B -->|15ms ≤ 时间 < 阈值| D[handle_second_category]
    B -->|时间 ≥ 阈值| E[handle_third_category]
    B -->|多包络模式| F[handle_multi_envelope]

    %% handle_second_category 流程
    D --> D1[第一阶段处理]
    D1 --> D2{是否最后迭代}
    D2 -->|是| D3[PROCESS_SECOND_STAGE<br/>第二阶段处理]
    D2 -->|否| D1
    D3 --> D4[第二阶段信号生成]
    D4 --> D5{是否最后迭代}
    D5 -->|是| D6[PROCESS_THIRD_STAGE<br/>第三阶段处理]
    D5 -->|否| D4
    D6 --> D7[第三阶段信号生成]
    D7 --> D8{条件检查}
    D8 -->|跳过| D9[APPLY_FILTER_AND_GAIN<br/>滤波和增益处理]
    D8 -->|继续| D7
    D9 --> D10[应用滤波器]
    D10 --> D11[应用增益]
    D11 --> D12[信号传输]

    %% handle_third_category 流程
    E --> E1{分段数检查}
    E1 -->|< 1| E2[START_SEGMENT_PROCESSING<br/>开始分段处理]
    E1 -->|≥ 1| E3[分段循环处理]
    E3 --> E4{是否最后分段}
    E4 -->|是| E5[PROCESS_FINAL_SEGMENT<br/>最终分段处理]
    E4 -->|否| E3
    E2 --> E6[计算分段参数]
    E5 --> E6
    E6 --> E7{当前分段检查}
    E7 -->|≠ 1| E8[CHECK_SEGMENT_QUALITY<br/>分段质量检查]
    E7 -->|= 1| E9[质量评估处理]
    E8 --> E10[分段计数处理]
    E9 --> E10
    E10 --> E11{继续条件}
    E11 -->|是| E2
    E11 -->|否| E12[完成处理]

    %% handle_multi_envelope 流程
    F --> F1{步骤数检查}
    F1 -->|< 1| F2[START_ENVELOPE_PROCESSING<br/>开始包络处理]
    F1 -->|≥ 1| F3[步骤循环处理]
    F3 --> F4[信号生成]
    F4 --> F5{序列标志检查}
    F5 -->|条件满足| F6[SET_SIGNAL_FLAGS<br/>设置信号标志]
    F5 -->|条件不满足| F7[其他处理]
    F6 --> F8[DELIVER_SIGNAL<br/>信号传输]
    F7 --> F8
    F8 --> F9[执行信号传输]
    F9 --> F10{传输成功}
    F10 -->|失败| F11[CLEANUP_AND_EXIT<br/>清理并退出]
    F10 -->|成功| F12{循环条件}
    F12 -->|继续| F13[CONTINUE_LOOP<br/>继续循环]
    F12 -->|结束| F2
    F13 --> F3
    F11 --> F14[释放资源]
    F14 --> F15[返回错误]

    %% 成功完成路径
    C --> C1[简单处理完成]
    D12 --> D13[返回成功]
    E12 --> E13[返回成功]
    F2 --> F16[返回成功]

    %% 样式定义
    classDef processStage fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef filterStage fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef segmentStage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef envelopeStage fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef errorStage fill:#ffebee,stroke:#b71c1c,stroke-width:2px

    class D3,D6 processStage
    class D9 filterStage
    class E2,E5,E8 segmentStage
    class F2,F6,F8,F13 envelopeStage
    class F11 errorStage
```

该流程图清晰展示了重命名后的goto标签在各个函数中的逻辑作用：

- **蓝色节点**：阶段处理标签（PROCESS_SECOND_STAGE, PROCESS_THIRD_STAGE）
- **紫色节点**：滤波处理标签（APPLY_FILTER_AND_GAIN）
- **绿色节点**：分段处理标签（START_SEGMENT_PROCESSING, PROCESS_FINAL_SEGMENT, CHECK_SEGMENT_QUALITY）
- **橙色节点**：包络处理标签（START_ENVELOPE_PROCESSING, SET_SIGNAL_FLAGS, DELIVER_SIGNAL, CONTINUE_LOOP）
- **红色节点**：错误处理标签（CLEANUP_AND_EXIT）

通过这种颜色编码，可以直观地看出每个函数的主要处理逻辑和goto标签的作用域。
